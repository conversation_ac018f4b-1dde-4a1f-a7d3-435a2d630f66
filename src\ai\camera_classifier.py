#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مصنف الكاميرات بالذكاء الاصطناعي - AI Camera Classifier
"""

import numpy as np
import cv2
from PIL import Image
import tensorflow as tf
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.cluster import KMeans
import pickle
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import hashlib
import threading

class CameraFeatureExtractor:
    """مستخرج الميزات من صور الكاميرات"""
    
    def __init__(self, config, logger):
        self.config = config
        self.logger = logger
        self.model = None
        self.feature_cache = {}
        self.lock = threading.Lock()
        
        # تحميل النموذج
        self.load_model()
    
    def load_model(self):
        """تحميل نموذج استخراج الميزات"""
        try:
            model_path = self.config.get_model_path()
            
            if model_path.exists():
                self.model = tf.keras.models.load_model(str(model_path))
                self.logger.info("تم تحميل نموذج الذكاء الاصطناعي")
            else:
                # إنشاء نموذج افتراضي
                self.model = self.create_default_model()
                self.logger.info("تم إنشاء نموذج افتراضي")
                
        except Exception as e:
            self.logger.error(f"خطأ في تحميل النموذج: {e}")
            self.model = self.create_default_model()
    
    def create_default_model(self):
        """إنشاء نموذج افتراضي"""
        try:
            # نموذج بسيط باستخدام MobileNetV2
            base_model = tf.keras.applications.MobileNetV2(
                input_shape=(224, 224, 3),
                include_top=False,
                weights='imagenet'
            )
            
            # تجميد الطبقات الأساسية
            base_model.trainable = False
            
            # إضافة طبقات مخصصة
            model = tf.keras.Sequential([
                base_model,
                tf.keras.layers.GlobalAveragePooling2D(),
                tf.keras.layers.Dense(512, activation='relu'),
                tf.keras.layers.Dropout(0.2),
                tf.keras.layers.Dense(256, activation='relu'),
                tf.keras.layers.Dense(128, activation='relu'),
                tf.keras.layers.Dense(64, name='feature_vector')  # طبقة الميزات
            ])
            
            model.compile(
                optimizer='adam',
                loss='categorical_crossentropy',
                metrics=['accuracy']
            )
            
            return model
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء النموذج الافتراضي: {e}")
            return None
    
    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """معالجة الصورة قبل التحليل"""
        try:
            # تحويل إلى RGB إذا كانت BGR
            if len(image.shape) == 3 and image.shape[2] == 3:
                image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # تغيير الحجم
            image = cv2.resize(image, (224, 224))
            
            # تطبيع القيم
            image = image.astype(np.float32) / 255.0
            
            # إضافة بعد الدفعة
            image = np.expand_dims(image, axis=0)
            
            return image
            
        except Exception as e:
            self.logger.error(f"خطأ في معالجة الصورة: {e}")
            return None
    
    def extract_features(self, image: np.ndarray) -> Optional[np.ndarray]:
        """استخراج الميزات من الصورة"""
        try:
            if self.model is None:
                return None
            
            # معالجة الصورة
            processed_image = self.preprocess_image(image)
            if processed_image is None:
                return None
            
            # استخراج الميزات
            with self.lock:
                features = self.model.predict(processed_image, verbose=0)
            
            # إرجاع الميزات كمصفوفة أحادية البعد
            return features.flatten()
            
        except Exception as e:
            self.logger.error(f"خطأ في استخراج الميزات: {e}")
            return None
    
    def extract_visual_features(self, image: np.ndarray) -> Dict[str, Any]:
        """استخراج الميزات البصرية التقليدية"""
        try:
            features = {}
            
            # تحويل إلى رمادي
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # الميزات الأساسية
            features['mean_intensity'] = np.mean(gray)
            features['std_intensity'] = np.std(gray)
            features['image_shape'] = image.shape[:2]
            
            # كشف الحواف
            edges = cv2.Canny(gray, 50, 150)
            features['edge_density'] = np.sum(edges > 0) / edges.size
            
            # كشف الزوايا
            corners = cv2.goodFeaturesToTrack(gray, maxCorners=100, qualityLevel=0.01, minDistance=10)
            features['corner_count'] = len(corners) if corners is not None else 0
            
            # تحليل الألوان
            colors = image.reshape(-1, 3)
            features['dominant_colors'] = self.get_dominant_colors(colors)
            
            # كشف الدوائر (للعدسات)
            circles = cv2.HoughCircles(
                gray, cv2.HOUGH_GRADIENT, dp=1, minDist=30,
                param1=50, param2=30, minRadius=10, maxRadius=100
            )
            features['circle_count'] = len(circles[0]) if circles is not None else 0
            
            # كشف المستطيلات
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            rectangles = []
            for contour in contours:
                approx = cv2.approxPolyDP(contour, 0.02 * cv2.arcLength(contour, True), True)
                if len(approx) == 4:
                    rectangles.append(approx)
            features['rectangle_count'] = len(rectangles)
            
            return features
            
        except Exception as e:
            self.logger.error(f"خطأ في استخراج الميزات البصرية: {e}")
            return {}
    
    def get_dominant_colors(self, colors: np.ndarray, k: int = 5) -> List[List[int]]:
        """الحصول على الألوان المهيمنة"""
        try:
            # استخدام K-means لتجميع الألوان
            kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
            kmeans.fit(colors)
            
            # إرجاع مراكز التجميع كألوان مهيمنة
            dominant_colors = kmeans.cluster_centers_.astype(int).tolist()
            return dominant_colors
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل الألوان: {e}")
            return []

class CameraClassifier:
    """مصنف الكاميرات الرئيسي"""
    
    def __init__(self, config, logger, database):
        self.config = config
        self.logger = logger
        self.database = database
        self.feature_extractor = CameraFeatureExtractor(config, logger)
        
        # قاعدة بيانات الميزات
        self.feature_database = {}
        self.load_feature_database()
    
    def load_feature_database(self):
        """تحميل قاعدة بيانات الميزات"""
        try:
            feature_db_path = self.config.data_dir / "features.pkl"
            
            if feature_db_path.exists():
                with open(feature_db_path, 'rb') as f:
                    self.feature_database = pickle.load(f)
                self.logger.info("تم تحميل قاعدة بيانات الميزات")
            else:
                # إنشاء قاعدة بيانات افتراضية
                self.create_default_features()
                
        except Exception as e:
            self.logger.error(f"خطأ في تحميل قاعدة بيانات الميزات: {e}")
            self.create_default_features()
    
    def create_default_features(self):
        """إنشاء قاعدة بيانات ميزات افتراضية"""
        try:
            # ميزات افتراضية للكاميرات الشائعة
            default_features = {
                "Hikvision_DS-2CD2142FWD-I": {
                    "features": np.random.rand(64),  # ميزات وهمية
                    "visual_features": {
                        "shape": "dome",
                        "color_scheme": "white_black",
                        "led_pattern": "circular"
                    }
                },
                "Dahua_IPC-HDW4431C-A": {
                    "features": np.random.rand(64),
                    "visual_features": {
                        "shape": "eyeball",
                        "color_scheme": "white",
                        "led_pattern": "ring"
                    }
                },
                "Axis_P3375-V": {
                    "features": np.random.rand(64),
                    "visual_features": {
                        "shape": "dome",
                        "color_scheme": "white",
                        "led_pattern": "none"
                    }
                }
            }
            
            self.feature_database = default_features
            self.save_feature_database()
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء قاعدة البيانات الافتراضية: {e}")
    
    def save_feature_database(self):
        """حفظ قاعدة بيانات الميزات"""
        try:
            feature_db_path = self.config.data_dir / "features.pkl"
            
            with open(feature_db_path, 'wb') as f:
                pickle.dump(self.feature_database, f)
                
            self.logger.info("تم حفظ قاعدة بيانات الميزات")
            
        except Exception as e:
            self.logger.error(f"خطأ في حفظ قاعدة بيانات الميزات: {e}")
    
    def classify_camera(self, image: np.ndarray) -> Dict[str, Any]:
        """تصنيف الكاميرا من الصورة"""
        try:
            # استخراج الميزات
            features = self.feature_extractor.extract_features(image)
            visual_features = self.feature_extractor.extract_visual_features(image)
            
            if features is None:
                return {
                    "success": False,
                    "error": "فشل في استخراج الميزات"
                }
            
            # مقارنة مع قاعدة البيانات
            best_match = self.find_best_match(features, visual_features)
            
            # تحليل إضافي
            analysis = self.analyze_camera_features(visual_features)
            
            result = {
                "success": True,
                "classification": best_match,
                "visual_analysis": analysis,
                "confidence": best_match.get("confidence", 0.0),
                "features": visual_features
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"خطأ في تصنيف الكاميرا: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def find_best_match(self, features: np.ndarray, visual_features: Dict[str, Any]) -> Dict[str, Any]:
        """البحث عن أفضل مطابقة"""
        try:
            best_similarity = 0.0
            best_match = None
            
            for camera_key, camera_data in self.feature_database.items():
                # حساب التشابه بين الميزات
                stored_features = camera_data["features"]
                similarity = cosine_similarity([features], [stored_features])[0][0]
                
                # تعديل التشابه بناءً على الميزات البصرية
                visual_bonus = self.calculate_visual_similarity(
                    visual_features, 
                    camera_data.get("visual_features", {})
                )
                
                total_similarity = (similarity * 0.7) + (visual_bonus * 0.3)
                
                if total_similarity > best_similarity:
                    best_similarity = total_similarity
                    brand, model = camera_key.split("_", 1)
                    best_match = {
                        "brand": brand,
                        "model": model,
                        "confidence": total_similarity,
                        "similarity_score": similarity,
                        "visual_score": visual_bonus
                    }
            
            # إضافة معلومات إضافية من قاعدة البيانات
            if best_match and best_similarity > self.config.get("ml.confidence_threshold", 0.7):
                camera_info = self.database.search_cameras(f"{best_match['brand']} {best_match['model']}")
                if camera_info:
                    best_match.update(camera_info[0])
            
            return best_match or {
                "brand": "غير معروف",
                "model": "غير محدد",
                "confidence": 0.0
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في البحث عن المطابقة: {e}")
            return {
                "brand": "خطأ",
                "model": "خطأ في التحليل",
                "confidence": 0.0
            }
    
    def calculate_visual_similarity(self, features1: Dict[str, Any], features2: Dict[str, Any]) -> float:
        """حساب التشابه البصري"""
        try:
            similarity = 0.0
            total_weight = 0.0
            
            # مقارنة الشكل
            if "shape" in features1 and "shape" in features2:
                if features1["shape"] == features2["shape"]:
                    similarity += 0.3
                total_weight += 0.3
            
            # مقارنة نظام الألوان
            if "color_scheme" in features1 and "color_scheme" in features2:
                if features1["color_scheme"] == features2["color_scheme"]:
                    similarity += 0.2
                total_weight += 0.2
            
            # مقارنة نمط LED
            if "led_pattern" in features1 and "led_pattern" in features2:
                if features1["led_pattern"] == features2["led_pattern"]:
                    similarity += 0.2
                total_weight += 0.2
            
            # مقارنة عدد الدوائر (العدسات)
            if "circle_count" in features1 and "circle_count" in features2:
                circle_diff = abs(features1["circle_count"] - features2.get("circle_count", 0))
                circle_similarity = max(0, 1 - (circle_diff / 5))
                similarity += circle_similarity * 0.3
                total_weight += 0.3
            
            return similarity / total_weight if total_weight > 0 else 0.0
            
        except Exception as e:
            self.logger.error(f"خطأ في حساب التشابه البصري: {e}")
            return 0.0
    
    def analyze_camera_features(self, visual_features: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل ميزات الكاميرا"""
        try:
            analysis = {}
            
            # تحليل الشكل
            if visual_features.get("circle_count", 0) > 0:
                analysis["lens_detected"] = True
                analysis["camera_type"] = "IP Camera"
            else:
                analysis["lens_detected"] = False
                analysis["camera_type"] = "Unknown"
            
            # تحليل الإضاءة
            mean_intensity = visual_features.get("mean_intensity", 0)
            if mean_intensity < 50:
                analysis["lighting"] = "Low light"
            elif mean_intensity > 200:
                analysis["lighting"] = "Bright"
            else:
                analysis["lighting"] = "Normal"
            
            # تحليل التعقيد
            edge_density = visual_features.get("edge_density", 0)
            if edge_density > 0.1:
                analysis["complexity"] = "High detail"
            elif edge_density > 0.05:
                analysis["complexity"] = "Medium detail"
            else:
                analysis["complexity"] = "Low detail"
            
            # تحليل الألوان المهيمنة
            dominant_colors = visual_features.get("dominant_colors", [])
            if dominant_colors:
                analysis["color_analysis"] = self.analyze_colors(dominant_colors)
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل الميزات: {e}")
            return {}
    
    def analyze_colors(self, colors: List[List[int]]) -> Dict[str, Any]:
        """تحليل الألوان"""
        try:
            color_analysis = {
                "primary_colors": [],
                "color_scheme": "unknown"
            }
            
            for color in colors[:3]:  # أخذ أول 3 ألوان
                r, g, b = color
                
                # تصنيف اللون
                if r > 200 and g > 200 and b > 200:
                    color_name = "أبيض"
                elif r < 50 and g < 50 and b < 50:
                    color_name = "أسود"
                elif r > g and r > b:
                    color_name = "أحمر"
                elif g > r and g > b:
                    color_name = "أخضر"
                elif b > r and b > g:
                    color_name = "أزرق"
                else:
                    color_name = "رمادي"
                
                color_analysis["primary_colors"].append({
                    "name": color_name,
                    "rgb": color
                })
            
            # تحديد نظام الألوان
            color_names = [c["name"] for c in color_analysis["primary_colors"]]
            if "أبيض" in color_names and "أسود" in color_names:
                color_analysis["color_scheme"] = "أبيض وأسود"
            elif "أبيض" in color_names:
                color_analysis["color_scheme"] = "أبيض"
            elif "أسود" in color_names:
                color_analysis["color_scheme"] = "أسود"
            else:
                color_analysis["color_scheme"] = "ملون"
            
            return color_analysis
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل الألوان: {e}")
            return {}
    
    def add_camera_to_database(self, image: np.ndarray, brand: str, model: str) -> bool:
        """إضافة كاميرا جديدة إلى قاعدة البيانات"""
        try:
            # استخراج الميزات
            features = self.feature_extractor.extract_features(image)
            visual_features = self.feature_extractor.extract_visual_features(image)
            
            if features is None:
                return False
            
            # إنشاء مفتاح الكاميرا
            camera_key = f"{brand}_{model}"
            
            # إضافة إلى قاعدة البيانات
            self.feature_database[camera_key] = {
                "features": features,
                "visual_features": visual_features,
                "added_date": datetime.now().isoformat()
            }
            
            # حفظ قاعدة البيانات
            self.save_feature_database()
            
            self.logger.info(f"تم إضافة كاميرا جديدة: {brand} {model}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في إضافة الكاميرا: {e}")
            return False
    
    def get_classification_confidence(self, result: Dict[str, Any]) -> str:
        """تحويل درجة الثقة إلى نص"""
        confidence = result.get("confidence", 0.0)
        
        if confidence >= 0.9:
            return "عالية جداً"
        elif confidence >= 0.8:
            return "عالية"
        elif confidence >= 0.7:
            return "متوسطة"
        elif confidence >= 0.5:
            return "منخفضة"
        else:
            return "منخفضة جداً"
