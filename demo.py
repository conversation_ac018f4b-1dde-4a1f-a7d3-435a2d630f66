#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض توضيحي لتطبيق كاميرا آيـدي
Camera ID Application Demo
"""

import os
import json
from datetime import datetime

def show_banner():
    """عرض البانر"""
    print("""
╔══════════════════════════════════════════════════════════════╗
║                    كاميرا آيـدي - Camera ID                     ║
║              تطبيق فحص كاميرات المراقبة                        ║
║                                                              ║
║  ✅ تم إكمال التطوير - التطبيق جاهز للاستخدام!               ║
╚══════════════════════════════════════════════════════════════╝
    """)

def check_files():
    """فحص الملفات المطلوبة"""
    print("🔍 فحص الملفات والمجلدات:")
    print("-" * 40)

    required_files = [
        'main.py',
        'src/core/config.py',
        'src/gui/main_window.py',
        'src/utils/logger.py',
        'data/cameras.json',
        'data/vulnerabilities.json',
        'data/oui.json',
        'config.yaml',
        'requirements.txt'
    ]

    required_dirs = ['data', 'models', 'logs', 'reports', 'temp']

    all_good = True

    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - مفقود")
            all_good = False

    print()
    for dir_name in required_dirs:
        if os.path.exists(dir_name):
            print(f"✅ مجلد {dir_name}/")
        else:
            print(f"❌ مجلد {dir_name}/ - مفقود")
            all_good = False

    return all_good

def show_database_stats():
    """عرض إحصائيات قاعدة البيانات"""
    print("\n📊 إحصائيات قاعدة البيانات:")
    print("-" * 40)

    try:
        # كاميرات
        with open('data/cameras.json', 'r', encoding='utf-8') as f:
            cameras = json.load(f)
        camera_count = len(cameras.get('cameras', []))
        model_count = sum(len(brand.get('models', [])) for brand in cameras.get('cameras', []))
        print(f"📹 كاميرات: {camera_count} ماركة، {model_count} موديل")

        # ثغرات
        with open('data/vulnerabilities.json', 'r', encoding='utf-8') as f:
            vulns = json.load(f)
        vuln_count = len(vulns.get('vulnerabilities', []))
        print(f"🛡️  ثغرات أمنية: {vuln_count} ثغرة")

        # عناوين MAC
        with open('data/oui.json', 'r', encoding='utf-8') as f:
            oui = json.load(f)
        oui_count = len(oui.get('oui', []))
        print(f"🔗 عناوين MAC: {oui_count} عنوان")

    except Exception as e:
        print(f"❌ خطأ في قراءة قاعدة البيانات: {e}")

def show_features():
    """عرض الميزات المتاحة"""
    print("\n🎯 الميزات المتاحة:")
    print("-" * 40)

    features = [
        "🔍 فحص بصري للكاميرات",
        "🌐 فحص شبكي تلقائي",
        "📊 كشف الثغرات الأمنية",
        "📄 إنشاء تقارير PDF",
        "💾 حفظ وتحميل النتائج",
        "🎨 واجهة عربية كاملة",
        "⚙️  إعدادات قابلة للتخصيص",
        "📝 نظام تسجيل الأحداث"
    ]

    for feature in features:
        print(f"  {feature}")

def show_run_instructions():
    """عرض تعليمات التشغيل"""
    print("\n🚀 كيفية التشغيل:")
    print("-" * 40)

    instructions = [
        "1. تأكد من تثبيت Python 3.8+",
        "2. تثبيت المكتبات: pip install -r requirements.txt",
        "3. تشغيل التطبيق: python main.py",
        "4. أو استخدم: python run_app.py (للتثبيت التلقائي)",
        "5. أو انقر على: run_camera_id.bat"
    ]

    for instruction in instructions:
        print(f"  {instruction}")

def show_sample_scan():
    """عرض مثال على نتيجة فحص"""
    print("\n📋 مثال على نتيجة فحص:")
    print("-" * 40)

    sample_result = {
        "model": "Hikvision DS-2CD2043G0-I",
        "confidence": 0.87,
        "brand": "Hikvision",
        "features": ["4MP", "IR Night Vision", "IP67"],
        "vulnerabilities": ["CVE-2017-7921"]
    }

    print(f"النموذج المكتشف: {sample_result['model']}")
    print(f"درجة الثقة: {sample_result['confidence']:.1%}")
    print(f"الشركة المصنعة: {sample_result['brand']}")
    print("المميزات:"
    for feature in sample_result['features']:
        print(f"  • {feature}")
    print("الثغرات الأمنية:"
    for vuln in sample_result['vulnerabilities']:
        print(f"  ⚠️ {vuln}")

def main():
    """الدالة الرئيسية"""
    show_banner()

    # فحص الملفات
    if not check_files():
        print("\n❌ بعض الملفات مفقودة!")
        return

    # عرض الإحصائيات
    show_database_stats()

    # عرض الميزات
    show_features()

    # عرض التعليمات
    show_run_instructions()

    # عرض مثال
    show_sample_scan()

    print(f"\n🎉 التطبيق جاهز للاستخدام! (تاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')})")
    print("\n💡 نصيحة: شغّل التطبيق الآن وجرب الميزات المختلفة!")

if __name__ == "__main__":
    main()