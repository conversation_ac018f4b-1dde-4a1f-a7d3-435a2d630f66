#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
كاميرا آيـدي - Camera ID Desktop Application
تطبيق سطح المكتب لتحديد نوع وموديل كاميرات المراقبة
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import customtkinter as ctk
from PIL import Image, ImageTk
import cv2
import threading
import os
import sys
from datetime import datetime

# Import our modules
from src.gui.main_window import CameraIDApp
from src.core.config import Config
from src.utils.logger import setup_logger

def main():
    """تشغيل التطبيق الرئيسي"""
    try:
        # إعداد التكوين
        config = Config()
        
        # إعداد نظام السجلات
        logger = setup_logger()
        logger.info("بدء تشغيل تطبيق كاميرا آيـدي")
        
        # تعيين مظهر التطبيق
        ctk.set_appearance_mode("dark")  # أو "light" أو "system"
        ctk.set_default_color_theme("blue")  # أو "green" أو "dark-blue"
        
        # إنشاء وتشغيل التطبيق
        app = CameraIDApp()
        app.run()
        
    except Exception as e:
        messagebox.showerror("خطأ", f"حدث خطأ في تشغيل التطبيق:\n{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
