#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ماسح الكاميرا - Camera Scanner
واجهة الفحص البصري للكاميرات
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import customtkinter as ctk
from PIL import Image, ImageTk
import cv2
import threading
import numpy as np
from datetime import datetime
import os

class CameraScannerFrame(ctk.CTkFrame):
    """إطار ماسح الكاميرا"""
    
    def __init__(self, parent, config):
        super().__init__(parent)
        self.config = config
        self.camera = None
        self.is_camera_running = False
        self.current_frame = None
        
        self.create_widgets()
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        
        # العنوان
        title = ctk.CTkLabel(
            self,
            text="الفحص البصري للكاميرات",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title.pack(pady=20)
        
        # إطار الكاميرا
        self.camera_frame = ctk.CTkFrame(self)
        self.camera_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # منطقة عرض الكاميرا
        self.video_label = ctk.CTkLabel(
            self.camera_frame,
            text="انقر على 'تشغيل الكاميرا' لبدء الفحص",
            width=640,
            height=480
        )
        self.video_label.pack(pady=20)
        
        # أزرار التحكم
        controls_frame = ctk.CTkFrame(self.camera_frame, fg_color="transparent")
        controls_frame.pack(pady=10)
        
        self.start_camera_btn = ctk.CTkButton(
            controls_frame,
            text="📹 تشغيل الكاميرا",
            command=self.start_camera,
            width=150
        )
        self.start_camera_btn.pack(side="left", padx=5)
        
        self.stop_camera_btn = ctk.CTkButton(
            controls_frame,
            text="⏹️ إيقاف الكاميرا",
            command=self.stop_camera,
            width=150,
            state="disabled"
        )
        self.stop_camera_btn.pack(side="left", padx=5)
        
        self.capture_btn = ctk.CTkButton(
            controls_frame,
            text="📸 التقاط صورة",
            command=self.capture_image,
            width=150,
            state="disabled"
        )
        self.capture_btn.pack(side="left", padx=5)
        
        self.load_image_btn = ctk.CTkButton(
            controls_frame,
            text="📁 تحميل صورة",
            command=self.load_image,
            width=150
        )
        self.load_image_btn.pack(side="left", padx=5)
        
        # إطار النتائج
        self.results_frame = ctk.CTkFrame(self)
        self.results_frame.pack(fill="x", padx=20, pady=10)
        
        results_title = ctk.CTkLabel(
            self.results_frame,
            text="نتائج التحليل",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        results_title.pack(pady=10)
        
        self.results_text = ctk.CTkTextbox(
            self.results_frame,
            height=150,
            font=ctk.CTkFont(size=12)
        )
        self.results_text.pack(fill="x", padx=10, pady=10)
        
        # زر التحليل
        self.analyze_btn = ctk.CTkButton(
            self.results_frame,
            text="🔍 تحليل الصورة",
            command=self.analyze_image,
            width=200,
            height=40,
            state="disabled"
        )
        self.analyze_btn.pack(pady=10)
    
    def start_camera(self):
        """تشغيل الكاميرا"""
        try:
            camera_device = self.config.get("camera.default_device", 0)
            self.camera = cv2.VideoCapture(camera_device)
            
            if not self.camera.isOpened():
                messagebox.showerror("خطأ", "لا يمكن الوصول إلى الكاميرا")
                return
            
            # تعيين دقة الكاميرا
            resolution = self.config.get("camera.resolution", [640, 480])
            self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, resolution[0])
            self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, resolution[1])
            
            self.is_camera_running = True
            self.start_camera_btn.configure(state="disabled")
            self.stop_camera_btn.configure(state="normal")
            self.capture_btn.configure(state="normal")
            
            # بدء خيط عرض الكاميرا
            self.camera_thread = threading.Thread(target=self.update_camera_feed)
            self.camera_thread.daemon = True
            self.camera_thread.start()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تشغيل الكاميرا: {str(e)}")
    
    def stop_camera(self):
        """إيقاف الكاميرا"""
        self.is_camera_running = False
        
        if self.camera:
            self.camera.release()
            self.camera = None
        
        self.start_camera_btn.configure(state="normal")
        self.stop_camera_btn.configure(state="disabled")
        self.capture_btn.configure(state="disabled")
        
        # إعادة تعيين عرض الكاميرا
        self.video_label.configure(
            image=None,
            text="انقر على 'تشغيل الكاميرا' لبدء الفحص"
        )
    
    def update_camera_feed(self):
        """تحديث عرض الكاميرا"""
        while self.is_camera_running and self.camera:
            ret, frame = self.camera.read()
            if ret:
                self.current_frame = frame.copy()
                
                # تحويل الإطار لعرضه في tkinter
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                frame_pil = Image.fromarray(frame_rgb)
                frame_pil = frame_pil.resize((640, 480), Image.Resampling.LANCZOS)
                frame_tk = ImageTk.PhotoImage(frame_pil)
                
                # تحديث العرض
                self.video_label.configure(image=frame_tk, text="")
                self.video_label.image = frame_tk
            
            # تأخير قصير
            self.after(30)
    
    def capture_image(self):
        """التقاط صورة من الكاميرا"""
        if self.current_frame is not None:
            # حفظ الصورة
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"camera_capture_{timestamp}.jpg"
            filepath = self.config.temp_dir / filename
            
            cv2.imwrite(str(filepath), self.current_frame)
            
            self.analyze_btn.configure(state="normal")
            self.results_text.insert("end", f"تم التقاط الصورة: {filename}\n")
            
            messagebox.showinfo("تم", f"تم حفظ الصورة: {filename}")
    
    def load_image(self):
        """تحميل صورة من الملف"""
        filetypes = [
            ("صور", "*.jpg *.jpeg *.png *.bmp *.tiff"),
            ("جميع الملفات", "*.*")
        ]
        
        filepath = filedialog.askopenfilename(
            title="اختر صورة للتحليل",
            filetypes=filetypes
        )
        
        if filepath:
            try:
                # تحميل وعرض الصورة
                image = cv2.imread(filepath)
                if image is not None:
                    self.current_frame = image.copy()
                    
                    # عرض الصورة
                    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                    image_pil = Image.fromarray(image_rgb)
                    image_pil = image_pil.resize((640, 480), Image.Resampling.LANCZOS)
                    image_tk = ImageTk.PhotoImage(image_pil)
                    
                    self.video_label.configure(image=image_tk, text="")
                    self.video_label.image = image_tk
                    
                    self.analyze_btn.configure(state="normal")
                    self.results_text.insert("end", f"تم تحميل الصورة: {os.path.basename(filepath)}\n")
                else:
                    messagebox.showerror("خطأ", "لا يمكن قراءة الصورة")
                    
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في تحميل الصورة: {str(e)}")
    
    def analyze_image(self):
        """تحليل الصورة المحددة"""
        if self.current_frame is None:
            messagebox.showwarning("تحذير", "لا توجد صورة للتحليل")
            return
        
        self.results_text.insert("end", "جاري تحليل الصورة...\n")
        self.analyze_btn.configure(state="disabled", text="جاري التحليل...")
        
        # تشغيل التحليل في خيط منفصل
        analysis_thread = threading.Thread(target=self.perform_analysis)
        analysis_thread.daemon = True
        analysis_thread.start()
    
    def perform_analysis(self):
        """تنفيذ عملية التحليل"""
        try:
            # محاكاة عملية التحليل (سيتم استبدالها بالنموذج الحقيقي)
            import time
            time.sleep(2)  # محاكاة وقت المعالجة
            
            # نتائج وهمية للاختبار
            results = {
                "brand": "Hikvision",
                "model": "DS-2CD2142FWD-I",
                "confidence": 0.85,
                "features": [
                    "كاميرا IP",
                    "دقة 4 ميجابكسل",
                    "رؤية ليلية",
                    "مقاومة للطقس IP67"
                ],
                "vulnerabilities": [
                    "CVE-2017-7921: ثغرة في كلمة المرور الافتراضية",
                    "يُنصح بتحديث البرنامج الثابت"
                ]
            }
            
            # عرض النتائج
            self.display_results(results)
            
        except Exception as e:
            self.after(0, lambda: messagebox.showerror("خطأ", f"خطأ في التحليل: {str(e)}"))
        finally:
            self.after(0, lambda: self.analyze_btn.configure(state="normal", text="🔍 تحليل الصورة"))
    
    def display_results(self, results):
        """عرض نتائج التحليل"""
        def update_ui():
            self.results_text.delete("1.0", "end")
            
            result_text = f"""
نتائج التحليل:
================

الماركة: {results['brand']}
الموديل: {results['model']}
درجة الثقة: {results['confidence']:.2%}

المميزات:
"""
            for feature in results['features']:
                result_text += f"• {feature}\n"
            
            result_text += "\nالثغرات الأمنية:\n"
            for vuln in results['vulnerabilities']:
                result_text += f"⚠️ {vuln}\n"
            
            result_text += f"\nوقت التحليل: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            
            self.results_text.insert("1.0", result_text)
        
        self.after(0, update_ui)
