#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لتطبيق كاميرا آيـدي
"""

import sys
import os

def test_imports():
    """اختبار الاستيراد"""
    print("اختبار الاستيراد...")

    try:
        from src.core.config import Config
        print("✓ تم استيراد Config")

        from src.utils.logger import setup_logger
        print("✓ تم استيراد logger")

        from src.gui.main_window import CameraIDApp
        print("✓ تم استيراد CameraIDApp")

        return True
    except ImportError as e:
        print(f"✗ فشل في الاستيراد: {e}")
        return False

def test_config():
    """اختبار التكوين"""
    print("\nاختبار التكوين...")

    try:
        from src.core.config import Config
        config = Config()
        print(f"✓ تم إنشاء التكوين: {config.app.app_name} v{config.app.version}")
        return True
    except Exception as e:
        print(f"✗ فشل في التكوين: {e}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    print("\nاختبار قاعدة البيانات...")

    try:
        import json

        # اختبار cameras.json
        with open('data/cameras.json', 'r', encoding='utf-8') as f:
            cameras = json.load(f)
        print(f"✓ تم تحميل {len(cameras.get('cameras', []))} ماركة كاميرا")

        # اختبار vulnerabilities.json
        with open('data/vulnerabilities.json', 'r', encoding='utf-8') as f:
            vulns = json.load(f)
        print(f"✓ تم تحميل {len(vulns.get('vulnerabilities', []))} ثغرة أمنية")

        # اختبار oui.json
        with open('data/oui.json', 'r', encoding='utf-8') as f:
            oui = json.load(f)
        print(f"✓ تم تحميل {len(oui.get('oui', []))} عنوان OUI")

        return True
    except Exception as e:
        print(f"✗ فشل في قاعدة البيانات: {e}")
        return False

def test_directories():
    """اختبار المجلدات"""
    print("\nاختبار المجلدات...")

    dirs_to_check = ['data', 'models', 'logs', 'reports', 'temp']

    for dir_name in dirs_to_check:
        if os.path.exists(dir_name):
            print(f"✓ مجلد {dir_name} موجود")
        else:
            print(f"✗ مجلد {dir_name} مفقود")
            return False

    return True

def main():
    """الدالة الرئيسية"""
    print("كاميرا آيـدي - اختبار سريع")
    print("=" * 30)

    tests = [
        test_directories,
        test_imports,
        test_config,
        test_database
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1

    print(f"\nالنتيجة: {passed}/{total} اختبار نجح")

    if passed == total:
        print("🎉 جميع الاختبارات نجحت! التطبيق جاهز للتشغيل.")
        return 0
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى إصلاح المشاكل قبل التشغيل.")
        return 1

if __name__ == "__main__":
    sys.exit(main())