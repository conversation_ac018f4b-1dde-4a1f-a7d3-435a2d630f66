#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مدير Excel - Excel Manager Test
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent))

from src.core.config import Config
from src.utils.logger import setup_logger
from src.excel.excel_manager import ExcelManager
from datetime import datetime

def test_excel_manager():
    """اختبار مدير Excel"""
    
    print("🧪 اختبار مدير Excel...")
    
    # تهيئة النظام
    config = Config()
    logger = setup_logger()
    excel_manager = ExcelManager(config, logger)
    
    print("✅ تم تهيئة مدير Excel بنجاح")
    
    # اختبار إنشاء تقرير الكاميرات
    print("\n📷 اختبار تقرير الكاميرات...")
    
    camera_data = [
        {
            "brand": "Hikvision",
            "model": "DS-2CD2142FWD-I",
            "type": "Fixed Dome",
            "resolution": "4MP",
            "confidence": 0.95,
            "created_at": datetime.now().isoformat(),
            "notes": "كاميرا عالية الجودة مع رؤية ليلية"
        },
        {
            "brand": "Dahua",
            "model": "IPC-HDW4431C-A",
            "type": "Eyeball",
            "resolution": "4MP",
            "confidence": 0.88,
            "created_at": datetime.now().isoformat(),
            "notes": "مناسبة للاستخدام الداخلي"
        },
        {
            "brand": "Axis",
            "model": "P3375-V",
            "type": "Fixed Dome",
            "resolution": "HDTV 1080p",
            "confidence": 0.92,
            "created_at": datetime.now().isoformat(),
            "notes": "كاميرا احترافية مقاومة للتخريب"
        },
        {
            "brand": "Bosch",
            "model": "NDI-5503-A",
            "type": "Fixed Dome",
            "resolution": "5MP",
            "confidence": 0.85,
            "created_at": datetime.now().isoformat(),
            "notes": "تحليل فيديو ذكي مدمج"
        },
        {
            "brand": "Sony",
            "model": "SNC-VB770",
            "type": "Box Camera",
            "resolution": "Full HD",
            "confidence": 0.78,
            "created_at": datetime.now().isoformat(),
            "notes": "كاميرا صندوقية عالية الأداء"
        }
    ]
    
    camera_report_path = "excel_files/camera_report_test.xlsx"
    success = excel_manager.create_camera_report(camera_data, camera_report_path)
    
    if success:
        print(f"✅ تم إنشاء تقرير الكاميرات: {camera_report_path}")
    else:
        print("❌ فشل في إنشاء تقرير الكاميرات")
    
    # اختبار إنشاء تقرير الشبكة
    print("\n🌐 اختبار تقرير الشبكة...")
    
    network_data = [
        {
            "ip_address": "*************",
            "mac_address": "00:11:22:33:44:55",
            "vendor": "Hikvision",
            "open_ports": [80, 554, 8000],
            "device_type": "IP Camera",
            "onvif_support": True,
            "http_banner": "Hikvision Web Server",
            "status": "نشط"
        },
        {
            "ip_address": "*************",
            "mac_address": "AA:BB:CC:DD:EE:FF",
            "vendor": "Dahua",
            "open_ports": [80, 554],
            "device_type": "IP Camera",
            "onvif_support": True,
            "http_banner": "Dahua Web Server",
            "status": "نشط"
        },
        {
            "ip_address": "*************",
            "mac_address": "11:22:33:44:55:66",
            "vendor": "Axis",
            "open_ports": [80, 443, 554],
            "device_type": "IP Camera",
            "onvif_support": True,
            "http_banner": "Axis Web Server",
            "status": "نشط"
        },
        {
            "ip_address": "*************",
            "mac_address": "77:88:99:AA:BB:CC",
            "vendor": "Bosch",
            "open_ports": [80, 443],
            "device_type": "IP Camera",
            "onvif_support": False,
            "http_banner": "Bosch Web Server",
            "status": "نشط"
        }
    ]
    
    network_report_path = "excel_files/network_report_test.xlsx"
    success = excel_manager.create_network_report(network_data, network_report_path)
    
    if success:
        print(f"✅ تم إنشاء تقرير الشبكة: {network_report_path}")
    else:
        print("❌ فشل في إنشاء تقرير الشبكة")
    
    # اختبار إنشاء تقرير الأمان
    print("\n🔒 اختبار تقرير الأمان...")
    
    security_data = [
        {
            "cve_id": "CVE-2017-7921",
            "brand": "Hikvision",
            "model": "DS-2CD2142FWD-I",
            "severity": "High",
            "description": "Authentication bypass vulnerability allowing unauthorized access",
            "solution": "Update firmware to version 5.4.5 or later",
            "published_date": "2017-04-24"
        },
        {
            "cve_id": "CVE-2019-3948",
            "brand": "Dahua",
            "model": "IPC-HDW4431C-A",
            "severity": "Medium",
            "description": "Default credentials vulnerability",
            "solution": "Change default username and password immediately",
            "published_date": "2019-07-15"
        },
        {
            "cve_id": "CVE-2018-1000600",
            "brand": "Axis",
            "model": "P3375-V",
            "severity": "Low",
            "description": "Information disclosure vulnerability",
            "solution": "Apply security patch from vendor",
            "published_date": "2018-06-20"
        }
    ]
    
    security_report_path = "excel_files/security_report_test.xlsx"
    success = excel_manager.create_security_report(security_data, security_report_path)
    
    if success:
        print(f"✅ تم إنشاء تقرير الأمان: {security_report_path}")
    else:
        print("❌ فشل في إنشاء تقرير الأمان")
    
    # اختبار إضافة الرسوم البيانية
    print("\n📊 اختبار الرسوم البيانية...")
    
    chart_data = {
        "brands": {
            "Hikvision": 45,
            "Dahua": 30,
            "Axis": 15,
            "Bosch": 8,
            "Sony": 2
        },
        "confidence_levels": {
            "عالية (>80%)": 60,
            "متوسطة (50-80%)": 25,
            "منخفضة (<50%)": 15
        },
        "timeline": {
            "2024-01": 20,
            "2024-02": 35,
            "2024-03": 50,
            "2024-04": 45,
            "2024-05": 60
        }
    }
    
    if os.path.exists(camera_report_path):
        success = excel_manager.add_charts_to_excel(camera_report_path, chart_data)
        if success:
            print("✅ تم إضافة الرسوم البيانية بنجاح")
        else:
            print("❌ فشل في إضافة الرسوم البيانية")
    
    # اختبار التحقق من صحة الملف
    print("\n🔍 اختبار التحقق من صحة الملف...")
    
    if os.path.exists(camera_report_path):
        validation_result = excel_manager.validate_excel_file(camera_report_path)
        
        if validation_result["is_valid"]:
            print("✅ الملف صحيح")
            print(f"   📊 عدد الأوراق: {len(validation_result['sheets'])}")
            print(f"   📈 إجمالي الصفوف: {validation_result['total_rows']}")
            print(f"   💾 حجم الملف: {validation_result['file_size']} بايت")
        else:
            print("❌ الملف يحتوي على أخطاء:")
            for error in validation_result["errors"]:
                print(f"   • {error}")
    
    # اختبار تحويل الصيغ
    print("\n🔄 اختبار تحويل الصيغ...")
    
    if os.path.exists(camera_report_path):
        formats = ["csv", "json", "html"]
        results = excel_manager.convert_to_formats(camera_report_path, formats)
        
        for format_type, path in results.items():
            if os.path.exists(path):
                print(f"✅ تم التحويل إلى {format_type.upper()}: {path}")
            else:
                print(f"❌ فشل في التحويل إلى {format_type.upper()}")
    
    print("\n🎉 انتهى اختبار مدير Excel!")
    print("\n📁 الملفات المُنشأة:")
    print("   • excel_files/camera_report_test.xlsx")
    print("   • excel_files/network_report_test.xlsx") 
    print("   • excel_files/security_report_test.xlsx")
    print("   • excel_files/camera_report_test.csv")
    print("   • excel_files/camera_report_test.json")
    print("   • excel_files/camera_report_test.html")

if __name__ == "__main__":
    try:
        test_excel_manager()
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
    
    input("\nاضغط Enter للخروج...")
