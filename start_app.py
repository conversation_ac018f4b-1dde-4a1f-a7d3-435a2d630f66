#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع لتطبيق كاميرا آيـدي
Quick start for Camera ID application
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import customtkinter as ctk
    from src.gui.main_window import CameraIDApp
    
    # تعيين المظهر
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
    
    # تشغيل التطبيق
    app = CameraIDApp()
    app.run()
    
except ImportError as e:
    print(f"مكتبة مفقودة: {e}")
    print("يرجى تثبيت المكتبات المطلوبة:")
    print("pip install customtkinter opencv-python pillow numpy pyyaml")
    
except Exception as e:
    print(f"خطأ: {e}")
    input("اضغط Enter للإغلاق...")
