#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التكوين - Configuration System
إدارة إعدادات التطبيق والمسارات
"""

import os
import json
import yaml
from pathlib import Path
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional, Any

@dataclass
class AppConfig:
    """إعدادات التطبيق الرئيسية"""
    app_name: str = "كاميرا آيـدي"
    version: str = "1.0.0"
    language: str = "ar"
    theme: str = "dark"
    debug_mode: bool = False
    
@dataclass
class GUIConfig:
    """إعدادات واجهة المستخدم"""
    window_width: int = 1200
    window_height: int = 800
    min_width: int = 800
    min_height: int = 600
    font_family: str = "Arial"
    font_size: int = 12
    rtl_support: bool = True
    
@dataclass
class CVConfig:
    """إعدادات الرؤية الحاسوبية"""
    model_path: str = "models/camera_detector.tflite"
    confidence_threshold: float = 0.7
    max_image_size: tuple = (1024, 1024)
    supported_formats: List[str] = None
    
    def __post_init__(self):
        if self.supported_formats is None:
            self.supported_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']

@dataclass
class NetworkConfig:
    """إعدادات فحص الشبكة"""
    onvif_timeout: int = 10
    http_timeout: int = 5
    max_concurrent_scans: int = 50
    common_ports: List[int] = None
    default_credentials: Dict[str, str] = None
    
    def __post_init__(self):
        if self.common_ports is None:
            self.common_ports = [80, 443, 554, 8080, 8000, 37777]
        if self.default_credentials is None:
            self.default_credentials = {
                "admin": "admin",
                "admin": "12345", 
                "root": "root",
                "user": "user"
            }

@dataclass
class DatabaseConfig:
    """إعدادات قاعدة البيانات"""
    camera_db_path: str = "data/cameras.json"
    vulnerability_db_path: str = "data/vulnerabilities.json"
    oui_db_path: str = "data/oui.json"
    update_interval: int = 24  # hours
    
@dataclass
class SecurityConfig:
    """إعدادات الأمان"""
    privacy_mode: bool = True
    local_only: bool = False
    data_retention_days: int = 30
    auto_check_vulnerabilities: bool = True
    allowed_scan_networks: List[str] = None
    
    def __post_init__(self):
        if self.allowed_scan_networks is None:
            self.allowed_scan_networks = ["192.168.0.0/16", "10.0.0.0/8", "172.16.0.0/12"]

class Config:
    """فئة التكوين الرئيسية"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or self._get_default_config_path()
        self.config_dir = Path(self.config_path).parent
        
        # إنشاء المجلدات المطلوبة
        self._create_directories()
        
        # تحميل التكوين
        self.app = AppConfig()
        self.gui = GUIConfig()
        self.cv = CVConfig()
        self.network = NetworkConfig()
        self.database = DatabaseConfig()
        self.security = SecurityConfig()
        
        self.load_config()
        
    def _get_default_config_path(self) -> str:
        """الحصول على مسار ملف التكوين الافتراضي"""
        if os.name == 'nt':  # Windows
            config_dir = os.path.join(os.environ['APPDATA'], 'CameraID')
        else:  # Linux/Mac
            config_dir = os.path.join(os.path.expanduser('~'), '.config', 'cameraid')
            
        return os.path.join(config_dir, 'config.yaml')
        
    def _create_directories(self):
        """إنشاء المجلدات المطلوبة"""
        dirs_to_create = [
            self.config_dir,
            'data',
            'models',
            'logs',
            'reports',
            'temp'
        ]
        
        for dir_name in dirs_to_create:
            if isinstance(dir_name, str) and not os.path.isabs(dir_name):
                dir_path = Path(dir_name)
            else:
                dir_path = Path(dir_name)
            dir_path.mkdir(parents=True, exist_ok=True)
            
    def load_config(self):
        """تحميل التكوين من ملف"""
        if os.path.exists(self.config_path):
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
                    
                if config_data:
                    # تحديث التكوين من البيانات المحمَّلة
                    for section_name, section_data in config_data.items():
                        if hasattr(self, section_name) and isinstance(section_data, dict):
                            section = getattr(self, section_name)
                            for key, value in section_data.items():
                                if hasattr(section, key):
                                    setattr(section, key, value)
                                    
            except Exception as e:
                print(f"خطأ في تحميل التكوين: {e}")
                self.save_config()  # حفظ التكوين الافتراضي
        else:
            self.save_config()
            
    def save_config(self):
        """حفظ التكوين في ملف"""
        try:
            config_data = {
                'app': asdict(self.app),
                'gui': asdict(self.gui),
                'cv': asdict(self.cv),
                'network': asdict(self.network),
                'database': asdict(self.database),
                'security': asdict(self.security)
            }
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
                         
        except Exception as e:
            print(f"خطأ في حفظ التكوين: {e}")
            
    def get_data_path(self, filename: str) -> str:
        """الحصول على مسار ملف في مجلد البيانات"""
        return os.path.join('data', filename)
        
    def get_model_path(self, filename: str) -> str:
        """الحصول على مسار نموذج في مجلد النماذج"""
        return os.path.join('models', filename)
        
    def get_log_path(self, filename: str) -> str:
        """الحصول على مسار ملف السجل"""
        return os.path.join('logs', filename)
        
    def get_report_path(self, filename: str) -> str:
        """الحصول على مسار ملف التقرير"""
        return os.path.join('reports', filename)
        
    def update_config(self, section: str, key: str, value: Any):
        """تحديث قيمة في التكوين"""
        if hasattr(self, section):
            section_obj = getattr(self, section)
            if hasattr(section_obj, key):
                setattr(section_obj, key, value)
                self.save_config()
                return True
        return False
        
    def reset_to_defaults(self):
        """إعادة تعيين التكوين للقيم الافتراضية"""
        self.app = AppConfig()
        self.gui = GUIConfig()
        self.cv = CVConfig()
        self.network = NetworkConfig()
        self.database = DatabaseConfig()
        self.security = SecurityConfig()
        self.save_config()