#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام قاعدة البيانات المتقدم - Advanced Database System
"""

import sqlite3
import json
import hashlib
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
import threading
import uuid

class CameraDatabase:
    """قاعدة بيانات الكاميرات المتقدمة"""
    
    def __init__(self, config, logger):
        self.config = config
        self.logger = logger
        self.db_path = config.get_database_path()
        self.lock = threading.Lock()
        
        # إنشاء قاعدة البيانات
        self.init_database()
        
        # تحميل البيانات الأساسية
        self.load_initial_data()
    
    def init_database(self):
        """تهيئة قاعدة البيانات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # جدول الكاميرات
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS cameras (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        brand TEXT NOT NULL,
                        model TEXT NOT NULL,
                        series TEXT,
                        type TEXT,
                        resolution TEXT,
                        features TEXT,
                        image_hash TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(brand, model)
                    )
                ''')
                
                # جدول الثغرات الأمنية
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS vulnerabilities (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        cve_id TEXT UNIQUE,
                        camera_id INTEGER,
                        severity TEXT,
                        description TEXT,
                        solution TEXT,
                        published_date DATE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (camera_id) REFERENCES cameras (id)
                    )
                ''')
                
                # جدول نتائج الفحص
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS scan_results (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        scan_id TEXT UNIQUE,
                        scan_type TEXT,
                        target TEXT,
                        camera_id INTEGER,
                        confidence REAL,
                        metadata TEXT,
                        image_path TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (camera_id) REFERENCES cameras (id)
                    )
                ''')
                
                # جدول الشبكات المفحوصة
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS network_scans (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        scan_id TEXT,
                        ip_address TEXT,
                        mac_address TEXT,
                        vendor TEXT,
                        open_ports TEXT,
                        device_type TEXT,
                        onvif_support BOOLEAN,
                        http_banner TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # جدول الإحصائيات
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS statistics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        stat_type TEXT,
                        stat_key TEXT,
                        stat_value TEXT,
                        date DATE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(stat_type, stat_key, date)
                    )
                ''')
                
                # جدول إعدادات المستخدم
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS user_settings (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        setting_key TEXT UNIQUE,
                        setting_value TEXT,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # إنشاء الفهارس
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_cameras_brand ON cameras(brand)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_cameras_model ON cameras(model)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_vulnerabilities_cve ON vulnerabilities(cve_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_scan_results_type ON scan_results(scan_type)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_network_scans_ip ON network_scans(ip_address)')
                
                conn.commit()
                self.logger.info("تم تهيئة قاعدة البيانات بنجاح")
                
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة قاعدة البيانات: {e}")
            raise
    
    def load_initial_data(self):
        """تحميل البيانات الأساسية"""
        try:
            # تحميل بيانات الكاميرات الأساسية
            initial_cameras = [
                {
                    "brand": "Hikvision",
                    "model": "DS-2CD2142FWD-I",
                    "series": "EasyIP 2.0",
                    "type": "Fixed Dome",
                    "resolution": "4MP",
                    "features": ["IR Night Vision", "IP67", "PoE", "H.264+"]
                },
                {
                    "brand": "Dahua",
                    "model": "IPC-HDW4431C-A",
                    "series": "Lite Series",
                    "type": "Eyeball",
                    "resolution": "4MP",
                    "features": ["Starlight", "Built-in Mic", "IP67", "PoE"]
                },
                {
                    "brand": "Axis",
                    "model": "P3375-V",
                    "series": "AXIS P33 Series",
                    "type": "Fixed Dome",
                    "resolution": "HDTV 1080p",
                    "features": ["Day/Night", "WDR", "Vandal Resistant", "PoE+"]
                },
                {
                    "brand": "Bosch",
                    "model": "NDI-5503-A",
                    "series": "FLEXIDOME IP",
                    "type": "Fixed Dome",
                    "resolution": "5MP",
                    "features": ["HDR", "Intelligent Video Analytics", "IK10", "PoE+"]
                },
                {
                    "brand": "Sony",
                    "model": "SNC-VB770",
                    "series": "Network Camera",
                    "type": "Box Camera",
                    "resolution": "Full HD",
                    "features": ["XDNR", "View-DR", "Intelligent Motion Detection"]
                }
            ]
            
            # إضافة الكاميرات إلى قاعدة البيانات
            for camera in initial_cameras:
                self.add_camera(camera)
            
            # تحميل الثغرات الأمنية الأساسية
            initial_vulnerabilities = [
                {
                    "cve_id": "CVE-2017-7921",
                    "brand": "Hikvision",
                    "model": "DS-2CD2142FWD-I",
                    "severity": "High",
                    "description": "Authentication bypass vulnerability",
                    "solution": "Update firmware to latest version"
                },
                {
                    "cve_id": "CVE-2019-3948",
                    "brand": "Dahua",
                    "model": "IPC-HDW4431C-A",
                    "severity": "Medium",
                    "description": "Default credentials vulnerability",
                    "solution": "Change default username and password"
                }
            ]
            
            # إضافة الثغرات إلى قاعدة البيانات
            for vuln in initial_vulnerabilities:
                self.add_vulnerability(vuln)
            
            self.logger.info("تم تحميل البيانات الأساسية")
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل البيانات الأساسية: {e}")
    
    def add_camera(self, camera_data: Dict[str, Any]) -> int:
        """إضافة كاميرا جديدة"""
        try:
            with self.lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    # التحقق من وجود الكاميرا
                    cursor.execute(
                        'SELECT id FROM cameras WHERE brand = ? AND model = ?',
                        (camera_data['brand'], camera_data['model'])
                    )
                    
                    existing = cursor.fetchone()
                    if existing:
                        return existing[0]
                    
                    # إضافة الكاميرا الجديدة
                    cursor.execute('''
                        INSERT INTO cameras (brand, model, series, type, resolution, features)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (
                        camera_data['brand'],
                        camera_data['model'],
                        camera_data.get('series', ''),
                        camera_data.get('type', ''),
                        camera_data.get('resolution', ''),
                        json.dumps(camera_data.get('features', []))
                    ))
                    
                    camera_id = cursor.lastrowid
                    conn.commit()
                    
                    self.logger.info(f"تم إضافة كاميرا جديدة: {camera_data['brand']} {camera_data['model']}")
                    return camera_id
                    
        except Exception as e:
            self.logger.error(f"خطأ في إضافة الكاميرا: {e}")
            return None
    
    def add_vulnerability(self, vuln_data: Dict[str, Any]) -> int:
        """إضافة ثغرة أمنية"""
        try:
            with self.lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    # البحث عن الكاميرا
                    camera_id = None
                    if 'brand' in vuln_data and 'model' in vuln_data:
                        cursor.execute(
                            'SELECT id FROM cameras WHERE brand = ? AND model = ?',
                            (vuln_data['brand'], vuln_data['model'])
                        )
                        result = cursor.fetchone()
                        if result:
                            camera_id = result[0]
                    
                    # إضافة الثغرة
                    cursor.execute('''
                        INSERT OR REPLACE INTO vulnerabilities 
                        (cve_id, camera_id, severity, description, solution, published_date)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (
                        vuln_data['cve_id'],
                        camera_id,
                        vuln_data.get('severity', 'Unknown'),
                        vuln_data.get('description', ''),
                        vuln_data.get('solution', ''),
                        vuln_data.get('published_date', datetime.now().date())
                    ))
                    
                    vuln_id = cursor.lastrowid
                    conn.commit()
                    
                    self.logger.info(f"تم إضافة ثغرة أمنية: {vuln_data['cve_id']}")
                    return vuln_id
                    
        except Exception as e:
            self.logger.error(f"خطأ في إضافة الثغرة الأمنية: {e}")
            return None
    
    def save_scan_result(self, scan_data: Dict[str, Any]) -> str:
        """حفظ نتيجة فحص"""
        try:
            scan_id = str(uuid.uuid4())
            
            with self.lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    # البحث عن الكاميرا المطابقة
                    camera_id = None
                    if 'brand' in scan_data and 'model' in scan_data:
                        cursor.execute(
                            'SELECT id FROM cameras WHERE brand = ? AND model = ?',
                            (scan_data['brand'], scan_data['model'])
                        )
                        result = cursor.fetchone()
                        if result:
                            camera_id = result[0]
                    
                    # حفظ نتيجة الفحص
                    cursor.execute('''
                        INSERT INTO scan_results 
                        (scan_id, scan_type, target, camera_id, confidence, metadata, image_path)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        scan_id,
                        scan_data.get('scan_type', 'unknown'),
                        scan_data.get('target', ''),
                        camera_id,
                        scan_data.get('confidence', 0.0),
                        json.dumps(scan_data.get('metadata', {})),
                        scan_data.get('image_path', '')
                    ))
                    
                    conn.commit()
                    
                    # تحديث الإحصائيات
                    self.update_statistics('scan_count', 1)
                    
                    self.logger.info(f"تم حفظ نتيجة الفحص: {scan_id}")
                    return scan_id
                    
        except Exception as e:
            self.logger.error(f"خطأ في حفظ نتيجة الفحص: {e}")
            return None
    
    def save_network_scan(self, network_data: Dict[str, Any]) -> int:
        """حفظ نتيجة فحص الشبكة"""
        try:
            with self.lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    cursor.execute('''
                        INSERT INTO network_scans 
                        (scan_id, ip_address, mac_address, vendor, open_ports, 
                         device_type, onvif_support, http_banner)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        network_data.get('scan_id', ''),
                        network_data.get('ip_address', ''),
                        network_data.get('mac_address', ''),
                        network_data.get('vendor', ''),
                        json.dumps(network_data.get('open_ports', [])),
                        network_data.get('device_type', ''),
                        network_data.get('onvif_support', False),
                        network_data.get('http_banner', '')
                    ))
                    
                    scan_id = cursor.lastrowid
                    conn.commit()
                    
                    self.logger.info(f"تم حفظ نتيجة فحص الشبكة: {network_data.get('ip_address')}")
                    return scan_id
                    
        except Exception as e:
            self.logger.error(f"خطأ في حفظ نتيجة فحص الشبكة: {e}")
            return None
    
    def get_camera_by_id(self, camera_id: int) -> Optional[Dict[str, Any]]:
        """الحصول على كاميرا بالمعرف"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('SELECT * FROM cameras WHERE id = ?', (camera_id,))
                row = cursor.fetchone()
                
                if row:
                    camera = dict(row)
                    camera['features'] = json.loads(camera['features'] or '[]')
                    return camera
                
                return None
                
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على الكاميرا: {e}")
            return None
    
    def search_cameras(self, query: str) -> List[Dict[str, Any]]:
        """البحث في الكاميرات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM cameras 
                    WHERE brand LIKE ? OR model LIKE ? OR series LIKE ?
                    ORDER BY brand, model
                ''', (f'%{query}%', f'%{query}%', f'%{query}%'))
                
                cameras = []
                for row in cursor.fetchall():
                    camera = dict(row)
                    camera['features'] = json.loads(camera['features'] or '[]')
                    cameras.append(camera)
                
                return cameras
                
        except Exception as e:
            self.logger.error(f"خطأ في البحث في الكاميرات: {e}")
            return []
    
    def get_vulnerabilities_for_camera(self, camera_id: int) -> List[Dict[str, Any]]:
        """الحصول على الثغرات الأمنية لكاميرا معينة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM vulnerabilities 
                    WHERE camera_id = ?
                    ORDER BY severity DESC, published_date DESC
                ''', (camera_id,))
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على الثغرات الأمنية: {e}")
            return []
    
    def get_scan_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """الحصول على تاريخ الفحص"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT sr.*, c.brand, c.model 
                    FROM scan_results sr
                    LEFT JOIN cameras c ON sr.camera_id = c.id
                    ORDER BY sr.created_at DESC
                    LIMIT ?
                ''', (limit,))
                
                results = []
                for row in cursor.fetchall():
                    result = dict(row)
                    result['metadata'] = json.loads(result['metadata'] or '{}')
                    results.append(result)
                
                return results
                
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على تاريخ الفحص: {e}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """الحصول على الإحصائيات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                stats = {}
                
                # عدد الكاميرات
                cursor.execute('SELECT COUNT(*) FROM cameras')
                stats['total_cameras'] = cursor.fetchone()[0]
                
                # عدد الثغرات
                cursor.execute('SELECT COUNT(*) FROM vulnerabilities')
                stats['total_vulnerabilities'] = cursor.fetchone()[0]
                
                # عدد عمليات الفحص
                cursor.execute('SELECT COUNT(*) FROM scan_results')
                stats['total_scans'] = cursor.fetchone()[0]
                
                # عدد فحوصات الشبكة
                cursor.execute('SELECT COUNT(*) FROM network_scans')
                stats['total_network_scans'] = cursor.fetchone()[0]
                
                # أكثر الماركات شيوعاً
                cursor.execute('''
                    SELECT brand, COUNT(*) as count 
                    FROM cameras 
                    GROUP BY brand 
                    ORDER BY count DESC 
                    LIMIT 5
                ''')
                stats['top_brands'] = [{'brand': row[0], 'count': row[1]} for row in cursor.fetchall()]
                
                # إحصائيات الفحص الأخيرة
                cursor.execute('''
                    SELECT scan_type, COUNT(*) as count 
                    FROM scan_results 
                    WHERE created_at >= date('now', '-30 days')
                    GROUP BY scan_type
                ''')
                stats['recent_scans'] = [{'type': row[0], 'count': row[1]} for row in cursor.fetchall()]
                
                return stats
                
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على الإحصائيات: {e}")
            return {}
    
    def update_statistics(self, stat_key: str, value: Any):
        """تحديث الإحصائيات"""
        try:
            with self.lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    today = datetime.now().date()
                    
                    # محاولة تحديث القيمة الموجودة
                    cursor.execute('''
                        UPDATE statistics 
                        SET stat_value = stat_value + ?, updated_at = CURRENT_TIMESTAMP
                        WHERE stat_type = 'daily' AND stat_key = ? AND date = ?
                    ''', (str(value), stat_key, today))
                    
                    # إذا لم توجد، إنشاء سجل جديد
                    if cursor.rowcount == 0:
                        cursor.execute('''
                            INSERT INTO statistics (stat_type, stat_key, stat_value, date)
                            VALUES ('daily', ?, ?, ?)
                        ''', (stat_key, str(value), today))
                    
                    conn.commit()
                    
        except Exception as e:
            self.logger.error(f"خطأ في تحديث الإحصائيات: {e}")
    
    def cleanup_old_data(self, days: int = 90):
        """تنظيف البيانات القديمة"""
        try:
            with self.lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    cutoff_date = datetime.now() - timedelta(days=days)
                    
                    # حذف نتائج الفحص القديمة
                    cursor.execute(
                        'DELETE FROM scan_results WHERE created_at < ?',
                        (cutoff_date,)
                    )
                    
                    # حذف فحوصات الشبكة القديمة
                    cursor.execute(
                        'DELETE FROM network_scans WHERE created_at < ?',
                        (cutoff_date,)
                    )
                    
                    # حذف الإحصائيات القديمة
                    cursor.execute(
                        'DELETE FROM statistics WHERE created_at < ?',
                        (cutoff_date,)
                    )
                    
                    conn.commit()
                    
                    self.logger.info(f"تم تنظيف البيانات الأقدم من {days} يوم")
                    
        except Exception as e:
            self.logger.error(f"خطأ في تنظيف البيانات: {e}")
    
    def backup_database(self, backup_path: str) -> bool:
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            import shutil
            
            with self.lock:
                shutil.copy2(self.db_path, backup_path)
                
            self.logger.info(f"تم إنشاء نسخة احتياطية: {backup_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False
    
    def restore_database(self, backup_path: str) -> bool:
        """استعادة قاعدة البيانات من نسخة احتياطية"""
        try:
            import shutil
            
            with self.lock:
                # إنشاء نسخة احتياطية من الحالية
                current_backup = f"{self.db_path}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                shutil.copy2(self.db_path, current_backup)
                
                # استعادة من النسخة الاحتياطية
                shutil.copy2(backup_path, self.db_path)
                
            self.logger.info(f"تم استعادة قاعدة البيانات من: {backup_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في استعادة قاعدة البيانات: {e}")
            return False
