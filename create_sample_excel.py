#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملف Excel تجريبي
"""

import os
from pathlib import Path

def create_sample_excel():
    """إنشاء ملف Excel تجريبي"""
    
    try:
        import pandas as pd
        
        # إنشاء مجلد excel_files
        excel_dir = Path("excel_files")
        excel_dir.mkdir(exist_ok=True)
        
        # بيانات تجريبية للكاميرات
        camera_data = {
            'الرقم': [1, 2, 3, 4, 5],
            'الماركة': ['Hikvision', 'Dahua', 'Axis', 'Bosch', 'Sony'],
            'الموديل': ['DS-2CD2142FWD-I', 'IPC-HDW4431C-A', 'P3375-V', 'NDI-5503-A', 'SNC-VB770'],
            'النوع': ['Fixed Dome', 'Eyeball', 'Fixed Dome', 'Fixed Dome', 'Box Camera'],
            'الدقة': ['4MP', '4MP', 'HDTV 1080p', '5MP', 'Full HD'],
            'مستوى الثقة': [0.95, 0.88, 0.92, 0.85, 0.78],
            'تاريخ الاكتشاف': ['2024-01-15', '2024-01-16', '2024-01-17', '2024-01-18', '2024-01-19'],
            'ملاحظات': [
                'كاميرا عالية الجودة مع رؤية ليلية',
                'مناسبة للاستخدام الداخلي',
                'كاميرا احترافية مقاومة للتخريب',
                'تحليل فيديو ذكي مدمج',
                'كاميرا صندوقية عالية الأداء'
            ]
        }
        
        # إنشاء DataFrame
        df_cameras = pd.DataFrame(camera_data)
        
        # بيانات تجريبية للشبكة
        network_data = {
            'الرقم': [1, 2, 3, 4],
            'عنوان IP': ['*************', '*************', '*************', '*************'],
            'عنوان MAC': ['00:11:22:33:44:55', 'AA:BB:CC:DD:EE:FF', '11:22:33:44:55:66', '77:88:99:AA:BB:CC'],
            'المصنع': ['Hikvision', 'Dahua', 'Axis', 'Bosch'],
            'المنافذ المفتوحة': ['80, 554, 8000', '80, 554', '80, 443, 554', '80, 443'],
            'نوع الجهاز': ['IP Camera', 'IP Camera', 'IP Camera', 'IP Camera'],
            'دعم ONVIF': [True, True, True, False],
            'HTTP Banner': ['Hikvision Web Server', 'Dahua Web Server', 'Axis Web Server', 'Bosch Web Server'],
            'الحالة': ['نشط', 'نشط', 'نشط', 'نشط']
        }
        
        df_network = pd.DataFrame(network_data)
        
        # بيانات تجريبية للأمان
        security_data = {
            'الرقم': [1, 2, 3],
            'CVE ID': ['CVE-2017-7921', 'CVE-2019-3948', 'CVE-2018-1000600'],
            'الماركة': ['Hikvision', 'Dahua', 'Axis'],
            'الموديل': ['DS-2CD2142FWD-I', 'IPC-HDW4431C-A', 'P3375-V'],
            'مستوى الخطورة': ['High', 'Medium', 'Low'],
            'الوصف': [
                'Authentication bypass vulnerability allowing unauthorized access',
                'Default credentials vulnerability',
                'Information disclosure vulnerability'
            ],
            'الحل المقترح': [
                'Update firmware to version 5.4.5 or later',
                'Change default username and password immediately',
                'Apply security patch from vendor'
            ],
            'تاريخ النشر': ['2017-04-24', '2019-07-15', '2018-06-20']
        }
        
        df_security = pd.DataFrame(security_data)
        
        # إنشاء ملف Excel مع أوراق متعددة
        excel_path = excel_dir / "sample_report.xlsx"
        
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            df_cameras.to_excel(writer, sheet_name='الكاميرات', index=False)
            df_network.to_excel(writer, sheet_name='الشبكة', index=False)
            df_security.to_excel(writer, sheet_name='الأمان', index=False)
        
        print(f"✅ تم إنشاء ملف Excel: {excel_path}")
        
        # إنشاء ملفات CSV
        df_cameras.to_csv(excel_dir / "cameras.csv", index=False, encoding='utf-8-sig')
        df_network.to_csv(excel_dir / "network.csv", index=False, encoding='utf-8-sig')
        df_security.to_csv(excel_dir / "security.csv", index=False, encoding='utf-8-sig')
        
        print("✅ تم إنشاء ملفات CSV")
        
        # إنشاء ملفات JSON
        df_cameras.to_json(excel_dir / "cameras.json", orient='records', force_ascii=False, indent=2)
        df_network.to_json(excel_dir / "network.json", orient='records', force_ascii=False, indent=2)
        df_security.to_json(excel_dir / "security.json", orient='records', force_ascii=False, indent=2)
        
        print("✅ تم إنشاء ملفات JSON")
        
        print("\n📁 الملفات المُنشأة:")
        for file_path in excel_dir.glob("*"):
            if file_path.is_file() and file_path.name != "sample_cameras.xlsx":
                size = file_path.stat().st_size
                print(f"   • {file_path.name} ({size} بايت)")
        
        return True
        
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        print("💡 يرجى تثبيت pandas و openpyxl:")
        print("   pip install pandas openpyxl")
        return False
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

if __name__ == "__main__":
    print("📊 إنشاء ملفات Excel تجريبية...")
    success = create_sample_excel()
    
    if success:
        print("\n🎉 تم إنشاء الملفات بنجاح!")
        print("🚀 يمكنك الآن اختبار مدير Excel في التطبيق")
    else:
        print("\n❌ فشل في إنشاء الملفات")
    
    input("\nاضغط Enter للخروج...")
