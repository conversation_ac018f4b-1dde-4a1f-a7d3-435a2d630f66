#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ماسح الشبكة - Network Scanner
واجهة الفحص الشبكي للكاميرات
"""

import tkinter as tk
from tkinter import ttk, messagebox
import customtkinter as ctk
import threading
import socket
import requests
from datetime import datetime
import ipaddress
import subprocess
import platform

class NetworkScannerFrame(ctk.CTkFrame):
    """إطار ماسح الشبكة"""
    
    def __init__(self, parent, config):
        super().__init__(parent)
        self.config = config
        self.is_scanning = False
        
        self.create_widgets()
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        
        # العنوان
        title = ctk.CTkLabel(
            self,
            text="الفحص الشبكي للكاميرات",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title.pack(pady=20)
        
        # إطار الإعدادات
        settings_frame = ctk.CTkFrame(self)
        settings_frame.pack(fill="x", padx=20, pady=10)
        
        settings_title = ctk.CTkLabel(
            settings_frame,
            text="إعدادات الفحص",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        settings_title.pack(pady=10)
        
        # إدخال نطاق IP
        ip_frame = ctk.CTkFrame(settings_frame, fg_color="transparent")
        ip_frame.pack(fill="x", padx=10, pady=5)
        
        ip_label = ctk.CTkLabel(ip_frame, text="نطاق الشبكة:")
        ip_label.pack(side="right", padx=10)
        
        self.ip_entry = ctk.CTkEntry(
            ip_frame,
            placeholder_text="***********/24",
            width=200
        )
        self.ip_entry.pack(side="left", padx=10)
        self.ip_entry.insert(0, self.config.get("network.scan_range", "***********/24"))
        
        # إدخال المنافذ
        ports_frame = ctk.CTkFrame(settings_frame, fg_color="transparent")
        ports_frame.pack(fill="x", padx=10, pady=5)
        
        ports_label = ctk.CTkLabel(ports_frame, text="المنافذ:")
        ports_label.pack(side="right", padx=10)
        
        self.ports_entry = ctk.CTkEntry(
            ports_frame,
            placeholder_text="80,8080,554,8000",
            width=200
        )
        self.ports_entry.pack(side="left", padx=10)
        self.ports_entry.insert(0, "80,8080,554,8000,8899")
        
        # خيارات الفحص
        options_frame = ctk.CTkFrame(settings_frame, fg_color="transparent")
        options_frame.pack(fill="x", padx=10, pady=5)
        
        self.onvif_check = ctk.CTkCheckBox(options_frame, text="فحص ONVIF")
        self.onvif_check.pack(side="right", padx=10)
        self.onvif_check.select()
        
        self.http_check = ctk.CTkCheckBox(options_frame, text="فحص HTTP")
        self.http_check.pack(side="right", padx=10)
        self.http_check.select()
        
        self.ping_check = ctk.CTkCheckBox(options_frame, text="فحص Ping")
        self.ping_check.pack(side="right", padx=10)
        self.ping_check.select()
        
        # أزرار التحكم
        controls_frame = ctk.CTkFrame(settings_frame, fg_color="transparent")
        controls_frame.pack(pady=10)
        
        self.scan_btn = ctk.CTkButton(
            controls_frame,
            text="🔍 بدء الفحص",
            command=self.start_scan,
            width=150,
            height=40
        )
        self.scan_btn.pack(side="left", padx=5)
        
        self.stop_btn = ctk.CTkButton(
            controls_frame,
            text="⏹️ إيقاف الفحص",
            command=self.stop_scan,
            width=150,
            height=40,
            state="disabled"
        )
        self.stop_btn.pack(side="left", padx=5)
        
        self.clear_btn = ctk.CTkButton(
            controls_frame,
            text="🗑️ مسح النتائج",
            command=self.clear_results,
            width=150,
            height=40
        )
        self.clear_btn.pack(side="left", padx=5)
        
        # شريط التقدم
        self.progress_frame = ctk.CTkFrame(self)
        self.progress_frame.pack(fill="x", padx=20, pady=5)
        
        self.progress_label = ctk.CTkLabel(
            self.progress_frame,
            text="جاهز للفحص"
        )
        self.progress_label.pack(pady=5)
        
        self.progress_bar = ctk.CTkProgressBar(self.progress_frame)
        self.progress_bar.pack(fill="x", padx=10, pady=5)
        self.progress_bar.set(0)
        
        # إطار النتائج
        results_frame = ctk.CTkFrame(self)
        results_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        results_title = ctk.CTkLabel(
            results_frame,
            text="نتائج الفحص",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        results_title.pack(pady=10)
        
        # جدول النتائج
        self.results_tree = ttk.Treeview(
            results_frame,
            columns=("ip", "ports", "device", "model", "status"),
            show="headings",
            height=15
        )
        
        # تعيين عناوين الأعمدة
        self.results_tree.heading("ip", text="عنوان IP")
        self.results_tree.heading("ports", text="المنافذ المفتوحة")
        self.results_tree.heading("device", text="نوع الجهاز")
        self.results_tree.heading("model", text="الموديل")
        self.results_tree.heading("status", text="الحالة")
        
        # تعيين عرض الأعمدة
        self.results_tree.column("ip", width=120)
        self.results_tree.column("ports", width=150)
        self.results_tree.column("device", width=150)
        self.results_tree.column("model", width=200)
        self.results_tree.column("status", width=100)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(results_frame, orient="vertical", command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=scrollbar.set)
        
        # تعبئة الجدول وشريط التمرير
        tree_frame = ctk.CTkFrame(results_frame, fg_color="transparent")
        tree_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        self.results_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def start_scan(self):
        """بدء عملية الفحص"""
        if self.is_scanning:
            return
        
        # التحقق من صحة المدخلات
        ip_range = self.ip_entry.get().strip()
        ports_str = self.ports_entry.get().strip()
        
        if not ip_range:
            messagebox.showerror("خطأ", "يرجى إدخال نطاق الشبكة")
            return
        
        if not ports_str:
            messagebox.showerror("خطأ", "يرجى إدخال المنافذ")
            return
        
        try:
            # التحقق من صحة نطاق IP
            network = ipaddress.ip_network(ip_range, strict=False)
            
            # التحقق من صحة المنافذ
            ports = [int(p.strip()) for p in ports_str.split(",")]
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في المدخلات: {str(e)}")
            return
        
        # بدء الفحص
        self.is_scanning = True
        self.scan_btn.configure(state="disabled")
        self.stop_btn.configure(state="normal")
        self.progress_label.configure(text="جاري الفحص...")
        self.progress_bar.set(0)
        
        # تشغيل الفحص في خيط منفصل
        scan_thread = threading.Thread(
            target=self.perform_scan,
            args=(network, ports)
        )
        scan_thread.daemon = True
        scan_thread.start()
    
    def stop_scan(self):
        """إيقاف عملية الفحص"""
        self.is_scanning = False
        self.scan_btn.configure(state="normal")
        self.stop_btn.configure(state="disabled")
        self.progress_label.configure(text="تم إيقاف الفحص")
    
    def clear_results(self):
        """مسح النتائج"""
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        self.progress_bar.set(0)
        self.progress_label.configure(text="جاهز للفحص")
    
    def perform_scan(self, network, ports):
        """تنفيذ عملية الفحص"""
        try:
            hosts = list(network.hosts())
            total_hosts = len(hosts)
            scanned_hosts = 0
            
            for host in hosts:
                if not self.is_scanning:
                    break
                
                host_ip = str(host)
                
                # تحديث شريط التقدم
                progress = scanned_hosts / total_hosts
                self.after(0, lambda p=progress: self.progress_bar.set(p))
                self.after(0, lambda ip=host_ip: self.progress_label.configure(
                    text=f"فحص {ip}..."
                ))
                
                # فحص المضيف
                result = self.scan_host(host_ip, ports)
                
                if result:
                    # إضافة النتيجة إلى الجدول
                    self.after(0, lambda r=result: self.add_result_to_tree(r))
                
                scanned_hosts += 1
            
            # انتهاء الفحص
            self.after(0, self.scan_completed)
            
        except Exception as e:
            self.after(0, lambda: messagebox.showerror("خطأ", f"خطأ في الفحص: {str(e)}"))
            self.after(0, self.scan_completed)
    
    def scan_host(self, ip, ports):
        """فحص مضيف واحد"""
        if not self.is_scanning:
            return None
        
        result = {
            "ip": ip,
            "open_ports": [],
            "device_type": "غير معروف",
            "model": "غير محدد",
            "status": "غير متاح"
        }
        
        # فحص Ping إذا كان مفعلاً
        if self.ping_check.get():
            if self.ping_host(ip):
                result["status"] = "متاح"
            else:
                return None  # المضيف غير متاح
        
        # فحص المنافذ
        for port in ports:
            if not self.is_scanning:
                break
            
            if self.check_port(ip, port):
                result["open_ports"].append(port)
        
        if not result["open_ports"]:
            return None  # لا توجد منافذ مفتوحة
        
        # فحص HTTP إذا كان مفعلاً
        if self.http_check.get():
            device_info = self.check_http_device(ip, result["open_ports"])
            if device_info:
                result["device_type"] = device_info.get("type", "غير معروف")
                result["model"] = device_info.get("model", "غير محدد")
        
        # فحص ONVIF إذا كان مفعلاً
        if self.onvif_check.get():
            onvif_info = self.check_onvif_device(ip)
            if onvif_info:
                result["device_type"] = "كاميرا IP"
                result["model"] = onvif_info.get("model", result["model"])
        
        return result
    
    def ping_host(self, ip):
        """فحص ping للمضيف"""
        try:
            if platform.system().lower() == "windows":
                cmd = ["ping", "-n", "1", "-w", "1000", ip]
            else:
                cmd = ["ping", "-c", "1", "-W", "1", ip]
            
            result = subprocess.run(cmd, capture_output=True, timeout=2)
            return result.returncode == 0
        except:
            return False
    
    def check_port(self, ip, port):
        """فحص منفذ محدد"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex((ip, port))
            sock.close()
            return result == 0
        except:
            return False
    
    def check_http_device(self, ip, ports):
        """فحص جهاز HTTP"""
        http_ports = [p for p in ports if p in [80, 8080, 8000, 8899]]
        
        for port in http_ports:
            try:
                url = f"http://{ip}:{port}"
                response = requests.get(url, timeout=3)
                
                # تحليل الاستجابة لتحديد نوع الجهاز
                content = response.text.lower()
                headers = response.headers
                
                device_info = {"type": "جهاز شبكي", "model": "غير محدد"}
                
                # البحث عن علامات الكاميرات
                if any(keyword in content for keyword in ["camera", "webcam", "ipcam", "surveillance"]):
                    device_info["type"] = "كاميرا IP"
                
                # البحث عن الماركات الشائعة
                brands = {
                    "hikvision": "Hikvision",
                    "dahua": "Dahua",
                    "axis": "Axis",
                    "bosch": "Bosch",
                    "sony": "Sony",
                    "panasonic": "Panasonic"
                }
                
                for brand_key, brand_name in brands.items():
                    if brand_key in content:
                        device_info["model"] = brand_name
                        break
                
                return device_info
                
            except:
                continue
        
        return None
    
    def check_onvif_device(self, ip):
        """فحص جهاز ONVIF"""
        # محاكاة فحص ONVIF (يتطلب مكتبة onvif-zeep)
        try:
            # هذا مجرد مثال - يحتاج إلى تطبيق حقيقي لـ ONVIF
            return {"model": "كاميرا ONVIF"}
        except:
            return None
    
    def add_result_to_tree(self, result):
        """إضافة نتيجة إلى الجدول"""
        ports_str = ", ".join(map(str, result["open_ports"]))
        
        self.results_tree.insert("", "end", values=(
            result["ip"],
            ports_str,
            result["device_type"],
            result["model"],
            result["status"]
        ))
    
    def scan_completed(self):
        """انتهاء عملية الفحص"""
        self.is_scanning = False
        self.scan_btn.configure(state="normal")
        self.stop_btn.configure(state="disabled")
        self.progress_bar.set(1.0)
        self.progress_label.configure(text="تم انتهاء الفحص")
        
        # عد النتائج
        result_count = len(self.results_tree.get_children())
        messagebox.showinfo("انتهى الفحص", f"تم العثور على {result_count} جهاز")
