#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للتطبيق الهجين
"""

import sys
import os
from pathlib import Path

print("🎯 اختبار التطبيق الهجين - كاميرا آيـدي")
print("=" * 50)

# فحص Python
print(f"✅ إصدار Python: {sys.version}")
print(f"✅ مسار Python: {sys.executable}")

# فحص المسار الحالي
current_dir = Path.cwd()
print(f"✅ المجلد الحالي: {current_dir}")

# فحص الملفات المطلوبة
required_files = [
    'hybrid_app.py',
    'run_hybrid.py',
    'requirements_hybrid.txt',
    'templates/base.html',
    'templates/index.html'
]

print("\n📁 فحص الملفات المطلوبة:")
all_files_exist = True
for file_path in required_files:
    if Path(file_path).exists():
        print(f"✅ {file_path}")
    else:
        print(f"❌ {file_path} - مفقود")
        all_files_exist = False

# فحص المكتبات الأساسية
print("\n📦 فحص المكتبات الأساسية:")
basic_libraries = [
    ('flask', 'Flask'),
    ('customtkinter', 'CustomTkinter'),
    ('pandas', 'Pandas'),
    ('PIL', 'Pillow'),
    ('cv2', 'OpenCV')
]

available_libraries = []
for lib_name, display_name in basic_libraries:
    try:
        __import__(lib_name)
        print(f"✅ {display_name}")
        available_libraries.append(lib_name)
    except ImportError:
        print(f"❌ {display_name} - غير مثبت")

# تشغيل اختبار بسيط
if all_files_exist and len(available_libraries) >= 3:
    print("\n🚀 محاولة تشغيل اختبار بسيط...")
    
    try:
        # اختبار استيراد التطبيق
        sys.path.insert(0, str(Path.cwd()))
        
        # اختبار Flask
        if 'flask' in available_libraries:
            from flask import Flask
            test_app = Flask(__name__)
            print("✅ Flask يعمل بشكل صحيح")
        
        # اختبار CustomTkinter
        if 'customtkinter' in available_libraries:
            import customtkinter as ctk
            print("✅ CustomTkinter يعمل بشكل صحيح")
        
        # اختبار قاعدة البيانات
        import sqlite3
        test_db = sqlite3.connect(':memory:')
        test_db.close()
        print("✅ SQLite يعمل بشكل صحيح")
        
        print("\n🎉 الاختبار الأساسي نجح!")
        print("يمكنك الآن تشغيل التطبيق باستخدام:")
        print("python run_hybrid.py --hybrid")
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        print("يرجى تثبيت المتطلبات أولاً:")
        print("pip install flask customtkinter pandas pillow opencv-python")

else:
    print("\n❌ بعض الملفات أو المكتبات مفقودة")
    print("يرجى التأكد من:")
    print("1. وجود جميع ملفات التطبيق")
    print("2. تثبيت المكتبات المطلوبة")

print("\n" + "=" * 50)
print("انتهى الاختبار")
