#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل التطبيق الهجين - Hybrid App Launcher
يدعم تشغيل التطبيق في أوضاع متعددة
"""

import sys
import os
import argparse
import subprocess
import threading
import time
import webbrowser
import socket
from pathlib import Path
import json
import logging
from datetime import datetime

# إضافة المسار الحالي لـ Python Path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from hybrid_app import HybridCameraIDApp
except ImportError as e:
    print(f"❌ خطأ في استيراد التطبيق: {e}")
    print("تأكد من تثبيت جميع المتطلبات باستخدام:")
    print("pip install -r requirements_hybrid.txt")
    sys.exit(1)

class HybridLauncher:
    """مشغل التطبيق الهجين"""
    
    def __init__(self):
        self.setup_logging()
        self.app = None
        self.web_process = None
        self.desktop_process = None
        
    def setup_logging(self):
        """إعداد نظام السجلات"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('hybrid_launcher.log', encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def check_dependencies(self):
        """فحص المتطلبات"""
        required_packages = [
            'flask', 'customtkinter', 'opencv-python', 
            'pandas', 'sqlalchemy', 'cryptography'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            self.logger.error(f"❌ المكتبات المفقودة: {', '.join(missing_packages)}")
            print("\n🔧 لتثبيت المتطلبات المفقودة:")
            print("pip install -r requirements_hybrid.txt")
            return False
            
        self.logger.info("✅ جميع المتطلبات متوفرة")
        return True
    
    def find_free_port(self, start_port=5000):
        """البحث عن منفذ متاح"""
        for port in range(start_port, start_port + 100):
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.bind(('localhost', port))
                sock.close()
                return port
            except:
                continue
        return start_port
    
    def run_desktop_only(self):
        """تشغيل سطح المكتب فقط"""
        self.logger.info("🖥️ تشغيل تطبيق سطح المكتب")
        try:
            self.app = HybridCameraIDApp()
            self.app.run_desktop()
        except Exception as e:
            self.logger.error(f"❌ خطأ في تشغيل سطح المكتب: {e}")
            return False
        return True
    
    def run_web_only(self, port=5000, host='0.0.0.0'):
        """تشغيل الويب فقط"""
        self.logger.info(f"🌐 تشغيل خادم الويب على {host}:{port}")
        try:
            self.app = HybridCameraIDApp()
            self.app.run_web_only(port)
        except Exception as e:
            self.logger.error(f"❌ خطأ في تشغيل خادم الويب: {e}")
            return False
        return True
    
    def run_hybrid(self):
        """تشغيل هجين (سطح المكتب + ويب)"""
        self.logger.info("🚀 تشغيل التطبيق الهجين")
        try:
            self.app = HybridCameraIDApp()
            self.app.run_desktop()
        except Exception as e:
            self.logger.error(f"❌ خطأ في التشغيل الهجين: {e}")
            return False
        return True
    
    def run_web_background(self, port=5000):
        """تشغيل الويب في الخلفية"""
        def web_server():
            try:
                self.app = HybridCameraIDApp()
                self.app.run_web_only(port)
            except Exception as e:
                self.logger.error(f"❌ خطأ في خادم الويب الخلفي: {e}")
        
        self.web_process = threading.Thread(target=web_server, daemon=True)
        self.web_process.start()
        
        # انتظار تشغيل الخادم
        time.sleep(3)
        
        # فحص إذا كان الخادم يعمل
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            if result == 0:
                self.logger.info(f"✅ خادم الويب يعمل على المنفذ {port}")
                return True
            else:
                self.logger.error("❌ فشل في تشغيل خادم الويب")
                return False
        except Exception as e:
            self.logger.error(f"❌ خطأ في فحص خادم الويب: {e}")
            return False
    
    def open_browser(self, port=5000):
        """فتح المتصفح"""
        url = f"http://localhost:{port}"
        self.logger.info(f"🌐 فتح المتصفح: {url}")
        try:
            webbrowser.open(url)
            return True
        except Exception as e:
            self.logger.error(f"❌ خطأ في فتح المتصفح: {e}")
            return False
    
    def install_requirements(self):
        """تثبيت المتطلبات"""
        self.logger.info("📦 تثبيت المتطلبات...")
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', '-r', 'requirements_hybrid.txt'
            ])
            self.logger.info("✅ تم تثبيت المتطلبات بنجاح")
            return True
        except subprocess.CalledProcessError as e:
            self.logger.error(f"❌ خطأ في تثبيت المتطلبات: {e}")
            return False
    
    def create_desktop_shortcut(self):
        """إنشاء اختصار سطح المكتب"""
        try:
            import winshell
            from win32com.client import Dispatch
            
            desktop = winshell.desktop()
            path = os.path.join(desktop, "كاميرا آيـدي.lnk")
            target = sys.executable
            wDir = str(Path(__file__).parent)
            arguments = f'"{Path(__file__)}" --hybrid'
            
            shell = Dispatch('WScript.Shell')
            shortcut = shell.CreateShortCut(path)
            shortcut.Targetpath = target
            shortcut.Arguments = arguments
            shortcut.WorkingDirectory = wDir
            shortcut.IconLocation = target
            shortcut.save()
            
            self.logger.info("✅ تم إنشاء اختصار سطح المكتب")
            return True
        except Exception as e:
            self.logger.warning(f"⚠️ لم يتم إنشاء اختصار سطح المكتب: {e}")
            return False
    
    def show_system_info(self):
        """عرض معلومات النظام"""
        import platform
        import psutil
        
        print("\n" + "="*50)
        print("🖥️  معلومات النظام - System Information")
        print("="*50)
        print(f"نظام التشغيل: {platform.system()} {platform.release()}")
        print(f"المعمارية: {platform.architecture()[0]}")
        print(f"المعالج: {platform.processor()}")
        print(f"إصدار Python: {platform.python_version()}")
        print(f"الذاكرة المتاحة: {psutil.virtual_memory().available // (1024**3)} GB")
        print(f"مساحة القرص المتاحة: {psutil.disk_usage('.').free // (1024**3)} GB")
        print("="*50)
    
    def show_help(self):
        """عرض المساعدة"""
        help_text = """
🎯 مشغل التطبيق الهجين - Hybrid App Launcher

الاستخدام:
  python run_hybrid.py [OPTIONS]

الخيارات:
  --desktop-only        تشغيل سطح المكتب فقط
  --web-only           تشغيل الويب فقط
  --hybrid             تشغيل هجين (افتراضي)
  --port PORT          منفذ خادم الويب (افتراضي: 5000)
  --host HOST          عنوان خادم الويب (افتراضي: 0.0.0.0)
  --install            تثبيت المتطلبات
  --check              فحص المتطلبات
  --shortcut           إنشاء اختصار سطح المكتب
  --info               عرض معلومات النظام
  --browser            فتح المتصفح بعد التشغيل
  --help               عرض هذه المساعدة

أمثلة:
  python run_hybrid.py                    # تشغيل هجين
  python run_hybrid.py --web-only         # ويب فقط
  python run_hybrid.py --desktop-only     # سطح المكتب فقط
  python run_hybrid.py --port 8080        # ويب على منفذ 8080
  python run_hybrid.py --install          # تثبيت المتطلبات
  python run_hybrid.py --check            # فحص المتطلبات

🌟 ميزات التطبيق:
  ✅ فحص بصري ذكي للكاميرات
  ✅ فحص شبكي متقدم
  ✅ تقارير شاملة وتصدير
  ✅ واجهة عربية حديثة
  ✅ أمان وخصوصية عالية
  ✅ دعم Excel متقدم
  ✅ تحديثات مباشرة
  ✅ API شامل
        """
        print(help_text)

def main():
    """الدالة الرئيسية"""
    launcher = HybridLauncher()
    
    # إعداد معالج الحجج
    parser = argparse.ArgumentParser(
        description='مشغل التطبيق الهجين لكاميرا آيـدي',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument('--desktop-only', action='store_true',
                       help='تشغيل سطح المكتب فقط')
    parser.add_argument('--web-only', action='store_true',
                       help='تشغيل الويب فقط')
    parser.add_argument('--hybrid', action='store_true',
                       help='تشغيل هجين (افتراضي)')
    parser.add_argument('--port', type=int, default=5000,
                       help='منفذ خادم الويب (افتراضي: 5000)')
    parser.add_argument('--host', default='0.0.0.0',
                       help='عنوان خادم الويب (افتراضي: 0.0.0.0)')
    parser.add_argument('--install', action='store_true',
                       help='تثبيت المتطلبات')
    parser.add_argument('--check', action='store_true',
                       help='فحص المتطلبات')
    parser.add_argument('--shortcut', action='store_true',
                       help='إنشاء اختصار سطح المكتب')
    parser.add_argument('--info', action='store_true',
                       help='عرض معلومات النظام')
    parser.add_argument('--browser', action='store_true',
                       help='فتح المتصفح بعد التشغيل')
    parser.add_argument('--help-ar', action='store_true',
                       help='عرض المساعدة بالعربية')
    
    args = parser.parse_args()
    
    # عرض المساعدة
    if args.help_ar:
        launcher.show_help()
        return
    
    # عرض معلومات النظام
    if args.info:
        launcher.show_system_info()
        return
    
    # تثبيت المتطلبات
    if args.install:
        if launcher.install_requirements():
            print("✅ تم تثبيت المتطلبات بنجاح")
        else:
            print("❌ فشل في تثبيت المتطلبات")
        return
    
    # فحص المتطلبات
    if args.check:
        if launcher.check_dependencies():
            print("✅ جميع المتطلبات متوفرة")
        else:
            print("❌ بعض المتطلبات مفقودة")
        return
    
    # إنشاء اختصار
    if args.shortcut:
        launcher.create_desktop_shortcut()
        return
    
    # فحص المتطلبات قبل التشغيل
    if not launcher.check_dependencies():
        print("\n❌ بعض المتطلبات مفقودة. استخدم --install لتثبيتها")
        return
    
    # تحديد وضع التشغيل
    if args.desktop_only:
        success = launcher.run_desktop_only()
    elif args.web_only:
        success = launcher.run_web_only(args.port, args.host)
        if success and args.browser:
            time.sleep(2)
            launcher.open_browser(args.port)
    else:
        # تشغيل هجين (افتراضي)
        if args.browser:
            # تشغيل الويب في الخلفية أولاً
            if launcher.run_web_background(args.port):
                launcher.open_browser(args.port)
                time.sleep(1)
        
        success = launcher.run_hybrid()
    
    if success:
        launcher.logger.info("✅ تم إنهاء التطبيق بنجاح")
    else:
        launcher.logger.error("❌ حدث خطأ في التطبيق")
        sys.exit(1)

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف التطبيق بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
