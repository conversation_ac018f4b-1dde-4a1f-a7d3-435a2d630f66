#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عارض Excel المتقدم - Advanced Excel Viewer
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
from datetime import datetime
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
import threading

class ExcelViewer(ctk.CTkFrame):
    """عارض Excel المتقدم"""
    
    def __init__(self, parent, config, logger, excel_manager):
        super().__init__(parent)
        
        self.config = config
        self.logger = logger
        self.excel_manager = excel_manager
        self.current_file = None
        self.current_data = {}
        
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title_label = ctk.CTkLabel(
            self,
            text="📊 مدير Excel المتقدم",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
        
        # شريط الأدوات
        self.create_toolbar()
        
        # المنطقة الرئيسية
        self.create_main_area()
        
        # شريط الحالة
        self.create_status_bar()
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar_frame = ctk.CTkFrame(self)
        toolbar_frame.pack(fill="x", padx=10, pady=5)
        
        # أزرار الملفات
        file_frame = ctk.CTkFrame(toolbar_frame)
        file_frame.pack(side="right", padx=5)
        
        ctk.CTkButton(
            file_frame,
            text="📁 فتح ملف",
            command=self.open_file,
            width=100
        ).pack(side="right", padx=2)
        
        ctk.CTkButton(
            file_frame,
            text="💾 حفظ",
            command=self.save_file,
            width=100
        ).pack(side="right", padx=2)
        
        ctk.CTkButton(
            file_frame,
            text="📄 جديد",
            command=self.new_file,
            width=100
        ).pack(side="right", padx=2)
        
        # أزرار التقارير
        reports_frame = ctk.CTkFrame(toolbar_frame)
        reports_frame.pack(side="right", padx=10)
        
        ctk.CTkButton(
            reports_frame,
            text="📷 تقرير الكاميرات",
            command=self.create_camera_report,
            width=120
        ).pack(side="right", padx=2)
        
        ctk.CTkButton(
            reports_frame,
            text="🌐 تقرير الشبكة",
            command=self.create_network_report,
            width=120
        ).pack(side="right", padx=2)
        
        ctk.CTkButton(
            reports_frame,
            text="🔒 تقرير الأمان",
            command=self.create_security_report,
            width=120
        ).pack(side="right", padx=2)
        
        # أزرار الأدوات
        tools_frame = ctk.CTkFrame(toolbar_frame)
        tools_frame.pack(side="left", padx=5)
        
        ctk.CTkButton(
            tools_frame,
            text="📊 إضافة رسوم بيانية",
            command=self.add_charts,
            width=130
        ).pack(side="left", padx=2)
        
        ctk.CTkButton(
            tools_frame,
            text="🔄 تحويل الصيغة",
            command=self.convert_format,
            width=120
        ).pack(side="left", padx=2)
        
        ctk.CTkButton(
            tools_frame,
            text="✅ فحص الملف",
            command=self.validate_file,
            width=100
        ).pack(side="left", padx=2)
    
    def create_main_area(self):
        """إنشاء المنطقة الرئيسية"""
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # إنشاء Notebook للأوراق
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill="both", expand=True, padx=5, pady=5)
        
        # إنشاء ورقة ترحيب افتراضية
        self.create_welcome_tab()
    
    def create_welcome_tab(self):
        """إنشاء ورقة الترحيب"""
        welcome_frame = ctk.CTkFrame(self.notebook)
        self.notebook.add(welcome_frame, text="مرحباً")
        
        # رسالة الترحيب
        welcome_label = ctk.CTkLabel(
            welcome_frame,
            text="🎉 مرحباً بك في مدير Excel المتقدم",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        welcome_label.pack(pady=50)
        
        # تعليمات
        instructions = """
        📋 الميزات المتاحة:
        
        • فتح وتحرير ملفات Excel
        • إنشاء تقارير احترافية للكاميرات والشبكة والأمان
        • إضافة رسوم بيانية تفاعلية
        • تحويل الملفات إلى صيغ مختلفة (CSV, JSON, HTML)
        • فحص وتحقق من صحة الملفات
        • قوالب جاهزة للتقارير
        
        🚀 ابدأ بفتح ملف Excel أو إنشاء تقرير جديد!
        """
        
        instructions_label = ctk.CTkLabel(
            welcome_frame,
            text=instructions,
            font=ctk.CTkFont(size=12),
            justify="right"
        )
        instructions_label.pack(pady=20)
        
        # أزرار سريعة
        quick_buttons_frame = ctk.CTkFrame(welcome_frame)
        quick_buttons_frame.pack(pady=30)
        
        ctk.CTkButton(
            quick_buttons_frame,
            text="📁 فتح ملف Excel",
            command=self.open_file,
            width=150,
            height=40
        ).pack(side="left", padx=10)
        
        ctk.CTkButton(
            quick_buttons_frame,
            text="📊 إنشاء تقرير جديد",
            command=self.show_report_options,
            width=150,
            height=40
        ).pack(side="left", padx=10)
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_frame = ctk.CTkFrame(self, height=30)
        self.status_frame.pack(fill="x", padx=10, pady=5)
        
        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="جاهز",
            font=ctk.CTkFont(size=10)
        )
        self.status_label.pack(side="right", padx=10, pady=5)
        
        # معلومات الملف
        self.file_info_label = ctk.CTkLabel(
            self.status_frame,
            text="",
            font=ctk.CTkFont(size=10)
        )
        self.file_info_label.pack(side="left", padx=10, pady=5)
    
    def update_status(self, message: str):
        """تحديث شريط الحالة"""
        self.status_label.configure(text=message)
        self.update()
    
    def open_file(self):
        """فتح ملف Excel"""
        try:
            file_path = filedialog.askopenfilename(
                title="اختر ملف Excel",
                filetypes=[
                    ("Excel files", "*.xlsx *.xls"),
                    ("All files", "*.*")
                ]
            )
            
            if not file_path:
                return
            
            self.update_status("جاري تحميل الملف...")
            
            # تحميل الملف في خيط منفصل
            threading.Thread(
                target=self.load_file_thread,
                args=(file_path,),
                daemon=True
            ).start()
            
        except Exception as e:
            self.logger.error(f"خطأ في فتح الملف: {e}")
            messagebox.showerror("خطأ", f"فشل في فتح الملف:\n{str(e)}")
            self.update_status("خطأ في فتح الملف")
    
    def load_file_thread(self, file_path: str):
        """تحميل الملف في خيط منفصل"""
        try:
            # تحميل البيانات
            data = self.excel_manager.load_excel_file(file_path)
            
            if data is None:
                self.after(0, lambda: messagebox.showerror("خطأ", "فشل في تحميل الملف"))
                self.after(0, lambda: self.update_status("فشل في التحميل"))
                return
            
            # تحديث الواجهة في الخيط الرئيسي
            self.after(0, lambda: self.display_excel_data(file_path, data))
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل الملف: {e}")
            self.after(0, lambda: messagebox.showerror("خطأ", f"خطأ في تحميل الملف:\n{str(e)}"))
            self.after(0, lambda: self.update_status("خطأ في التحميل"))
    
    def display_excel_data(self, file_path: str, data: Dict[str, pd.DataFrame]):
        """عرض بيانات Excel"""
        try:
            self.current_file = file_path
            self.current_data = data
            
            # مسح الأوراق الموجودة
            for tab in self.notebook.tabs():
                self.notebook.forget(tab)
            
            # إضافة ورقة لكل sheet
            for sheet_name, df in data.items():
                self.create_data_tab(sheet_name, df)
            
            # تحديث معلومات الملف
            file_info = self.excel_manager.get_excel_info(file_path)
            info_text = f"الملف: {file_info.get('file_name', '')} | الحجم: {file_info.get('file_size', 0)} بايت | الأوراق: {len(data)}"
            self.file_info_label.configure(text=info_text)
            
            self.update_status("تم تحميل الملف بنجاح")
            
        except Exception as e:
            self.logger.error(f"خطأ في عرض البيانات: {e}")
            messagebox.showerror("خطأ", f"خطأ في عرض البيانات:\n{str(e)}")
    
    def create_data_tab(self, sheet_name: str, df: pd.DataFrame):
        """إنشاء ورقة لعرض البيانات"""
        try:
            # إنشاء إطار للورقة
            tab_frame = ctk.CTkFrame(self.notebook)
            self.notebook.add(tab_frame, text=sheet_name)
            
            # إنشاء Treeview لعرض البيانات
            tree_frame = ctk.CTkFrame(tab_frame)
            tree_frame.pack(fill="both", expand=True, padx=5, pady=5)
            
            # إنشاء Treeview
            columns = list(df.columns)
            tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=20)
            
            # إعداد الأعمدة
            for col in columns:
                tree.heading(col, text=str(col))
                tree.column(col, width=120, anchor="center")
            
            # إضافة البيانات
            for index, row in df.iterrows():
                values = [str(val) if pd.notnull(val) else "" for val in row]
                tree.insert("", "end", values=values)
            
            # إضافة شريط التمرير
            scrollbar_y = ttk.Scrollbar(tree_frame, orient="vertical", command=tree.yview)
            scrollbar_x = ttk.Scrollbar(tree_frame, orient="horizontal", command=tree.xview)
            tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
            
            # ترتيب العناصر
            tree.pack(side="left", fill="both", expand=True)
            scrollbar_y.pack(side="right", fill="y")
            scrollbar_x.pack(side="bottom", fill="x")
            
            # إضافة معلومات الورقة
            info_frame = ctk.CTkFrame(tab_frame, height=40)
            info_frame.pack(fill="x", padx=5, pady=2)
            
            info_text = f"الصفوف: {len(df)} | الأعمدة: {len(df.columns)} | الخلايا الفارغة: {df.isnull().sum().sum()}"
            info_label = ctk.CTkLabel(info_frame, text=info_text, font=ctk.CTkFont(size=10))
            info_label.pack(pady=10)
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء ورقة البيانات: {e}")
    
    def save_file(self):
        """حفظ الملف"""
        try:
            if not self.current_data:
                messagebox.showwarning("تحذير", "لا يوجد بيانات للحفظ")
                return
            
            file_path = filedialog.asksaveasfilename(
                title="حفظ الملف",
                defaultextension=".xlsx",
                filetypes=[
                    ("Excel files", "*.xlsx"),
                    ("All files", "*.*")
                ]
            )
            
            if not file_path:
                return
            
            self.update_status("جاري حفظ الملف...")
            
            success = self.excel_manager.save_excel_file(self.current_data, file_path)
            
            if success:
                self.current_file = file_path
                messagebox.showinfo("نجح", "تم حفظ الملف بنجاح")
                self.update_status("تم حفظ الملف")
            else:
                messagebox.showerror("خطأ", "فشل في حفظ الملف")
                self.update_status("فشل في الحفظ")
                
        except Exception as e:
            self.logger.error(f"خطأ في حفظ الملف: {e}")
            messagebox.showerror("خطأ", f"خطأ في حفظ الملف:\n{str(e)}")
    
    def new_file(self):
        """إنشاء ملف جديد"""
        try:
            # مسح البيانات الحالية
            self.current_file = None
            self.current_data = {}
            
            # مسح الأوراق
            for tab in self.notebook.tabs():
                self.notebook.forget(tab)
            
            # إنشاء ورقة فارغة
            empty_df = pd.DataFrame()
            self.current_data["ورقة جديدة"] = empty_df
            self.create_data_tab("ورقة جديدة", empty_df)
            
            self.file_info_label.configure(text="ملف جديد")
            self.update_status("تم إنشاء ملف جديد")
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء ملف جديد: {e}")
            messagebox.showerror("خطأ", f"خطأ في إنشاء ملف جديد:\n{str(e)}")
    
    def create_camera_report(self):
        """إنشاء تقرير الكاميرات"""
        try:
            # الحصول على بيانات الكاميرات (مثال)
            camera_data = [
                {
                    "brand": "Hikvision",
                    "model": "DS-2CD2142FWD-I",
                    "type": "Fixed Dome",
                    "resolution": "4MP",
                    "confidence": 0.95,
                    "created_at": datetime.now().isoformat(),
                    "notes": "كاميرا عالية الجودة"
                },
                {
                    "brand": "Dahua",
                    "model": "IPC-HDW4431C-A",
                    "type": "Eyeball",
                    "resolution": "4MP",
                    "confidence": 0.88,
                    "created_at": datetime.now().isoformat(),
                    "notes": "مناسبة للاستخدام الداخلي"
                }
            ]
            
            # اختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                title="حفظ تقرير الكاميرات",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx")]
            )
            
            if not file_path:
                return
            
            self.update_status("جاري إنشاء تقرير الكاميرات...")
            
            success = self.excel_manager.create_camera_report(camera_data, file_path)
            
            if success:
                messagebox.showinfo("نجح", "تم إنشاء تقرير الكاميرات بنجاح")
                self.update_status("تم إنشاء التقرير")
                
                # فتح التقرير
                if messagebox.askyesno("فتح التقرير", "هل تريد فتح التقرير الآن؟"):
                    self.load_file_thread(file_path)
            else:
                messagebox.showerror("خطأ", "فشل في إنشاء التقرير")
                self.update_status("فشل في إنشاء التقرير")
                
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء تقرير الكاميرات: {e}")
            messagebox.showerror("خطأ", f"خطأ في إنشاء التقرير:\n{str(e)}")
    
    def create_network_report(self):
        """إنشاء تقرير الشبكة"""
        try:
            # بيانات تجريبية للشبكة
            network_data = [
                {
                    "ip_address": "*************",
                    "mac_address": "00:11:22:33:44:55",
                    "vendor": "Hikvision",
                    "open_ports": [80, 554, 8000],
                    "device_type": "IP Camera",
                    "onvif_support": True,
                    "http_banner": "Hikvision Web Server",
                    "status": "نشط"
                },
                {
                    "ip_address": "*************",
                    "mac_address": "AA:BB:CC:DD:EE:FF",
                    "vendor": "Dahua",
                    "open_ports": [80, 554],
                    "device_type": "IP Camera",
                    "onvif_support": True,
                    "http_banner": "Dahua Web Server",
                    "status": "نشط"
                }
            ]
            
            file_path = filedialog.asksaveasfilename(
                title="حفظ تقرير الشبكة",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx")]
            )
            
            if not file_path:
                return
            
            self.update_status("جاري إنشاء تقرير الشبكة...")
            
            success = self.excel_manager.create_network_report(network_data, file_path)
            
            if success:
                messagebox.showinfo("نجح", "تم إنشاء تقرير الشبكة بنجاح")
                self.update_status("تم إنشاء التقرير")
                
                if messagebox.askyesno("فتح التقرير", "هل تريد فتح التقرير الآن؟"):
                    self.load_file_thread(file_path)
            else:
                messagebox.showerror("خطأ", "فشل في إنشاء التقرير")
                
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء تقرير الشبكة: {e}")
            messagebox.showerror("خطأ", f"خطأ في إنشاء التقرير:\n{str(e)}")
    
    def create_security_report(self):
        """إنشاء تقرير الأمان"""
        try:
            # بيانات تجريبية للثغرات
            security_data = [
                {
                    "cve_id": "CVE-2017-7921",
                    "brand": "Hikvision",
                    "model": "DS-2CD2142FWD-I",
                    "severity": "High",
                    "description": "Authentication bypass vulnerability",
                    "solution": "Update firmware to latest version",
                    "published_date": "2017-04-24"
                },
                {
                    "cve_id": "CVE-2019-3948",
                    "brand": "Dahua",
                    "model": "IPC-HDW4431C-A",
                    "severity": "Medium",
                    "description": "Default credentials vulnerability",
                    "solution": "Change default username and password",
                    "published_date": "2019-07-15"
                }
            ]
            
            file_path = filedialog.asksaveasfilename(
                title="حفظ تقرير الأمان",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx")]
            )
            
            if not file_path:
                return
            
            self.update_status("جاري إنشاء تقرير الأمان...")
            
            success = self.excel_manager.create_security_report(security_data, file_path)
            
            if success:
                messagebox.showinfo("نجح", "تم إنشاء تقرير الأمان بنجاح")
                self.update_status("تم إنشاء التقرير")
                
                if messagebox.askyesno("فتح التقرير", "هل تريد فتح التقرير الآن؟"):
                    self.load_file_thread(file_path)
            else:
                messagebox.showerror("خطأ", "فشل في إنشاء التقرير")
                
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء تقرير الأمان: {e}")
            messagebox.showerror("خطأ", f"خطأ في إنشاء التقرير:\n{str(e)}")
    
    def add_charts(self):
        """إضافة رسوم بيانية"""
        try:
            if not self.current_file:
                messagebox.showwarning("تحذير", "يجب فتح ملف Excel أولاً")
                return
            
            # بيانات تجريبية للرسوم البيانية
            chart_data = {
                "brands": {"Hikvision": 45, "Dahua": 30, "Axis": 15, "Bosch": 10},
                "confidence_levels": {"عالية": 60, "متوسطة": 25, "منخفضة": 15},
                "timeline": {"2024-01": 20, "2024-02": 35, "2024-03": 50}
            }
            
            self.update_status("جاري إضافة الرسوم البيانية...")
            
            success = self.excel_manager.add_charts_to_excel(self.current_file, chart_data)
            
            if success:
                messagebox.showinfo("نجح", "تم إضافة الرسوم البيانية بنجاح")
                self.update_status("تم إضافة الرسوم البيانية")
                
                # إعادة تحميل الملف
                self.load_file_thread(self.current_file)
            else:
                messagebox.showerror("خطأ", "فشل في إضافة الرسوم البيانية")
                
        except Exception as e:
            self.logger.error(f"خطأ في إضافة الرسوم البيانية: {e}")
            messagebox.showerror("خطأ", f"خطأ في إضافة الرسوم البيانية:\n{str(e)}")
    
    def convert_format(self):
        """تحويل صيغة الملف"""
        try:
            if not self.current_file:
                messagebox.showwarning("تحذير", "يجب فتح ملف Excel أولاً")
                return
            
            # نافذة اختيار الصيغ
            format_window = ctk.CTkToplevel(self)
            format_window.title("تحويل الصيغة")
            format_window.geometry("400x300")
            format_window.transient(self)
            format_window.grab_set()
            
            ctk.CTkLabel(format_window, text="اختر الصيغ المطلوبة:", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=20)
            
            # متغيرات الاختيار
            csv_var = tk.BooleanVar()
            json_var = tk.BooleanVar()
            html_var = tk.BooleanVar()
            
            ctk.CTkCheckBox(format_window, text="CSV", variable=csv_var).pack(pady=5)
            ctk.CTkCheckBox(format_window, text="JSON", variable=json_var).pack(pady=5)
            ctk.CTkCheckBox(format_window, text="HTML", variable=html_var).pack(pady=5)
            
            def convert():
                formats = []
                if csv_var.get(): formats.append("csv")
                if json_var.get(): formats.append("json")
                if html_var.get(): formats.append("html")
                
                if not formats:
                    messagebox.showwarning("تحذير", "يجب اختيار صيغة واحدة على الأقل")
                    return
                
                format_window.destroy()
                
                self.update_status("جاري تحويل الملف...")
                
                results = self.excel_manager.convert_to_formats(self.current_file, formats)
                
                if results:
                    message = "تم التحويل بنجاح:\n"
                    for format_type, path in results.items():
                        message += f"• {format_type.upper()}: {os.path.basename(path)}\n"
                    messagebox.showinfo("نجح", message)
                    self.update_status("تم التحويل بنجاح")
                else:
                    messagebox.showerror("خطأ", "فشل في تحويل الملف")
                    self.update_status("فشل في التحويل")
            
            ctk.CTkButton(format_window, text="تحويل", command=convert).pack(pady=20)
            
        except Exception as e:
            self.logger.error(f"خطأ في تحويل الصيغة: {e}")
            messagebox.showerror("خطأ", f"خطأ في تحويل الصيغة:\n{str(e)}")
    
    def validate_file(self):
        """فحص صحة الملف"""
        try:
            if not self.current_file:
                messagebox.showwarning("تحذير", "يجب فتح ملف Excel أولاً")
                return
            
            self.update_status("جاري فحص الملف...")
            
            validation_result = self.excel_manager.validate_excel_file(self.current_file)
            
            # عرض نتائج الفحص
            result_window = ctk.CTkToplevel(self)
            result_window.title("نتائج فحص الملف")
            result_window.geometry("500x400")
            result_window.transient(self)
            result_window.grab_set()
            
            # النتيجة العامة
            status_color = "green" if validation_result["is_valid"] else "red"
            status_text = "صحيح ✅" if validation_result["is_valid"] else "يحتوي على أخطاء ❌"
            
            ctk.CTkLabel(
                result_window,
                text=f"حالة الملف: {status_text}",
                font=ctk.CTkFont(size=16, weight="bold"),
                text_color=status_color
            ).pack(pady=20)
            
            # تفاصيل الملف
            details_frame = ctk.CTkScrollableFrame(result_window)
            details_frame.pack(fill="both", expand=True, padx=20, pady=10)
            
            details_text = f"""
📊 معلومات الملف:
• الحجم: {validation_result['file_size']} بايت
• إجمالي الصفوف: {validation_result['total_rows']}
• عدد الأوراق: {len(validation_result['sheets'])}

📋 تفاصيل الأوراق:
"""
            
            for sheet in validation_result['sheets']:
                details_text += f"• {sheet['name']}: {sheet['rows']} صف، {sheet['columns']} عمود\n"
            
            if validation_result['errors']:
                details_text += f"\n❌ الأخطاء:\n"
                for error in validation_result['errors']:
                    details_text += f"• {error}\n"
            
            if validation_result['warnings']:
                details_text += f"\n⚠️ التحذيرات:\n"
                for warning in validation_result['warnings']:
                    details_text += f"• {warning}\n"
            
            ctk.CTkLabel(
                details_frame,
                text=details_text,
                font=ctk.CTkFont(size=11),
                justify="right"
            ).pack(pady=10)
            
            self.update_status("تم فحص الملف")
            
        except Exception as e:
            self.logger.error(f"خطأ في فحص الملف: {e}")
            messagebox.showerror("خطأ", f"خطأ في فحص الملف:\n{str(e)}")
    
    def show_report_options(self):
        """عرض خيارات التقارير"""
        options_window = ctk.CTkToplevel(self)
        options_window.title("إنشاء تقرير جديد")
        options_window.geometry("400x300")
        options_window.transient(self)
        options_window.grab_set()
        
        ctk.CTkLabel(
            options_window,
            text="اختر نوع التقرير:",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=30)
        
        ctk.CTkButton(
            options_window,
            text="📷 تقرير الكاميرات",
            command=lambda: [options_window.destroy(), self.create_camera_report()],
            width=200,
            height=40
        ).pack(pady=10)
        
        ctk.CTkButton(
            options_window,
            text="🌐 تقرير الشبكة",
            command=lambda: [options_window.destroy(), self.create_network_report()],
            width=200,
            height=40
        ).pack(pady=10)
        
        ctk.CTkButton(
            options_window,
            text="🔒 تقرير الأمان",
            command=lambda: [options_window.destroy(), self.create_security_report()],
            width=200,
            height=40
        ).pack(pady=10)
