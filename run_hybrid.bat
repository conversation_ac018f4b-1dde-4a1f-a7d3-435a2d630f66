@echo off
chcp 65001 >nul
title كاميرا آيـدي - التطبيق الهجين

echo.
echo ===============================================
echo 🎯 كاميرا آيـدي - التطبيق الهجين
echo    Camera ID - Hybrid Application
echo ===============================================
echo.

REM فحص وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo ✅ تم العثور على Python

REM فحص وجود pip
python -m pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير متوفر
    echo يرجى تثبيت pip
    pause
    exit /b 1
)

echo ✅ تم العثور على pip

REM فحص وجود ملف المتطلبات
if not exist "requirements_hybrid.txt" (
    echo ❌ ملف المتطلبات غير موجود: requirements_hybrid.txt
    pause
    exit /b 1
)

echo ✅ ملف المتطلبات موجود

REM فحص وجود التطبيق الرئيسي
if not exist "hybrid_app.py" (
    echo ❌ ملف التطبيق الرئيسي غير موجود: hybrid_app.py
    pause
    exit /b 1
)

if not exist "run_hybrid.py" (
    echo ❌ ملف المشغل غير موجود: run_hybrid.py
    pause
    exit /b 1
)

echo ✅ ملفات التطبيق موجودة

REM عرض القائمة
:menu
echo.
echo ===============================================
echo 🚀 خيارات التشغيل - Launch Options
echo ===============================================
echo.
echo [1] تشغيل هجين (سطح المكتب + ويب)
echo [2] تشغيل سطح المكتب فقط
echo [3] تشغيل الويب فقط
echo [4] تشغيل الويب + فتح المتصفح
echo [5] تثبيت/تحديث المتطلبات
echo [6] فحص المتطلبات
echo [7] عرض معلومات النظام
echo [8] إنشاء اختصار سطح المكتب
echo [9] المساعدة
echo [0] خروج
echo.
set /p choice="اختر رقم الخيار (1-9): "

if "%choice%"=="1" goto hybrid
if "%choice%"=="2" goto desktop
if "%choice%"=="3" goto web
if "%choice%"=="4" goto web_browser
if "%choice%"=="5" goto install
if "%choice%"=="6" goto check
if "%choice%"=="7" goto info
if "%choice%"=="8" goto shortcut
if "%choice%"=="9" goto help
if "%choice%"=="0" goto exit

echo ❌ خيار غير صحيح
goto menu

:hybrid
echo.
echo 🚀 تشغيل التطبيق الهجين...
echo.
python run_hybrid.py --hybrid
goto end

:desktop
echo.
echo 🖥️ تشغيل سطح المكتب فقط...
echo.
python run_hybrid.py --desktop-only
goto end

:web
echo.
echo 🌐 تشغيل خادم الويب فقط...
echo.
set /p port="أدخل رقم المنفذ (افتراضي 5000): "
if "%port%"=="" set port=5000
python run_hybrid.py --web-only --port %port%
goto end

:web_browser
echo.
echo 🌐 تشغيل الويب + فتح المتصفح...
echo.
set /p port="أدخل رقم المنفذ (افتراضي 5000): "
if "%port%"=="" set port=5000
python run_hybrid.py --web-only --port %port% --browser
goto end

:install
echo.
echo 📦 تثبيت/تحديث المتطلبات...
echo.
python run_hybrid.py --install
if errorlevel 1 (
    echo.
    echo ❌ فشل في تثبيت المتطلبات
    echo جاري المحاولة بطريقة مباشرة...
    python -m pip install -r requirements_hybrid.txt
    if errorlevel 1 (
        echo ❌ فشل في التثبيت المباشر أيضاً
        echo يرجى فحص اتصال الإنترنت والمحاولة مرة أخرى
    ) else (
        echo ✅ تم تثبيت المتطلبات بنجاح
    )
) else (
    echo ✅ تم تثبيت المتطلبات بنجاح
)
pause
goto menu

:check
echo.
echo 🔍 فحص المتطلبات...
echo.
python run_hybrid.py --check
pause
goto menu

:info
echo.
echo 🖥️ معلومات النظام...
echo.
python run_hybrid.py --info
pause
goto menu

:shortcut
echo.
echo 🔗 إنشاء اختصار سطح المكتب...
echo.
python run_hybrid.py --shortcut
pause
goto menu

:help
echo.
echo 📖 المساعدة...
echo.
python run_hybrid.py --help-ar
pause
goto menu

:exit
echo.
echo 👋 شكراً لاستخدام كاميرا آيـدي
echo.
exit /b 0

:end
echo.
echo ===============================================
echo ✅ انتهى تشغيل التطبيق
echo ===============================================
echo.
pause

REM العودة للقائمة أم الخروج؟
echo.
set /p return="هل تريد العودة للقائمة الرئيسية؟ (y/n): "
if /i "%return%"=="y" goto menu
if /i "%return%"=="yes" goto menu
if /i "%return%"=="نعم" goto menu

echo.
echo 👋 شكراً لاستخدام كاميرا آيـدي
echo.
exit /b 0
