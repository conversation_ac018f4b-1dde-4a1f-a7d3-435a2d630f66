@echo off
chcp 65001 >nul
title كاميرا آيـدي - التطبيق الهجين - تشغيل مبسط

echo.
echo ===============================================
echo 🎯 كاميرا آيـدي - التطبيق الهجين
echo    Camera ID - Hybrid Application
echo ===============================================
echo.

REM محاولة العثور على Python
set PYTHON_CMD=
set PYTHON_FOUND=0

echo 🔍 البحث عن Python...

REM محاولة python
python --version >nul 2>&1
if not errorlevel 1 (
    set PYTHON_CMD=python
    set PYTHON_FOUND=1
    echo ✅ تم العثور على Python: python
    goto :found_python
)

REM محاولة py
py --version >nul 2>&1
if not errorlevel 1 (
    set PYTHON_CMD=py
    set PYTHON_FOUND=1
    echo ✅ تم العثور على Python: py
    goto :found_python
)

REM محاولة python3
python3 --version >nul 2>&1
if not errorlevel 1 (
    set PYTHON_CMD=python3
    set PYTHON_FOUND=1
    echo ✅ تم العثور على Python: python3
    goto :found_python
)

REM محاولة مسارات شائعة
for %%i in (
    "C:\Python39\python.exe"
    "C:\Python310\python.exe"
    "C:\Python311\python.exe"
    "C:\Python312\python.exe"
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe"
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe"
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe"
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe"
) do (
    if exist %%i (
        set PYTHON_CMD=%%i
        set PYTHON_FOUND=1
        echo ✅ تم العثور على Python في: %%i
        goto :found_python
    )
)

:found_python
if %PYTHON_FOUND%==0 (
    echo.
    echo ❌ لم يتم العثور على Python
    echo.
    echo 📥 يرجى تثبيت Python من:
    echo    https://python.org/downloads/
    echo.
    echo 💡 تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    echo.
    pause
    exit /b 1
)

echo.
echo 📋 فحص إصدار Python...
%PYTHON_CMD% --version

echo.
echo 📁 فحص الملفات المطلوبة...

if not exist "hybrid_app.py" (
    echo ❌ ملف hybrid_app.py غير موجود
    goto :missing_files
)
echo ✅ hybrid_app.py

if not exist "run_hybrid.py" (
    echo ❌ ملف run_hybrid.py غير موجود
    goto :missing_files
)
echo ✅ run_hybrid.py

if not exist "requirements_hybrid.txt" (
    echo ❌ ملف requirements_hybrid.txt غير موجود
    goto :missing_files
)
echo ✅ requirements_hybrid.txt

if not exist "templates" (
    echo ❌ مجلد templates غير موجود
    goto :missing_files
)
echo ✅ مجلد templates

goto :files_ok

:missing_files
echo.
echo ❌ بعض الملفات المطلوبة مفقودة
echo يرجى التأكد من وجود جميع ملفات التطبيق
pause
exit /b 1

:files_ok
echo.
echo ✅ جميع الملفات موجودة

echo.
echo 📦 فحص المكتبات الأساسية...

REM فحص Flask
%PYTHON_CMD% -c "import flask; print('✅ Flask متوفر')" 2>nul
if errorlevel 1 (
    echo ❌ Flask غير مثبت
    set NEED_INSTALL=1
) else (
    echo ✅ Flask متوفر
)

REM فحص CustomTkinter
%PYTHON_CMD% -c "import customtkinter; print('✅ CustomTkinter متوفر')" 2>nul
if errorlevel 1 (
    echo ❌ CustomTkinter غير مثبت
    set NEED_INSTALL=1
) else (
    echo ✅ CustomTkinter متوفر
)

REM فحص Pandas
%PYTHON_CMD% -c "import pandas; print('✅ Pandas متوفر')" 2>nul
if errorlevel 1 (
    echo ❌ Pandas غير مثبت
    set NEED_INSTALL=1
) else (
    echo ✅ Pandas متوفر
)

if defined NEED_INSTALL (
    echo.
    echo 📦 بعض المكتبات مفقودة. هل تريد تثبيتها؟
    set /p install_choice="اكتب 'y' للتثبيت أو 'n' للمتابعة: "
    
    if /i "%install_choice%"=="y" (
        echo.
        echo 📦 تثبيت المكتبات...
        %PYTHON_CMD% -m pip install --upgrade pip
        %PYTHON_CMD% -m pip install flask customtkinter pandas pillow opencv-python sqlalchemy
        
        if errorlevel 1 (
            echo ❌ فشل في تثبيت بعض المكتبات
            echo جاري المحاولة مع الملف الكامل...
            %PYTHON_CMD% -m pip install -r requirements_hybrid.txt
        )
        
        echo ✅ تم تثبيت المكتبات الأساسية
    )
)

echo.
echo ===============================================
echo 🚀 خيارات التشغيل
echo ===============================================
echo.
echo [1] تشغيل هجين (سطح المكتب + ويب)
echo [2] تشغيل سطح المكتب فقط  
echo [3] تشغيل الويب فقط
echo [4] تشغيل الويب + فتح المتصفح
echo [5] اختبار بسيط
echo [0] خروج
echo.

set /p choice="اختر رقم الخيار: "

if "%choice%"=="1" goto :hybrid
if "%choice%"=="2" goto :desktop
if "%choice%"=="3" goto :web
if "%choice%"=="4" goto :web_browser
if "%choice%"=="5" goto :test
if "%choice%"=="0" goto :exit

echo ❌ خيار غير صحيح
pause
goto :files_ok

:hybrid
echo.
echo 🚀 تشغيل التطبيق الهجين...
echo.
%PYTHON_CMD% run_hybrid.py --hybrid
goto :end

:desktop
echo.
echo 🖥️ تشغيل سطح المكتب فقط...
echo.
%PYTHON_CMD% run_hybrid.py --desktop-only
goto :end

:web
echo.
echo 🌐 تشغيل خادم الويب...
echo سيكون متاح على: http://localhost:5000
echo.
%PYTHON_CMD% run_hybrid.py --web-only --port 5000
goto :end

:web_browser
echo.
echo 🌐 تشغيل الويب + فتح المتصفح...
echo.
%PYTHON_CMD% run_hybrid.py --web-only --port 5000 --browser
goto :end

:test
echo.
echo 🧪 تشغيل اختبار بسيط...
echo.
if exist "test_hybrid_simple.py" (
    %PYTHON_CMD% test_hybrid_simple.py
) else (
    echo ❌ ملف الاختبار غير موجود
    echo جاري إنشاء اختبار سريع...
    
    echo import sys > quick_test.py
    echo print("🎯 اختبار سريع للتطبيق الهجين") >> quick_test.py
    echo print("Python version:", sys.version) >> quick_test.py
    echo try: >> quick_test.py
    echo     import flask >> quick_test.py
    echo     print("✅ Flask يعمل") >> quick_test.py
    echo except: >> quick_test.py
    echo     print("❌ Flask لا يعمل") >> quick_test.py
    echo try: >> quick_test.py
    echo     import customtkinter >> quick_test.py
    echo     print("✅ CustomTkinter يعمل") >> quick_test.py
    echo except: >> quick_test.py
    echo     print("❌ CustomTkinter لا يعمل") >> quick_test.py
    echo print("✅ الاختبار انتهى") >> quick_test.py
    
    %PYTHON_CMD% quick_test.py
    del quick_test.py
)
pause
goto :files_ok

:exit
echo.
echo 👋 شكراً لاستخدام كاميرا آيـدي
exit /b 0

:end
echo.
echo ===============================================
echo ✅ انتهى تشغيل التطبيق
echo ===============================================
echo.
pause
exit /b 0
