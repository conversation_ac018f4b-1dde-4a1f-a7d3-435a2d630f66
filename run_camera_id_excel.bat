@echo off
chcp 65001 > nul
title كاميرا آيـدي مع مدير Excel المتقدم

echo ========================================
echo    كاميرا آيـدي - Camera ID
echo    تطبيق تحديد كاميرات المراقبة
echo    مع مدير Excel المتقدم
echo ========================================
echo.

echo 🔍 جاري فحص المتطلبات...

REM فحص Python
py --version >nul 2>&1
if %errorlevel% neq 0 (
    python --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ Python غير مثبت
        echo يرجى تثبيت Python من python.org
        pause
        exit /b 1
    )
    set PYTHON_CMD=python
) else (
    set PYTHON_CMD=py
)

echo ✅ Python متوفر

REM إنشاء مجلدات ضرورية
if not exist "excel_files" mkdir excel_files
if not exist "reports" mkdir reports
if not exist "data" mkdir data

echo ✅ تم إنشاء المجلدات الضرورية

REM فحص المكتبات الأساسية
echo 📦 جاري فحص المكتبات المطلوبة...

%PYTHON_CMD% -c "import customtkinter" >nul 2>&1
if %errorlevel% neq 0 (
    echo تثبيت CustomTkinter...
    %PYTHON_CMD% -m pip install customtkinter
)

%PYTHON_CMD% -c "import cv2" >nul 2>&1
if %errorlevel% neq 0 (
    echo تثبيت OpenCV...
    %PYTHON_CMD% -m pip install opencv-python
)

%PYTHON_CMD% -c "import PIL" >nul 2>&1
if %errorlevel% neq 0 (
    echo تثبيت Pillow...
    %PYTHON_CMD% -m pip install Pillow
)

REM فحص مكتبات Excel
echo 📊 جاري فحص مكتبات Excel...

%PYTHON_CMD% -c "import pandas" >nul 2>&1
if %errorlevel% neq 0 (
    echo تثبيت Pandas...
    %PYTHON_CMD% -m pip install pandas
)

%PYTHON_CMD% -c "import openpyxl" >nul 2>&1
if %errorlevel% neq 0 (
    echo تثبيت OpenPyXL...
    %PYTHON_CMD% -m pip install openpyxl
)

%PYTHON_CMD% -c "import matplotlib" >nul 2>&1
if %errorlevel% neq 0 (
    echo تثبيت Matplotlib...
    %PYTHON_CMD% -m pip install matplotlib
)

%PYTHON_CMD% -c "import cryptography" >nul 2>&1
if %errorlevel% neq 0 (
    echo تثبيت Cryptography...
    %PYTHON_CMD% -m pip install cryptography
)

echo ✅ جميع المكتبات متوفرة

REM إنشاء ملفات Excel تجريبية
echo 📋 إنشاء ملفات Excel تجريبية...
%PYTHON_CMD% create_sample_excel.py >nul 2>&1

echo.
echo ========================================
echo 🚀 جاري تشغيل التطبيق...
echo ========================================
echo.
echo 📋 الميزات المتاحة:
echo    • 🔍 فحص الكاميرات بصرياً
echo    • 🌐 فحص الشبكة
echo    • 📊 مدير Excel المتقدم
echo    • 📄 تقارير PDF احترافية
echo    • 🔒 نظام الأمان والتشفير
echo.
echo 💡 نصائح:
echo    • انقر على "Excel" في شريط القائمة لفتح مدير Excel
echo    • استخدم القوالب الجاهزة لإنشاء تقارير احترافية
echo    • يمكنك رفع ملفات Excel موجودة وتحريرها
echo    • جميع البيانات تبقى محلية على جهازك
echo.

%PYTHON_CMD% start_app.py

echo.
echo 👋 تم إغلاق التطبيق
echo.
echo 📁 الملفات المُنشأة:
echo    • excel_files/ - ملفات Excel والتقارير
echo    • reports/ - تقارير PDF
echo    • data/ - قواعد البيانات المحلية
echo.
echo 📖 للمساعدة:
echo    • راجع ملف "دليل_مدير_Excel.txt"
echo    • راجع ملف "README_EXCEL.md"
echo.
pause
