#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام السجلات
"""

import logging
import os
from datetime import datetime
from pathlib import Path

def setup_logger(name="CameraID", level=logging.INFO):
    """إعداد نظام السجلات"""
    
    # إنشاء مجلد السجلات
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    
    # إنشاء اسم ملف السجل
    log_filename = f"camera_id_{datetime.now().strftime('%Y%m%d')}.log"
    log_path = logs_dir / log_filename
    
    # إعداد التنسيق
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # إعداد معالج الملف
    file_handler = logging.FileHandler(log_path, encoding='utf-8')
    file_handler.setFormatter(formatter)
    file_handler.setLevel(level)
    
    # إعداد معالج وحدة التحكم
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(level)
    
    # إعداد المسجل
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # إزالة المعالجات الموجودة لتجنب التكرار
    logger.handlers.clear()
    
    # إضافة المعالجات
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger
