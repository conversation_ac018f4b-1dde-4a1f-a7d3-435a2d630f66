# 🎯 كاميرا آيـدي - التطبيق الهجين

## نظرة عامة

**كاميرا آيـدي** هو تطبيق هجين متقدم لتحديد نوع وموديل كاميرات المراقبة باستخدام الذكاء الاصطناعي والفحص الشبكي. يعمل على **سطح المكتب والويب معاً** مما يوفر مرونة كاملة في الاستخدام.

## 🌟 الميزات الرئيسية

### 🖥️ **التطبيق الهجين**
- ✅ **سطح المكتب**: واجهة CustomTkinter حديثة وسريعة
- ✅ **الويب**: واجهة Flask متجاوبة تعمل على أي متصفح  
- ✅ **التكامل**: مشاركة البيانات والإعدادات بين الواجهتين
- ✅ **المرونة**: إمكانية التشغيل في أي وضع حسب الحاجة

### 🔍 **الفحص الذكي**
- 📷 **الفحص البصري**: تحليل الصور باستخدام الذكاء الاصطناعي
- 🌐 **الفحص الشبكي**: اكتشاف كاميرات IP في الشبكة
- 🔌 **ONVIF**: دعم بروتوكول ONVIF للكاميرات المتقدمة
- 🏷️ **MAC Lookup**: تحديد الشركة المصنعة من عنوان MAC

### 📊 **إدارة البيانات**
- 🗄️ **قاعدة بيانات SQLite**: تخزين محلي آمن
- 📈 **تكامل Excel**: استيراد وتصدير البيانات
- 📋 **تقارير شاملة**: تقارير PDF و HTML
- 📊 **إحصائيات مفصلة**: تحليل شامل للنتائج

### 🔒 **الأمان والخصوصية**
- 🔐 **تشفير البيانات**: حماية كاملة للمعلومات الحساسة
- 🏠 **عدم الاتصال بالإنترنت**: جميع العمليات محلية
- 👤 **مصادقة المستخدمين**: نظام تسجيل دخول آمن
- 📝 **تدقيق العمليات**: سجل شامل لجميع الأنشطة

## 🚀 التثبيت والتشغيل

### المتطلبات الأساسية

#### 1. تثبيت Python
**على Windows:**
1. اذهب إلى [python.org](https://python.org/downloads/)
2. حمل Python 3.8 أو أحدث
3. **مهم**: تأكد من تحديد "Add Python to PATH" أثناء التثبيت
4. تأكد من التثبيت بفتح Command Prompt وكتابة: `python --version`

**على Linux:**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3 python3-pip

# CentOS/RHEL
sudo yum install python3 python3-pip
```

#### 2. متطلبات النظام
- **نظام التشغيل**: Windows 10/11 أو Linux
- **الذاكرة**: 4GB RAM (8GB مستحسن)
- **المساحة**: 2GB مساحة فارغة
- **الشبكة**: اتصال إنترنت لتثبيت المكتبات

### التثبيت السريع

#### الطريقة الأولى: التشغيل التلقائي (Windows)
1. **حمل الملفات** أو انسخها لمجلد جديد
2. **انقر نقراً مزدوجاً** على `run_hybrid.bat`
3. **اختر الخيار 5** لتثبيت المتطلبات
4. **اختر الخيار 1** للتشغيل الهجين

#### الطريقة الثانية: التثبيت اليدوي
```bash
# 1. فتح Terminal/Command Prompt في مجلد التطبيق
cd path/to/camera-id-hybrid

# 2. تثبيت المتطلبات
pip install -r requirements_hybrid.txt

# 3. تشغيل الاختبار
python test_hybrid_simple.py

# 4. تشغيل التطبيق
python run_hybrid.py --hybrid
```

### خيارات التشغيل

#### 🖥️ التشغيل الهجين (افتراضي)
```bash
python run_hybrid.py --hybrid
```
- يشغل واجهة سطح المكتب
- يمكن تشغيل خادم الويب من داخل التطبيق
- **أفضل خيار للاستخدام الشخصي**

#### 🖥️ سطح المكتب فقط
```bash
python run_hybrid.py --desktop-only
```
- واجهة سطح المكتب فقط
- استهلاك أقل للموارد
- مناسب للأجهزة المحدودة

#### 🌐 الويب فقط
```bash
python run_hybrid.py --web-only --port 5000
```
- خادم ويب فقط
- مناسب للخوادم
- يمكن الوصول إليه من أي جهاز في الشبكة

#### 🌐 الويب + المتصفح
```bash
python run_hybrid.py --web-only --browser --port 8080
```
- يشغل الخادم ويفتح المتصفح تلقائياً
- مناسب للاستخدام السريع

## 📱 كيفية الاستخدام

### 🖥️ واجهة سطح المكتب

#### البدء السريع:
1. **شغل التطبيق**: `python run_hybrid.py --hybrid`
2. **انقر على "تشغيل خادم الويب"** لتفعيل الواجهة الويب
3. **انقر على "فتح في المتصفح"** للوصول للواجهة الويب
4. **استخدم الأزرار** للفحص البصري أو الشبكي

#### الفحص البصري:
1. انقر على **"فحص بصري"**
2. اختر صورة الكاميرا من جهازك
3. انتظر التحليل (عادة أقل من 10 ثوان)
4. اعرض النتائج مع درجة الثقة

#### الفحص الشبكي:
1. انقر على **"فحص شبكي"**
2. أدخل نطاق الشبكة (مثل: `***********/24`)
3. انتظر اكتمال الفحص
4. اعرض الأجهزة المكتشفة مع تفاصيلها

### 🌐 واجهة الويب

#### الوصول:
- **محلياً**: `http://localhost:5000`
- **من الشبكة**: `http://[IP-ADDRESS]:5000`

#### الميزات:
- 📊 **لوحة تحكم**: إحصائيات مباشرة ونشاط حديث
- 📷 **فحص بصري**: رفع الصور بالسحب والإفلات
- 🌐 **فحص شبكي**: إعدادات متقدمة وتقدم مباشر
- 📋 **النتائج**: عرض وتصدير جميع النتائج
- ⚙️ **الإعدادات**: تخصيص التطبيق

## 🔧 الإعدادات المتقدمة

### ملف الإعدادات (config.yaml)
```yaml
# إعدادات التطبيق
app:
  name: "كاميرا آيـدي"
  version: "2.0.0"
  language: "ar"
  theme: "dark"

# إعدادات الفحص
scanning:
  visual:
    confidence_threshold: 0.7
    max_image_size: "10MB"
  network:
    default_ports: [80, 554, 8080, 37777]
    timeout: 3
    max_threads: 50
```

### متغيرات البيئة (.env)
```bash
# أمان
SECRET_KEY=your-secret-key-here
JWT_SECRET=your-jwt-secret-here

# قاعدة البيانات
DATABASE_URL=sqlite:///data/camera_id_hybrid.db

# خادم الويب
FLASK_ENV=production
FLASK_DEBUG=false
```

## 🛠️ استكشاف الأخطاء

### المشاكل الشائعة

#### ❌ "python is not recognized"
**الحل:**
1. تأكد من تثبيت Python بشكل صحيح
2. أضف Python إلى PATH:
   - Windows: إعدادات النظام → متغيرات البيئة
   - Linux: أضف إلى `.bashrc` أو `.zshrc`

#### ❌ خطأ في تثبيت المتطلبات
**الحل:**
```bash
# تحديث pip
python -m pip install --upgrade pip

# تثبيت بدون cache
pip install -r requirements_hybrid.txt --no-cache-dir

# تثبيت واحد تلو الآخر
pip install flask customtkinter pandas pillow opencv-python
```

#### ❌ خطأ في تشغيل الكاميرا
**الحل:**
- تأكد من وجود كاميرا متصلة
- فحص أذونات الوصول للكاميرا
- إعادة تشغيل التطبيق

#### ❌ "Port already in use"
**الحل:**
```bash
# استخدام منفذ مختلف
python run_hybrid.py --web-only --port 8080

# أو إيقاف العملية المستخدمة للمنفذ
netstat -ano | findstr :5000
taskkill /PID [PID_NUMBER] /F
```

### فحص الحالة
```bash
# فحص المتطلبات
python run_hybrid.py --check

# عرض معلومات النظام
python run_hybrid.py --info

# اختبار بسيط
python test_hybrid_simple.py
```

## 📊 الملفات والمجلدات

```
camera-id-hybrid/
├── 📄 hybrid_app.py              # التطبيق الرئيسي
├── 📄 run_hybrid.py              # مشغل التطبيق
├── 📄 run_hybrid.bat             # مشغل Windows
├── 📄 requirements_hybrid.txt    # المتطلبات
├── 📄 test_hybrid_simple.py      # اختبار بسيط
├── 📁 templates/                 # قوالب HTML
│   ├── base.html
│   └── index.html
├── 📁 static/                    # ملفات CSS/JS
├── 📁 data/                      # قاعدة البيانات
├── 📁 uploads/                   # الصور المرفوعة
├── 📁 exports/                   # التصديرات
└── 📁 src/                       # كود المصدر
    ├── gui/                      # واجهات المستخدم
    ├── core/                     # الوظائف الأساسية
    ├── ai/                       # الذكاء الاصطناعي
    └── security/                 # الأمان
```

## 🚀 الميزات المتقدمة

### API المتقدم
```python
import requests

# فحص بصري
files = {'image': open('camera.jpg', 'rb')}
response = requests.post('http://localhost:5000/api/scan/visual', files=files)
result = response.json()

# فحص شبكي
data = {'ip_range': '***********/24', 'ports': [80, 554]}
response = requests.post('http://localhost:5000/api/scan/network', json=data)
results = response.json()
```

### التكامل مع Excel
- **استيراد**: قراءة ملفات Excel موجودة
- **تصدير**: حفظ النتائج في Excel مع رسوم بيانية
- **قوالب**: قوالب جاهزة للتقارير المختلفة

### الأمان المتقدم
- **تشفير AES-256**: جميع البيانات الحساسة
- **JWT**: مصادقة آمنة للجلسات
- **Rate Limiting**: حماية من الهجمات
- **CORS**: تحكم في الوصول للموارد

## 📞 الدعم والمساعدة

### الحصول على المساعدة
```bash
# المساعدة المدمجة
python run_hybrid.py --help-ar

# فحص السجلات
type hybrid_launcher.log

# اختبار النظام
python run_hybrid.py --info
```

### الإبلاغ عن المشاكل
1. فحص السجلات في `hybrid_launcher.log`
2. تجربة إعادة التشغيل
3. فحص المتطلبات: `python run_hybrid.py --check`
4. إرسال تقرير مفصل مع:
   - نظام التشغيل
   - إصدار Python
   - رسالة الخطأ الكاملة
   - خطوات إعادة إنتاج المشكلة

## 🎯 أمثلة سريعة

### تشغيل سريع للاختبار
```bash
# Windows
run_hybrid.bat

# Linux/Mac
python run_hybrid.py --web-only --browser
```

### فحص شبكة محلية
```bash
# تشغيل فحص شبكي سريع
python -c "
from hybrid_app import HybridCameraIDApp
app = HybridCameraIDApp()
results = app.network_scan('***********/24', [80, 554])
print(f'تم العثور على {len(results)} جهاز')
"
```

### تصدير النتائج
```bash
# تصدير جميع النتائج
curl http://localhost:5000/api/export
```

## 🌟 الخلاصة

**كاميرا آيـدي** هو تطبيق هجين متقدم يجمع بين:
- 🖥️ **قوة سطح المكتب**: أداء سريع وواجهة غنية
- 🌐 **مرونة الويب**: وصول من أي مكان وجهاز
- 🔒 **أمان عالي**: خصوصية كاملة وتشفير متقدم
- 🎯 **سهولة الاستخدام**: واجهة عربية بديهية

**🚀 ابدأ الآن واكتشف قوة التطبيق الهجين!**

---

## 📝 ملاحظات مهمة

- ✅ **التطبيق يعمل بالكامل محلياً** - لا يرسل بيانات للإنترنت
- ✅ **دعم كامل للغة العربية** - واجهة وتوثيق عربي
- ✅ **مفتوح المصدر** - يمكن تعديله وتطويره
- ✅ **متعدد المنصات** - Windows, Linux, Mac
- ✅ **تحديثات مستمرة** - ميزات جديدة بانتظام

---

*© 2024 كاميرا آيـدي. جميع الحقوق محفوظة.*
