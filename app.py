#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق كاميرا آيـدي - Camera ID Web Application
تطبيق ويب لتحديد نوع وموديل كاميرات المراقبة
"""

from flask import Flask, render_template, request, jsonify, flash, redirect, url_for, session
from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed, FileRequired
from wtforms import StringField, TextAreaField, SelectField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Optional
from werkzeug.utils import secure_filename
import os
import json
import threading
import time
from datetime import datetime
import pandas as pd
import openpyxl
from openpyxl import Workbook

# Import our modules
from src.core.config import Config
from src.utils.logger import setup_logger

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'camera-id-web-secret-key-2024'
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
app.config['WTF_CSRF_ENABLED'] = True

# Create upload folder if it doesn't exist
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Initialize config and logger
config = Config()
logger = setup_logger()

# Excel Database Manager
class ExcelDatabase:
    def __init__(self, filename="camera_database.xlsx"):
        self.filename = filename
        self.ensure_database()

    def ensure_database(self):
        """إنشاء قاعدة البيانات إذا لم تكن موجودة"""
        if not os.path.exists(self.filename):
            wb = Workbook()
            ws = wb.active
            ws.title = "Cameras"
            # Headers
            headers = ["ID", "Timestamp", "Scan_Type", "IP_Address", "Model", "Brand",
                      "Confidence", "Features", "Vulnerabilities", "Notes"]
            for col, header in enumerate(headers, 1):
                ws.cell(row=1, column=col, value=header)
            wb.save(self.filename)
            logger.info(f"Created new Excel database: {self.filename}")

    def save_scan_result(self, scan_type, result):
        """حفظ نتيجة الفحص في Excel"""
        try:
            wb = openpyxl.load_workbook(self.filename)
            ws = wb.active

            # Find next empty row
            next_row = ws.max_row + 1

            # Generate ID
            scan_id = f"{scan_type.upper()}_{int(time.time())}"

            # Prepare data
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            if isinstance(result, dict):
                ip = result.get('ip', '')
                model = result.get('model', '')
                brand = result.get('brand', '')
                confidence = result.get('confidence', 0)
                features = ', '.join(result.get('features', []))
                vulnerabilities = ', '.join(result.get('vulnerabilities', []))
            else:
                ip = model = brand = ''
                confidence = 0
                features = vulnerabilities = ''

            # Write data
            data = [scan_id, timestamp, scan_type, ip, model, brand,
                   confidence, features, vulnerabilities, ""]

            for col, value in enumerate(data, 1):
                ws.cell(row=next_row, column=col, value=value)

            wb.save(self.filename)
            logger.info(f"Saved scan result to Excel: {scan_id}")
            return scan_id

        except Exception as e:
            logger.error(f"Error saving to Excel: {e}")
            return None

    def get_all_records(self):
        """الحصول على جميع السجلات"""
        try:
            df = pd.read_excel(self.filename)
            return df.to_dict('records')
        except Exception as e:
            logger.error(f"Error reading Excel: {e}")
            return []

    def search_records(self, query):
        """البحث في السجلات"""
        try:
            df = pd.read_excel(self.filename)
            # Search in model, brand, and IP columns
            mask = (
                df['Model'].str.contains(query, case=False, na=False) |
                df['Brand'].str.contains(query, case=False, na=False) |
                df['IP_Address'].str.contains(query, case=False, na=False)
            )
            return df[mask].to_dict('records')
        except Exception as e:
            logger.error(f"Error searching Excel: {e}")
            return []

# Initialize Excel database
excel_db = ExcelDatabase()

# Forms
class ImageUploadForm(FlaskForm):
    image = FileField('صورة الكاميرا',
                     validators=[FileRequired(), FileAllowed(['jpg', 'jpeg', 'png', 'bmp', 'tiff'],
                                                            'صيغ الصور المسموحة فقط!')])
    submit = SubmitField('تحليل الصورة')

class NetworkScanForm(FlaskForm):
    ip_range = StringField('نطاق الشبكة',
                          validators=[DataRequired()],
                          default='***********/24')
    ports = StringField('المنافذ',
                       validators=[DataRequired()],
                       default='80,554,8080,37777')
    onvif_scan = BooleanField('فحص ONVIF', default=True)
    http_scan = BooleanField('فحص HTTP', default=True)
    mac_scan = BooleanField('فحص MAC Address', default=True)
    submit = SubmitField('بدء الفحص')

# Routes
@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template('index.html')

@app.route('/scan')
def scan():
    """صفحة اختيار نوع الفحص"""
    return render_template('scan.html')

@app.route('/visual_scan', methods=['GET', 'POST'])
def visual_scan():
    """فحص بصري"""
    form = ImageUploadForm()
    result = None

    if form.validate_on_submit():
        # حفظ الصورة المرفوعة
        filename = secure_filename(form.image.data.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        form.image.data.save(filepath)

        # تحليل الصورة
        result = analyze_image(filepath)

        # حفظ النتيجة في Excel
        if result:
            excel_db.save_scan_result('visual', result)

        # حذف الصورة المؤقتة
        os.remove(filepath)

    return render_template('visual_scan.html', form=form, result=result)

@app.route('/network_scan', methods=['GET', 'POST'])
def network_scan():
    """فحص شبكي"""
    form = NetworkScanForm()
    results = []
    scanning = False

    if form.validate_on_submit():
        scanning = True
        # تشغيل الفحص في خيط منفصل
        thread = threading.Thread(target=network_scan_thread,
                                args=(form, results))
        thread.daemon = True
        thread.start()

    return render_template('network_scan.html', form=form,
                         results=results, scanning=scanning)

@app.route('/results')
def results():
    """عرض النتائج"""
    scan_type = request.args.get('type', 'unknown')
    # في التطبيق الحقيقي، سنحصل على النتائج من session أو database
    return render_template('results.html', scan_type=scan_type)

@app.route('/database')
def database():
    """عرض قاعدة البيانات"""
    records = excel_db.get_all_records()
    return render_template('database.html', records=records)

@app.route('/api/search')
def api_search():
    """API للبحث في قاعدة البيانات"""
    query = request.args.get('q', '')
    if query:
        results = excel_db.search_records(query)
    else:
        results = excel_db.get_all_records()

    return jsonify(results)

@app.route('/api/export')
def api_export():
    """تصدير البيانات إلى Excel"""
    try:
        records = excel_db.get_all_records()
        # إنشاء ملف Excel جديد للتصدير
        export_filename = f"camera_export_{int(time.time())}.xlsx"
        export_path = os.path.join('exports', export_filename)
        os.makedirs('exports', exist_ok=True)

        df = pd.DataFrame(records)
        df.to_excel(export_path, index=False)

        return jsonify({'success': True, 'filename': export_filename})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# Helper Functions
def analyze_image(image_path):
    """تحليل الصورة وتحديد نوع الكاميرا"""
    # تحميل قاعدة بيانات الكاميرات
    cameras_db_path = os.path.join("data", "cameras.json")

    try:
        with open(cameras_db_path, 'r', encoding='utf-8') as f:
            cameras_data = json.load(f)
    except:
        return {"error": "فشل في تحميل قاعدة بيانات الكاميرات"}

    # تحليل بسيط للصورة (يمكن تحسينه باستخدام ML)
    result = simple_image_analysis(image_path)

    # البحث عن تطابق في قاعدة البيانات
    best_match = None
    best_confidence = 0.0

    if "cameras" in cameras_data:
        for brand_data in cameras_data["cameras"]:
            brand = brand_data["brand"]
            for model_data in brand_data["models"]:
                model = model_data["model"]

                # حساب درجة التطابق البسيطة
                confidence = calculate_match_confidence(result, model_data)

                if confidence > best_confidence:
                    best_confidence = confidence
                    best_match = {
                        "brand": brand,
                        "model": model,
                        "confidence": confidence,
                        "features": model_data.get("features", []),
                        "vulnerabilities": model_data.get("vulnerabilities", [])
                    }

    # إذا لم يتم العثور على تطابق جيد، إرجاع نتيجة عامة
    if best_match is None or best_confidence < 0.3:
        best_match = {
            "brand": "غير محدد",
            "model": "كاميرا IP عامة",
            "confidence": 0.5,
            "features": ["كاميرا مراقبة"],
            "vulnerabilities": ["Default credentials", "Weak Encryption"]
        }

    return best_match

def simple_image_analysis(image_path):
    """تحليل بسيط للصورة"""
    from PIL import Image
    result = {
        "has_camera_features": False,
        "resolution": "unknown",
        "color_space": "unknown",
        "aspect_ratio": 1.0
    }

    try:
        image = Image.open(image_path)
        width, height = image.size
        result["resolution"] = f"{width}x{height}"
        result["aspect_ratio"] = width / height if height > 0 else 1.0
        result["color_space"] = image.mode

        # كشف ميزات بسيطة للكاميرا
        result["has_camera_features"] = detect_camera_features(image)

    except Exception as e:
        logger.error(f"خطأ في تحليل الصورة: {e}")

    return result

def detect_camera_features(image):
    """كشف ميزات الكاميرا في الصورة"""
    try:
        # تحويل إلى grayscale وتحليل التباين
        grayscale = image.convert('L')
        pixels = list(grayscale.getdata())
        avg_brightness = sum(pixels) / len(pixels)
        contrast = max(pixels) - min(pixels)

        # كشف أساسي للكاميرات
        return contrast > 50 and avg_brightness > 30
    except:
        return False

def calculate_match_confidence(image_analysis, model_data):
    """حساب درجة التطابق بين تحليل الصورة وبيانات النموذج"""
    confidence = 0.0

    # تطابق الميزات
    if image_analysis.get("has_camera_features"):
        confidence += 0.3

    # تطابق الدقة (إذا كانت متوفرة)
    model_features = model_data.get("features", [])
    if any("MP" in str(feature) for feature in model_features):
        confidence += 0.2

    # تطابق عام
    confidence += 0.5  # قاعدة أساسية

    return min(confidence, 1.0)

def network_scan_thread(form, results):
    """تشغيل فحص الشبكة في خيط منفصل"""
    try:
        import socket
        import ipaddress
        import requests

        # الحصول على الإعدادات
        ip_range = form.ip_range.data
        ports_str = form.ports.data

        # التحقق من صحة المدخلات
        try:
            network = ipaddress.ip_network(ip_range, strict=False)
            ports = [int(p.strip()) for p in ports_str.split(',') if p.strip()]
        except Exception as e:
            logger.error(f"خطأ في المدخلات: {e}")
            return

        # فحص الشبكة
        hosts = list(network.hosts())

        for host in hosts[:50]:  # محدود لـ50 مضيف كحد أقصى للويب
            host_ip = str(host)

            # فحص المنافذ
            open_ports = []
            for port in ports:
                if check_port(host_ip, port):
                    open_ports.append(port)

            if not open_ports:
                continue

            # فحص نوع الجهاز
            device_info = identify_device(host_ip, open_ports, form)

            if device_info:
                result = {
                    "ip": host_ip,
                    "port": open_ports[0],
                    "model": device_info.get("model", "كاميرا IP"),
                    "confidence": device_info.get("confidence", 0.8)
                }
                results.append(result)

                # حفظ في Excel
                excel_db.save_scan_result('network', result)

    except Exception as e:
        logger.error(f"خطأ في فحص الشبكة: {e}")

def check_port(ip, port):
    """فحص منفذ محدد"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((ip, port))
        sock.close()
        return result == 0
    except:
        return False

def identify_device(ip, ports, form):
    """تحديد نوع الجهاز"""
    # فحص HTTP
    if form.http_scan.data:
        device_info = check_http_device(ip, ports)
        if device_info:
            return device_info

    # إذا لم يتم العثور على معلومات محددة
    return {
        "model": "كاميرا IP عامة",
        "confidence": 0.5
    }

def check_http_device(ip, ports):
    """فحص جهاز HTTP"""
    http_ports = [p for p in ports if p in [80, 8080, 8000, 8899]]

    for port in http_ports:
        try:
            url = f"http://{ip}:{port}"
            response = requests.get(url, timeout=3, allow_redirects=True)

            content = response.text.lower()

            # البحث عن علامات الكاميرات
            camera_keywords = ['camera', 'webcam', 'ipcam', 'surveillance', 'hikvision', 'dahua', 'axis']

            if any(keyword in content for keyword in camera_keywords):
                # تحديد الماركة
                brand = "غير محدد"
                if 'hikvision' in content:
                    brand = "Hikvision"
                elif 'dahua' in content:
                    brand = "Dahua"
                elif 'axis' in content:
                    brand = "Axis"

                return {
                    "model": f"{brand} كاميرا IP",
                    "confidence": 0.8
                }

        except:
            continue

    return None

if __name__ == '__main__':
    logger.info("بدء تشغيل تطبيق كاميرا آيـدي الويب")
    app.run(debug=True, host='0.0.0.0', port=5000)