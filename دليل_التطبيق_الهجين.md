# 🎯 دليل التطبيق الهجين - كاميرا آيـدي

## نظرة عامة

**كاميرا آيـدي** هو تطبيق هجين متقدم لتحديد نوع وموديل كاميرات المراقبة باستخدام تقنيات الذكاء الاصطناعي والفحص الشبكي. يعمل التطبيق على **سطح المكتب والويب معاً** مما يوفر مرونة كاملة في الاستخدام.

---

## 🌟 الميزات الرئيسية

### 🖥️ **التطبيق الهجين**
- **سطح المكتب**: واجهة CustomTkinter حديثة وسريعة
- **الويب**: واجهة Flask متجاوبة تعمل على أي متصفح
- **التكامل**: مشاركة البيانات والإعدادات بين الواجهتين
- **المرونة**: إمكانية التشغيل في أي وضع حسب الحاجة

### 🔍 **الفحص الذكي**
- **الفحص البصري**: تحليل الصور باستخدام الذكاء الاصطناعي
- **الفحص الشبكي**: اكتشاف كاميرات IP في الشبكة
- **ONVIF**: دعم بروتوكول ONVIF للكاميرات المتقدمة
- **MAC Lookup**: تحديد الشركة المصنعة من عنوان MAC

### 📊 **إدارة البيانات**
- **قاعدة بيانات SQLite**: تخزين محلي آمن
- **تكامل Excel**: استيراد وتصدير البيانات
- **تقارير شاملة**: تقارير PDF و HTML
- **إحصائيات مفصلة**: تحليل شامل للنتائج

### 🔒 **الأمان والخصوصية**
- **تشفير البيانات**: حماية كاملة للمعلومات الحساسة
- **عدم الاتصال بالإنترنت**: جميع العمليات محلية
- **مصادقة المستخدمين**: نظام تسجيل دخول آمن
- **تدقيق العمليات**: سجل شامل لجميع الأنشطة

---

## 🚀 التثبيت والتشغيل

### المتطلبات الأساسية
- **Python 3.8+**
- **Windows 10/11** أو **Linux**
- **4GB RAM** (8GB مستحسن)
- **2GB مساحة فارغة**

### التثبيت السريع

#### على Windows:
```batch
# 1. تحميل الملفات
git clone https://github.com/your-repo/camera-id-hybrid.git
cd camera-id-hybrid

# 2. تشغيل الملف التلقائي
run_hybrid.bat
```

#### على Linux:
```bash
# 1. تحميل الملفات
git clone https://github.com/your-repo/camera-id-hybrid.git
cd camera-id-hybrid

# 2. تثبيت المتطلبات
pip install -r requirements_hybrid.txt

# 3. تشغيل التطبيق
python run_hybrid.py --hybrid
```

### خيارات التشغيل

#### 1. التشغيل الهجين (افتراضي)
```bash
python run_hybrid.py --hybrid
```
- يشغل واجهة سطح المكتب
- يمكن تشغيل خادم الويب من داخل التطبيق
- أفضل خيار للاستخدام الشخصي

#### 2. سطح المكتب فقط
```bash
python run_hybrid.py --desktop-only
```
- واجهة سطح المكتب فقط
- استهلاك أقل للموارد
- مناسب للأجهزة المحدودة

#### 3. الويب فقط
```bash
python run_hybrid.py --web-only --port 5000
```
- خادم ويب فقط
- مناسب للخوادم
- يمكن الوصول إليه من أي جهاز في الشبكة

#### 4. الويب + المتصفح
```bash
python run_hybrid.py --web-only --browser --port 8080
```
- يشغل الخادم ويفتح المتصفح تلقائياً
- مناسب للاستخدام السريع

---

## 📱 واجهات الاستخدام

### 🖥️ **واجهة سطح المكتب**

#### الشاشة الرئيسية
- **العنوان**: كاميرا آيـدي - التطبيق الهجين
- **أزرار التحكم**:
  - 🌐 تشغيل خادم الويب
  - 🔗 فتح في المتصفح
  - 📷 فحص بصري
  - 🌐 فحص شبكي
- **منطقة المعلومات**: عرض النشاط والنتائج

#### الفحص البصري
1. انقر على "فحص بصري"
2. اختر صورة الكاميرا
3. انتظر التحليل
4. اعرض النتائج

#### الفحص الشبكي
1. انقر على "فحص شبكي"
2. أدخل نطاق الشبكة (مثل: ***********/24)
3. انتظر اكتمال الفحص
4. اعرض الأجهزة المكتشفة

### 🌐 **واجهة الويب**

#### الصفحة الرئيسية
- **إحصائيات مباشرة**: عدد الفحوصات والأجهزة
- **النشاط الأخير**: آخر العمليات المنجزة
- **إجراءات سريعة**: أزرار للعمليات الشائعة

#### صفحة الفحص البصري
- **رفع الصور**: سحب وإفلات أو تصفح
- **معاينة مباشرة**: عرض الصورة قبل التحليل
- **نتائج تفاعلية**: عرض مفصل مع الثقة

#### صفحة الفحص الشبكي
- **إعدادات متقدمة**: نطاق IP، منافذ، مهلة زمنية
- **تقدم مباشر**: شريط تقدم مع التفاصيل
- **خريطة الشبكة**: عرض بصري للأجهزة

---

## 🔧 الإعدادات والتخصيص

### ملف الإعدادات (config.yaml)
```yaml
# إعدادات التطبيق
app:
  name: "كاميرا آيـدي"
  version: "2.0.0"
  language: "ar"
  theme: "dark"

# إعدادات قاعدة البيانات
database:
  type: "sqlite"
  path: "data/camera_id_hybrid.db"
  backup: true

# إعدادات الأمان
security:
  encryption: true
  session_timeout: 3600
  max_login_attempts: 5

# إعدادات الفحص
scanning:
  visual:
    confidence_threshold: 0.7
    max_image_size: "10MB"
  network:
    default_ports: [80, 554, 8080, 37777]
    timeout: 3
    max_threads: 50
```

### تخصيص الواجهة
- **الألوان**: تعديل ملف CSS للويب
- **الخطوط**: دعم الخطوط العربية
- **اللغة**: إضافة لغات جديدة
- **الأيقونات**: استخدام Font Awesome

---

## 📊 إدارة البيانات

### قاعدة البيانات
- **SQLite**: قاعدة بيانات محلية سريعة
- **جداول رئيسية**:
  - `scan_results`: نتائج الفحص
  - `users`: المستخدمين
  - `settings`: الإعدادات
  - `audit_log`: سجل العمليات

### تكامل Excel
- **استيراد**: قراءة ملفات Excel موجودة
- **تصدير**: حفظ النتائج في Excel
- **قوالب**: قوالب جاهزة للتقارير
- **رسوم بيانية**: إضافة تلقائية للمخططات

### النسخ الاحتياطي
```bash
# نسخ احتياطي يدوي
python run_hybrid.py --backup

# استعادة من نسخة احتياطية
python run_hybrid.py --restore backup_file.zip
```

---

## 🔍 دليل الاستكشاف

### المشاكل الشائعة

#### 1. خطأ في تثبيت المتطلبات
```bash
# الحل
pip install --upgrade pip
pip install -r requirements_hybrid.txt --no-cache-dir
```

#### 2. خطأ في تشغيل الكاميرا
- تأكد من وجود كاميرا متصلة
- فحص أذونات الوصول للكاميرا
- إعادة تشغيل التطبيق

#### 3. خطأ في الاتصال بالشبكة
- فحص إعدادات الجدار الناري
- التأكد من صحة نطاق IP
- فحص أذونات الشبكة

#### 4. بطء في الأداء
- إغلاق التطبيقات الأخرى
- زيادة الذاكرة المخصصة
- تحديث برامج التشغيل

### رسائل الخطأ

| الرسالة | السبب | الحل |
|---------|--------|------|
| `ModuleNotFoundError` | مكتبة مفقودة | `pip install -r requirements_hybrid.txt` |
| `Permission denied` | أذونات غير كافية | تشغيل كمدير |
| `Port already in use` | المنفذ مستخدم | تغيير رقم المنفذ |
| `Database locked` | قاعدة البيانات مقفلة | إعادة تشغيل التطبيق |

---

## 🛡️ الأمان والخصوصية

### حماية البيانات
- **تشفير AES-256**: جميع البيانات الحساسة مشفرة
- **تخزين محلي**: لا يتم إرسال بيانات للإنترنت
- **كلمات مرور آمنة**: تشفير PBKDF2 للكلمات
- **جلسات آمنة**: انتهاء صلاحية تلقائي

### أفضل الممارسات
1. **تحديث منتظم**: تحديث التطبيق والمتطلبات
2. **نسخ احتياطية**: نسخ دورية لقاعدة البيانات
3. **كلمات مرور قوية**: استخدام كلمات معقدة
4. **فحص الشبكة بحذر**: احترام خصوصية الآخرين

---

## 🚀 الميزات المتقدمة

### API المتقدم
```python
# مثال على استخدام API
import requests

# فحص بصري
files = {'image': open('camera.jpg', 'rb')}
response = requests.post('http://localhost:5000/api/scan/visual', files=files)
result = response.json()

# فحص شبكي
data = {'ip_range': '***********/24', 'ports': [80, 554]}
response = requests.post('http://localhost:5000/api/scan/network', json=data)
results = response.json()
```

### التكامل مع أنظمة أخرى
- **REST API**: واجهة برمجية شاملة
- **WebSocket**: تحديثات مباشرة
- **CSV/JSON**: تصدير للأنظمة الأخرى
- **Database**: اتصال مباشر بقاعدة البيانات

### الإضافات والتوسعات
- **نماذج AI مخصصة**: إضافة نماذج جديدة
- **قوالب تقارير**: تصميم تقارير مخصصة
- **لغات جديدة**: إضافة دعم لغات أخرى
- **واجهات مخصصة**: تطوير واجهات جديدة

---

## 📞 الدعم والمساعدة

### الحصول على المساعدة
- **الوثائق**: هذا الدليل الشامل
- **المساعدة المدمجة**: `python run_hybrid.py --help-ar`
- **السجلات**: فحص ملفات السجل للأخطاء
- **المجتمع**: منتديات المطورين العرب

### الإبلاغ عن المشاكل
1. فحص السجلات في `hybrid_launcher.log`
2. تجربة إعادة التشغيل
3. فحص المتطلبات والإعدادات
4. إرسال تقرير مفصل

### المساهمة في التطوير
- **GitHub**: رفع التحسينات والإصلاحات
- **الترجمة**: إضافة لغات جديدة
- **الاختبار**: تجربة الميزات الجديدة
- **التوثيق**: تحسين الأدلة

---

## 🎯 خارطة الطريق

### الإصدار الحالي (2.0.0)
- ✅ التطبيق الهجين الكامل
- ✅ فحص بصري وشبكي متقدم
- ✅ تكامل Excel شامل
- ✅ أمان وخصوصية عالية

### الإصدارات القادمة
- 🔄 **2.1.0**: دعم الهواتف المحمولة
- 🔄 **2.2.0**: ذكاء اصطناعي محسن
- 🔄 **2.3.0**: تكامل سحابي اختياري
- 🔄 **3.0.0**: إعادة تصميم شاملة

---

## 📝 الخلاصة

**كاميرا آيـدي** هو تطبيق هجين متقدم يجمع بين قوة سطح المكتب ومرونة الويب لتوفير حل شامل لتحديد كاميرات المراقبة. مع التركيز على الأمان والخصوصية والسهولة في الاستخدام، يوفر التطبيق جميع الأدوات اللازمة للمحترفين والهواة على حد سواء.

**🌟 ابدأ رحلتك مع كاميرا آيـدي اليوم واكتشف قوة التطبيق الهجين!**

---

*© 2024 كاميرا آيـدي. جميع الحقوق محفوظة.*
