#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لمدير Excel
"""

import os
import sys
from pathlib import Path

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent))

def test_excel_simple():
    """اختبار بسيط لمدير Excel"""
    
    print("🧪 اختبار مدير Excel البسيط...")
    
    try:
        # إنشاء مجلد excel_files إذا لم يكن موجوداً
        excel_dir = Path("excel_files")
        excel_dir.mkdir(exist_ok=True)
        print("✅ تم إنشاء مجلد excel_files")
        
        # اختبار استيراد المكتبات
        print("📦 اختبار المكتبات...")
        
        try:
            import pandas as pd
            print("✅ pandas متوفر")
        except ImportError:
            print("❌ pandas غير متوفر")
            return False
            
        try:
            import openpyxl
            print("✅ openpyxl متوفر")
        except ImportError:
            print("❌ openpyxl غير متوفر")
            return False
            
        try:
            import matplotlib.pyplot as plt
            print("✅ matplotlib متوفر")
        except ImportError:
            print("❌ matplotlib غير متوفر")
            return False
        
        # إنشاء ملف Excel بسيط
        print("\n📊 إنشاء ملف Excel تجريبي...")
        
        # بيانات تجريبية
        data = {
            'الماركة': ['Hikvision', 'Dahua', 'Axis', 'Bosch', 'Sony'],
            'الموديل': ['DS-2CD2142FWD-I', 'IPC-HDW4431C-A', 'P3375-V', 'NDI-5503-A', 'SNC-VB770'],
            'النوع': ['Fixed Dome', 'Eyeball', 'Fixed Dome', 'Fixed Dome', 'Box Camera'],
            'الدقة': ['4MP', '4MP', 'HDTV 1080p', '5MP', 'Full HD'],
            'مستوى الثقة': [0.95, 0.88, 0.92, 0.85, 0.78]
        }
        
        # إنشاء DataFrame
        df = pd.DataFrame(data)
        
        # حفظ كملف Excel
        excel_path = excel_dir / "test_cameras.xlsx"
        df.to_excel(excel_path, index=False, sheet_name='الكاميرات')
        
        print(f"✅ تم إنشاء ملف Excel: {excel_path}")
        
        # قراءة الملف للتأكد
        df_read = pd.read_excel(excel_path)
        print(f"✅ تم قراءة الملف بنجاح - عدد الصفوف: {len(df_read)}")
        
        # إنشاء ملف CSV
        csv_path = excel_dir / "test_cameras.csv"
        df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        print(f"✅ تم إنشاء ملف CSV: {csv_path}")
        
        # إنشاء ملف JSON
        json_path = excel_dir / "test_cameras.json"
        df.to_json(json_path, orient='records', force_ascii=False, indent=2)
        print(f"✅ تم إنشاء ملف JSON: {json_path}")
        
        # إنشاء رسم بياني بسيط
        print("\n📈 إنشاء رسم بياني...")
        
        try:
            plt.figure(figsize=(10, 6))
            brand_counts = df['الماركة'].value_counts()
            plt.pie(brand_counts.values, labels=brand_counts.index, autopct='%1.1f%%')
            plt.title('توزيع الكاميرات حسب الماركة')
            
            chart_path = excel_dir / "brands_chart.png"
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"✅ تم إنشاء الرسم البياني: {chart_path}")
            
        except Exception as e:
            print(f"⚠️ تحذير: لم يتم إنشاء الرسم البياني - {e}")
        
        print("\n🎉 نجح الاختبار البسيط!")
        print("\n📁 الملفات المُنشأة:")
        
        for file_path in excel_dir.glob("*"):
            if file_path.is_file():
                size = file_path.stat().st_size
                print(f"   • {file_path.name} ({size} بايت)")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_excel_simple()
    
    if success:
        print("\n✅ جميع الاختبارات نجحت!")
        print("🚀 يمكنك الآن استخدام مدير Excel في التطبيق")
    else:
        print("\n❌ فشل في بعض الاختبارات")
        print("💡 تأكد من تثبيت جميع المكتبات المطلوبة")
    
    input("\nاضغط Enter للخروج...")
