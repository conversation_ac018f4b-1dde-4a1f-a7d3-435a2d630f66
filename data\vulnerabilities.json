{"vulnerabilities": [{"cve": "CVE-2017-7921", "description": "Hikvision IP Camera Access Control Bypass", "severity": "High", "affected_brands": ["Hikvision"], "cvss_score": 9.8, "published_date": "2017-04-28", "recommendations": ["Change default passwords", "Update firmware to latest version", "Restrict network access"]}, {"cve": "CVE-2021-36260", "description": "Hikvision Camera Command Injection Vulnerability", "severity": "Critical", "affected_brands": ["Hikvision"], "cvss_score": 9.8, "published_date": "2021-07-15", "recommendations": ["Update firmware immediately", "Monitor for unauthorized access", "Isolate camera network"]}, {"cve": "CVE-2020-25078", "description": "Dahua Authentication Bypass Vulnerability", "severity": "High", "affected_brands": ["Dahua"], "cvss_score": 8.8, "published_date": "2020-09-03", "recommendations": ["Change default credentials", "Enable authentication", "Update firmware"]}, {"cve": "Default Credentials", "description": "Use of default manufacturer credentials", "severity": "Medium", "affected_brands": ["All"], "cvss_score": 6.5, "published_date": "N/A", "recommendations": ["Change all default passwords", "Use strong passwords", "Disable unused accounts"]}, {"cve": "Weak Encryption", "description": "Weak or no encryption for data transmission", "severity": "Medium", "affected_brands": ["Various"], "cvss_score": 5.9, "published_date": "N/A", "recommendations": ["Enable HTTPS/TLS encryption", "Use strong encryption protocols", "Regular security audits"]}]}