#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
شاشة البداية الاحترافية - Professional Splash Screen
"""

import tkinter as tk
from tkinter import ttk
import customtkinter as ctk
from PIL import Image, ImageTk, ImageDraw, ImageFont
import threading
import time
import os
from datetime import datetime

class SplashScreen:
    """شاشة البداية الاحترافية"""
    
    def __init__(self, config):
        self.config = config
        self.progress = 0
        self.status_text = "جاري التحميل..."
        
        # إنشاء النافذة
        self.root = ctk.CTk()
        self.setup_window()
        self.create_widgets()
        
        # بدء عملية التحميل
        self.loading_thread = threading.Thread(target=self.loading_process)
        self.loading_thread.daemon = True
        self.loading_thread.start()
    
    def setup_window(self):
        """إعداد نافذة شاشة البداية"""
        self.root.title("كاميرا آيـدي")
        self.root.geometry("600x400")
        
        # إزالة شريط العنوان
        self.root.overrideredirect(True)
        
        # وضع النافذة في المنتصف
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.root.winfo_screenheight() // 2) - (400 // 2)
        self.root.geometry(f"600x400+{x}+{y}")
        
        # جعل النافذة في المقدمة
        self.root.attributes("-topmost", True)
        
        # تعيين لون الخلفية
        self.root.configure(fg_color="#1a1a1a")
    
    def create_widgets(self):
        """إنشاء عناصر شاشة البداية"""
        
        # الإطار الرئيسي
        main_frame = ctk.CTkFrame(
            self.root,
            fg_color="#2b2b2b",
            corner_radius=20,
            border_width=2,
            border_color="#0078d4"
        )
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # شعار التطبيق
        logo_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        logo_frame.pack(pady=(40, 20))
        
        # أيقونة كبيرة
        self.create_logo_icon(logo_frame)
        
        # عنوان التطبيق
        title_label = ctk.CTkLabel(
            main_frame,
            text="كاميرا آيـدي",
            font=ctk.CTkFont(size=36, weight="bold"),
            text_color="#ffffff"
        )
        title_label.pack(pady=10)
        
        # العنوان الفرعي
        subtitle_label = ctk.CTkLabel(
            main_frame,
            text="Camera ID Professional",
            font=ctk.CTkFont(size=16),
            text_color="#b0b0b0"
        )
        subtitle_label.pack(pady=5)
        
        # وصف التطبيق
        description_label = ctk.CTkLabel(
            main_frame,
            text="تطبيق متقدم لتحديد وتحليل كاميرات المراقبة",
            font=ctk.CTkFont(size=14),
            text_color="#888888"
        )
        description_label.pack(pady=10)
        
        # شريط التقدم
        progress_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        progress_frame.pack(fill="x", padx=40, pady=20)
        
        self.progress_bar = ctk.CTkProgressBar(
            progress_frame,
            height=8,
            corner_radius=4,
            progress_color="#0078d4"
        )
        self.progress_bar.pack(fill="x", pady=10)
        self.progress_bar.set(0)
        
        # نص الحالة
        self.status_label = ctk.CTkLabel(
            progress_frame,
            text=self.status_text,
            font=ctk.CTkFont(size=12),
            text_color="#cccccc"
        )
        self.status_label.pack(pady=5)
        
        # معلومات الإصدار
        version_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        version_frame.pack(side="bottom", fill="x", padx=20, pady=10)
        
        version_label = ctk.CTkLabel(
            version_frame,
            text=f"الإصدار {self.config.version}",
            font=ctk.CTkFont(size=10),
            text_color="#666666"
        )
        version_label.pack(side="left")
        
        copyright_label = ctk.CTkLabel(
            version_frame,
            text="© 2024 Camera ID Team",
            font=ctk.CTkFont(size=10),
            text_color="#666666"
        )
        copyright_label.pack(side="right")
    
    def create_logo_icon(self, parent):
        """إنشاء أيقونة الشعار"""
        try:
            # إنشاء أيقونة مخصصة
            icon_size = 80
            image = Image.new('RGBA', (icon_size, icon_size), (0, 0, 0, 0))
            draw = ImageDraw.Draw(image)
            
            # رسم دائرة خلفية
            draw.ellipse([5, 5, icon_size-5, icon_size-5], 
                        fill=(0, 120, 212, 255), outline=(255, 255, 255, 100), width=2)
            
            # رسم رمز الكاميرا
            camera_color = (255, 255, 255, 255)
            
            # جسم الكاميرا
            draw.rectangle([20, 30, 60, 50], fill=camera_color)
            
            # العدسة
            draw.ellipse([35, 25, 55, 45], fill=camera_color)
            draw.ellipse([38, 28, 52, 42], fill=(0, 120, 212, 255))
            
            # الفلاش
            draw.rectangle([15, 25, 20, 30], fill=camera_color)
            
            # تحويل إلى PhotoImage
            photo = ImageTk.PhotoImage(image)
            
            # عرض الأيقونة
            icon_label = ctk.CTkLabel(parent, image=photo, text="")
            icon_label.pack()
            
            # الاحتفاظ بمرجع للصورة
            icon_label.image = photo
            
        except Exception as e:
            # في حالة فشل إنشاء الأيقونة، استخدم نص بديل
            icon_label = ctk.CTkLabel(
                parent,
                text="📹",
                font=ctk.CTkFont(size=48),
                text_color="#0078d4"
            )
            icon_label.pack()
    
    def loading_process(self):
        """عملية التحميل المحاكاة"""
        loading_steps = [
            ("تحميل الإعدادات...", 0.1),
            ("تهيئة قاعدة البيانات...", 0.2),
            ("تحميل نماذج الذكاء الاصطناعي...", 0.4),
            ("فحص الكاميرات المتاحة...", 0.6),
            ("تحميل واجهة المستخدم...", 0.8),
            ("التحقق من التحديثات...", 0.9),
            ("جاهز للتشغيل!", 1.0)
        ]
        
        for status, progress in loading_steps:
            self.status_text = status
            self.progress = progress
            
            # تحديث الواجهة
            self.root.after(0, self.update_ui)
            
            # انتظار قصير
            time.sleep(0.8)
        
        # انتظار إضافي قبل الإغلاق
        time.sleep(1)
        
        # إغلاق شاشة البداية
        self.root.after(0, self.close_splash)
    
    def update_ui(self):
        """تحديث واجهة المستخدم"""
        self.progress_bar.set(self.progress)
        self.status_label.configure(text=self.status_text)
    
    def close_splash(self):
        """إغلاق شاشة البداية"""
        self.root.destroy()
    
    def show(self):
        """عرض شاشة البداية"""
        self.root.mainloop()
        return True  # إشارة لبدء التطبيق الرئيسي
