#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مولد التقارير المتقدم - Advanced Report Generator
"""

import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
import json
import csv

# مكتبات PDF
from reportlab.lib.pagesizes import A4, letter
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.colors import HexColor, black, white, red, green, blue
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image, PageBreak
from reportlab.platypus.flowables import HRFlowable
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

# مكتبات الرسوم البيانية
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.backends.backend_agg import FigureCanvasAgg
import seaborn as sns
import pandas as pd

# تعيين الخط العربي
plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']

class ReportGenerator:
    """مولد التقارير الرئيسي"""
    
    def __init__(self, config, logger, database):
        self.config = config
        self.logger = logger
        self.database = database
        
        # إعداد مجلد التقارير
        self.reports_dir = config.base_dir / "reports"
        self.reports_dir.mkdir(exist_ok=True)
        
        # إعداد مجلد الرسوم البيانية
        self.charts_dir = self.reports_dir / "charts"
        self.charts_dir.mkdir(exist_ok=True)
        
        # تحميل قوالب التقارير
        self.load_report_templates()
    
    def load_report_templates(self):
        """تحميل قوالب التقارير"""
        try:
            templates_file = self.config.base_dir / "templates" / "report_templates.json"
            
            default_templates = {
                "scan_summary": {
                    "title": "تقرير ملخص الفحص",
                    "sections": ["overview", "statistics", "findings", "recommendations"]
                },
                "security_audit": {
                    "title": "تقرير التدقيق الأمني",
                    "sections": ["executive_summary", "vulnerabilities", "risk_assessment", "mitigation"]
                },
                "network_analysis": {
                    "title": "تقرير تحليل الشبكة",
                    "sections": ["network_overview", "device_inventory", "security_status", "recommendations"]
                },
                "compliance_report": {
                    "title": "تقرير الامتثال",
                    "sections": ["compliance_status", "gaps", "action_plan", "timeline"]
                }
            }
            
            if templates_file.exists():
                with open(templates_file, 'r', encoding='utf-8') as f:
                    loaded_templates = json.load(f)
                    default_templates.update(loaded_templates)
            
            self.report_templates = default_templates
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل قوالب التقارير: {e}")
            self.report_templates = {}
    
    def generate_scan_summary_report(self, scan_data: List[Dict[str, Any]], 
                                   output_path: Optional[str] = None) -> str:
        """إنشاء تقرير ملخص الفحص"""
        try:
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = self.reports_dir / f"scan_summary_{timestamp}.pdf"
            
            # إنشاء مستند PDF
            doc = SimpleDocTemplate(
                str(output_path),
                pagesize=A4,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18
            )
            
            # قائمة العناصر
            story = []
            
            # إضافة العنوان والمعلومات الأساسية
            story.extend(self.create_report_header("تقرير ملخص الفحص"))
            
            # إضافة الملخص التنفيذي
            story.extend(self.create_executive_summary(scan_data))
            
            # إضافة الإحصائيات
            story.extend(self.create_statistics_section(scan_data))
            
            # إضافة النتائج المفصلة
            story.extend(self.create_detailed_findings(scan_data))
            
            # إضافة التوصيات
            story.extend(self.create_recommendations_section(scan_data))
            
            # إضافة الرسوم البيانية
            charts = self.generate_charts(scan_data)
            for chart_path in charts:
                story.append(PageBreak())
                story.append(Paragraph("الرسوم البيانية", self.get_heading_style()))
                story.append(Spacer(1, 12))
                story.append(Image(str(chart_path), width=6*inch, height=4*inch))
            
            # بناء المستند
            doc.build(story)
            
            self.logger.info(f"تم إنشاء تقرير الفحص: {output_path}")
            return str(output_path)
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء تقرير الفحص: {e}")
            return None
    
    def generate_security_audit_report(self, audit_data: Dict[str, Any],
                                     output_path: Optional[str] = None) -> str:
        """إنشاء تقرير التدقيق الأمني"""
        try:
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = self.reports_dir / f"security_audit_{timestamp}.pdf"
            
            doc = SimpleDocTemplate(str(output_path), pagesize=A4)
            story = []
            
            # العنوان
            story.extend(self.create_report_header("تقرير التدقيق الأمني"))
            
            # الملخص التنفيذي
            story.append(Paragraph("الملخص التنفيذي", self.get_heading_style()))
            story.append(Spacer(1, 12))
            
            executive_summary = f"""
            تم إجراء تدقيق أمني شامل للنظام في تاريخ {datetime.now().strftime('%Y-%m-%d')}.
            تم فحص {audit_data.get('total_devices', 0)} جهاز وتحديد {audit_data.get('total_vulnerabilities', 0)} ثغرة أمنية.
            """
            
            story.append(Paragraph(executive_summary, self.get_normal_style()))
            story.append(Spacer(1, 20))
            
            # الثغرات الأمنية
            story.append(Paragraph("الثغرات الأمنية المكتشفة", self.get_heading_style()))
            story.append(Spacer(1, 12))
            
            vulnerabilities = audit_data.get('vulnerabilities', [])
            if vulnerabilities:
                vuln_table_data = [['الثغرة', 'الخطورة', 'الوصف', 'الحل']]
                
                for vuln in vulnerabilities[:10]:  # أول 10 ثغرات
                    vuln_table_data.append([
                        vuln.get('cve_id', 'غير محدد'),
                        vuln.get('severity', 'غير محدد'),
                        vuln.get('description', '')[:50] + '...',
                        vuln.get('solution', '')[:50] + '...'
                    ])
                
                vuln_table = Table(vuln_table_data, colWidths=[1.5*inch, 1*inch, 2*inch, 2*inch])
                vuln_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), HexColor('#4472C4')),
                    ('TEXTCOLOR', (0, 0), (-1, 0), white),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), HexColor('#F2F2F2')),
                    ('GRID', (0, 0), (-1, -1), 1, black)
                ]))
                
                story.append(vuln_table)
            
            # بناء المستند
            doc.build(story)
            
            self.logger.info(f"تم إنشاء تقرير التدقيق الأمني: {output_path}")
            return str(output_path)
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء تقرير التدقيق الأمني: {e}")
            return None
    
    def create_report_header(self, title: str) -> List:
        """إنشاء رأس التقرير"""
        elements = []
        
        # العنوان الرئيسي
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=getSampleStyleSheet()['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=HexColor('#2E75B6')
        )
        
        elements.append(Paragraph(title, title_style))
        
        # معلومات التقرير
        info_data = [
            ['تاريخ الإنشاء:', datetime.now().strftime('%Y-%m-%d %H:%M:%S')],
            ['النسخة:', self.config.get('app.version', '1.0.0')],
            ['المؤسسة:', 'كاميرا آيـدي - Camera ID']
        ]
        
        info_table = Table(info_data, colWidths=[2*inch, 3*inch])
        info_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        
        elements.append(info_table)
        elements.append(Spacer(1, 20))
        elements.append(HRFlowable(width="100%", thickness=1, color=HexColor('#CCCCCC')))
        elements.append(Spacer(1, 20))
        
        return elements
    
    def create_executive_summary(self, scan_data: List[Dict[str, Any]]) -> List:
        """إنشاء الملخص التنفيذي"""
        elements = []
        
        elements.append(Paragraph("الملخص التنفيذي", self.get_heading_style()))
        elements.append(Spacer(1, 12))
        
        # حساب الإحصائيات
        total_scans = len(scan_data)
        successful_scans = len([s for s in scan_data if s.get('confidence', 0) > 0.7])
        unique_cameras = len(set(f"{s.get('brand', '')}-{s.get('model', '')}" for s in scan_data))
        
        summary_text = f"""
        تم إجراء {total_scans} عملية فحص، منها {successful_scans} عملية ناجحة بثقة عالية.
        تم تحديد {unique_cameras} نوع مختلف من الكاميرات.
        معدل نجاح الفحص: {(successful_scans/total_scans*100):.1f}% إذا كان هناك عمليات فحص.
        """
        
        elements.append(Paragraph(summary_text, self.get_normal_style()))
        elements.append(Spacer(1, 20))
        
        return elements
    
    def create_statistics_section(self, scan_data: List[Dict[str, Any]]) -> List:
        """إنشاء قسم الإحصائيات"""
        elements = []
        
        elements.append(Paragraph("الإحصائيات", self.get_heading_style()))
        elements.append(Spacer(1, 12))
        
        # جدول الإحصائيات
        stats_data = [['المؤشر', 'القيمة']]
        
        # حساب الإحصائيات
        total_scans = len(scan_data)
        brands = {}
        confidence_levels = {'عالية': 0, 'متوسطة': 0, 'منخفضة': 0}
        
        for scan in scan_data:
            # إحصائيات الماركات
            brand = scan.get('brand', 'غير محدد')
            brands[brand] = brands.get(brand, 0) + 1
            
            # مستويات الثقة
            confidence = scan.get('confidence', 0)
            if confidence >= 0.8:
                confidence_levels['عالية'] += 1
            elif confidence >= 0.5:
                confidence_levels['متوسطة'] += 1
            else:
                confidence_levels['منخفضة'] += 1
        
        stats_data.extend([
            ['إجمالي عمليات الفحص', str(total_scans)],
            ['عدد الماركات المختلفة', str(len(brands))],
            ['فحوصات بثقة عالية', str(confidence_levels['عالية'])],
            ['فحوصات بثقة متوسطة', str(confidence_levels['متوسطة'])],
            ['فحوصات بثقة منخفضة', str(confidence_levels['منخفضة'])]
        ])
        
        stats_table = Table(stats_data, colWidths=[3*inch, 2*inch])
        stats_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor('#4472C4')),
            ('TEXTCOLOR', (0, 0), (-1, 0), white),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), HexColor('#F8F9FA')),
            ('GRID', (0, 0), (-1, -1), 1, black)
        ]))
        
        elements.append(stats_table)
        elements.append(Spacer(1, 20))
        
        return elements
    
    def create_detailed_findings(self, scan_data: List[Dict[str, Any]]) -> List:
        """إنشاء النتائج المفصلة"""
        elements = []
        
        elements.append(Paragraph("النتائج المفصلة", self.get_heading_style()))
        elements.append(Spacer(1, 12))
        
        # جدول النتائج
        findings_data = [['التاريخ', 'الماركة', 'الموديل', 'مستوى الثقة', 'نوع الفحص']]
        
        for scan in scan_data[:20]:  # أول 20 نتيجة
            confidence = scan.get('confidence', 0)
            confidence_text = 'عالية' if confidence >= 0.8 else 'متوسطة' if confidence >= 0.5 else 'منخفضة'
            
            findings_data.append([
                scan.get('created_at', datetime.now().strftime('%Y-%m-%d'))[:10],
                scan.get('brand', 'غير محدد'),
                scan.get('model', 'غير محدد'),
                f"{confidence:.2f} ({confidence_text})",
                scan.get('scan_type', 'بصري')
            ])
        
        findings_table = Table(findings_data, colWidths=[1.2*inch, 1.5*inch, 1.5*inch, 1.3*inch, 1*inch])
        findings_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor('#4472C4')),
            ('TEXTCOLOR', (0, 0), (-1, 0), white),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 9),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), HexColor('#F8F9FA')),
            ('GRID', (0, 0), (-1, -1), 1, black)
        ]))
        
        elements.append(findings_table)
        elements.append(Spacer(1, 20))
        
        return elements
    
    def create_recommendations_section(self, scan_data: List[Dict[str, Any]]) -> List:
        """إنشاء قسم التوصيات"""
        elements = []
        
        elements.append(Paragraph("التوصيات", self.get_heading_style()))
        elements.append(Spacer(1, 12))
        
        recommendations = [
            "تحديث قاعدة بيانات الكاميرات بانتظام لتحسين دقة التحديد",
            "إجراء فحوصات دورية للشبكة لاكتشاف الأجهزة الجديدة",
            "تطبيق التحديثات الأمنية على جميع الكاميرات المكتشفة",
            "مراجعة إعدادات الأمان وكلمات المرور الافتراضية",
            "توثيق جميع الكاميرات المكتشفة في سجل الأصول"
        ]
        
        for i, recommendation in enumerate(recommendations, 1):
            elements.append(Paragraph(f"{i}. {recommendation}", self.get_normal_style()))
            elements.append(Spacer(1, 8))
        
        return elements
    
    def generate_charts(self, scan_data: List[Dict[str, Any]]) -> List[str]:
        """إنشاء الرسوم البيانية"""
        chart_paths = []
        
        try:
            # رسم بياني للماركات
            brands_chart = self.create_brands_chart(scan_data)
            if brands_chart:
                chart_paths.append(brands_chart)
            
            # رسم بياني لمستويات الثقة
            confidence_chart = self.create_confidence_chart(scan_data)
            if confidence_chart:
                chart_paths.append(confidence_chart)
            
            # رسم بياني زمني
            timeline_chart = self.create_timeline_chart(scan_data)
            if timeline_chart:
                chart_paths.append(timeline_chart)
                
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الرسوم البيانية: {e}")
        
        return chart_paths
    
    def create_brands_chart(self, scan_data: List[Dict[str, Any]]) -> Optional[str]:
        """إنشاء رسم بياني للماركات"""
        try:
            brands = {}
            for scan in scan_data:
                brand = scan.get('brand', 'غير محدد')
                brands[brand] = brands.get(brand, 0) + 1
            
            if not brands:
                return None
            
            # إنشاء الرسم البياني
            plt.figure(figsize=(10, 6))
            plt.pie(brands.values(), labels=brands.keys(), autopct='%1.1f%%', startangle=90)
            plt.title('توزيع الماركات المكتشفة', fontsize=16, pad=20)
            
            # حفظ الرسم
            chart_path = self.charts_dir / f"brands_chart_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(chart_path)
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء رسم الماركات: {e}")
            return None
    
    def create_confidence_chart(self, scan_data: List[Dict[str, Any]]) -> Optional[str]:
        """إنشاء رسم بياني لمستويات الثقة"""
        try:
            confidence_levels = {'عالية (>80%)': 0, 'متوسطة (50-80%)': 0, 'منخفضة (<50%)': 0}
            
            for scan in scan_data:
                confidence = scan.get('confidence', 0)
                if confidence >= 0.8:
                    confidence_levels['عالية (>80%)'] += 1
                elif confidence >= 0.5:
                    confidence_levels['متوسطة (50-80%)'] += 1
                else:
                    confidence_levels['منخفضة (<50%)'] += 1
            
            if sum(confidence_levels.values()) == 0:
                return None
            
            # إنشاء الرسم البياني
            plt.figure(figsize=(10, 6))
            colors = ['#2E8B57', '#FFD700', '#DC143C']
            bars = plt.bar(confidence_levels.keys(), confidence_levels.values(), color=colors)
            
            plt.title('توزيع مستويات الثقة', fontsize=16, pad=20)
            plt.ylabel('عدد العمليات')
            plt.xlabel('مستوى الثقة')
            
            # إضافة القيم على الأعمدة
            for bar in bars:
                height = bar.get_height()
                plt.text(bar.get_x() + bar.get_width()/2., height,
                        f'{int(height)}', ha='center', va='bottom')
            
            plt.xticks(rotation=45)
            
            # حفظ الرسم
            chart_path = self.charts_dir / f"confidence_chart_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(chart_path)
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء رسم مستويات الثقة: {e}")
            return None
    
    def create_timeline_chart(self, scan_data: List[Dict[str, Any]]) -> Optional[str]:
        """إنشاء رسم بياني زمني"""
        try:
            if not scan_data:
                return None
            
            # تجميع البيانات حسب التاريخ
            dates = {}
            for scan in scan_data:
                date_str = scan.get('created_at', datetime.now().isoformat())[:10]
                dates[date_str] = dates.get(date_str, 0) + 1
            
            if not dates:
                return None
            
            # ترتيب التواريخ
            sorted_dates = sorted(dates.items())
            
            # إنشاء الرسم البياني
            plt.figure(figsize=(12, 6))
            dates_list = [item[0] for item in sorted_dates]
            counts_list = [item[1] for item in sorted_dates]
            
            plt.plot(dates_list, counts_list, marker='o', linewidth=2, markersize=6)
            plt.title('عدد عمليات الفحص عبر الزمن', fontsize=16, pad=20)
            plt.ylabel('عدد العمليات')
            plt.xlabel('التاريخ')
            plt.xticks(rotation=45)
            plt.grid(True, alpha=0.3)
            
            # حفظ الرسم
            chart_path = self.charts_dir / f"timeline_chart_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(chart_path)
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الرسم الزمني: {e}")
            return None
    
    def get_heading_style(self):
        """الحصول على نمط العناوين"""
        return ParagraphStyle(
            'CustomHeading',
            parent=getSampleStyleSheet()['Heading2'],
            fontSize=14,
            spaceAfter=12,
            textColor=HexColor('#2E75B6'),
            alignment=TA_RIGHT
        )
    
    def get_normal_style(self):
        """الحصول على النمط العادي"""
        return ParagraphStyle(
            'CustomNormal',
            parent=getSampleStyleSheet()['Normal'],
            fontSize=11,
            spaceAfter=6,
            alignment=TA_RIGHT,
            rightIndent=20,
            leftIndent=20
        )
    
    def export_to_csv(self, data: List[Dict[str, Any]], output_path: str) -> bool:
        """تصدير البيانات إلى CSV"""
        try:
            with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
                if not data:
                    return False
                
                fieldnames = data[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for row in data:
                    writer.writerow(row)
            
            self.logger.info(f"تم تصدير البيانات إلى CSV: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في تصدير CSV: {e}")
            return False
    
    def export_to_json(self, data: Any, output_path: str) -> bool:
        """تصدير البيانات إلى JSON"""
        try:
            with open(output_path, 'w', encoding='utf-8') as jsonfile:
                json.dump(data, jsonfile, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"تم تصدير البيانات إلى JSON: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في تصدير JSON: {e}")
            return False
