{% extends "base.html" %}

{% block title %}كاميرا آيـدي - الصفحة الرئيسية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- Hero Section -->
        <div class="text-center mb-5">
            <h1 class="display-4 fw-bold text-primary mb-3">
                <i class="fas fa-video me-3"></i>
                كاميرا آيـدي
            </h1>
            <p class="lead text-muted mb-4">
                تطبيق هجين متقدم لتحديد نوع وموديل كاميرات المراقبة باستخدام الذكاء الاصطناعي
            </p>
            <div class="d-flex justify-content-center gap-3 flex-wrap">
                <span class="badge bg-primary fs-6">
                    <i class="fas fa-desktop me-1"></i>
                    سطح المكتب
                </span>
                <span class="badge bg-success fs-6">
                    <i class="fas fa-globe me-1"></i>
                    الويب
                </span>
                <span class="badge bg-info fs-6">
                    <i class="fas fa-mobile-alt me-1"></i>
                    متجاوب
                </span>
                <span class="badge bg-warning fs-6">
                    <i class="fas fa-shield-alt me-1"></i>
                    آمن
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Features Cards -->
<div class="row g-4 mb-5">
    <div class="col-lg-4 col-md-6">
        <div class="card h-100 text-center">
            <div class="card-body">
                <div class="mb-3">
                    <i class="fas fa-camera fa-3x text-primary"></i>
                </div>
                <h5 class="card-title">الفحص البصري</h5>
                <p class="card-text text-muted">
                    تحليل الصور باستخدام الذكاء الاصطناعي لتحديد نوع وموديل الكاميرا
                </p>
                <a href="/scan/visual" class="btn btn-primary">
                    <i class="fas fa-play me-2"></i>
                    ابدأ الفحص
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 col-md-6">
        <div class="card h-100 text-center">
            <div class="card-body">
                <div class="mb-3">
                    <i class="fas fa-network-wired fa-3x text-success"></i>
                </div>
                <h5 class="card-title">الفحص الشبكي</h5>
                <p class="card-text text-muted">
                    اكتشاف كاميرات IP في الشبكة وتحديد معلوماتها التقنية
                </p>
                <a href="/scan/network" class="btn btn-success">
                    <i class="fas fa-search me-2"></i>
                    فحص الشبكة
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 col-md-6">
        <div class="card h-100 text-center">
            <div class="card-body">
                <div class="mb-3">
                    <i class="fas fa-chart-bar fa-3x text-info"></i>
                </div>
                <h5 class="card-title">التقارير والنتائج</h5>
                <p class="card-text text-muted">
                    عرض وتحليل نتائج الفحص مع إمكانية التصدير والطباعة
                </p>
                <a href="/results" class="btn btn-info">
                    <i class="fas fa-eye me-2"></i>
                    عرض النتائج
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Dashboard -->
<div class="row g-4 mb-5">
    <div class="col-12">
        <h3 class="text-center mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>
            لوحة الإحصائيات
        </h3>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-camera fa-2x mb-2"></i>
                <h4 id="total-scans">0</h4>
                <p class="mb-0">إجمالي الفحوصات</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-2x mb-2"></i>
                <h4 id="successful-scans">0</h4>
                <p class="mb-0">فحوصات ناجحة</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <h4 id="vulnerabilities-found">0</h4>
                <p class="mb-0">ثغرات مكتشفة</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-network-wired fa-2x mb-2"></i>
                <h4 id="devices-found">0</h4>
                <p class="mb-0">أجهزة مكتشفة</p>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    النشاط الأخير
                </h5>
                <button class="btn btn-sm btn-outline-primary" onclick="refreshActivity()">
                    <i class="fas fa-sync-alt me-1"></i>
                    تحديث
                </button>
            </div>
            <div class="card-body">
                <div id="recent-activity">
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                        <p>جاري تحميل النشاط الأخير...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex flex-wrap gap-2">
                    <button class="btn btn-outline-primary" onclick="quickVisualScan()">
                        <i class="fas fa-camera me-1"></i>
                        فحص بصري سريع
                    </button>
                    <button class="btn btn-outline-success" onclick="quickNetworkScan()">
                        <i class="fas fa-network-wired me-1"></i>
                        فحص الشبكة المحلية
                    </button>
                    <button class="btn btn-outline-info" onclick="exportResults()">
                        <i class="fas fa-download me-1"></i>
                        تصدير النتائج
                    </button>
                    <button class="btn btn-outline-warning" onclick="clearDatabase()">
                        <i class="fas fa-trash me-1"></i>
                        مسح قاعدة البيانات
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Load statistics on page load
    document.addEventListener('DOMContentLoaded', function() {
        loadStatistics();
        loadRecentActivity();
    });
    
    // Load statistics from API
    async function loadStatistics() {
        try {
            const response = await fetch('/api/statistics');
            const data = await response.json();
            
            document.getElementById('total-scans').textContent = data.total_scans || 0;
            document.getElementById('successful-scans').textContent = data.successful_scans || 0;
            document.getElementById('vulnerabilities-found').textContent = data.vulnerabilities_found || 0;
            document.getElementById('devices-found').textContent = data.devices_found || 0;
        } catch (error) {
            console.error('خطأ في تحميل الإحصائيات:', error);
        }
    }
    
    // Load recent activity
    async function loadRecentActivity() {
        try {
            const response = await fetch('/api/results?limit=5');
            const data = await response.json();
            
            const activityDiv = document.getElementById('recent-activity');
            
            if (data.length === 0) {
                activityDiv.innerHTML = `
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-inbox fa-2x mb-3"></i>
                        <p>لا توجد أنشطة حديثة</p>
                    </div>
                `;
                return;
            }
            
            let html = '<div class="list-group list-group-flush">';
            
            data.forEach(item => {
                const date = new Date(item.timestamp).toLocaleString('ar-SA');
                const badgeClass = item.scan_type === 'visual' ? 'bg-primary' : 'bg-success';
                const icon = item.scan_type === 'visual' ? 'fa-camera' : 'fa-network-wired';
                
                html += `
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas ${icon} me-2"></i>
                            <strong>${item.brand || 'غير محدد'} ${item.model || ''}</strong>
                            <br>
                            <small class="text-muted">${date}</small>
                        </div>
                        <div>
                            <span class="badge ${badgeClass}">${item.scan_type === 'visual' ? 'بصري' : 'شبكي'}</span>
                            <span class="badge bg-secondary ms-1">${(item.confidence * 100).toFixed(0)}%</span>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            activityDiv.innerHTML = html;
            
        } catch (error) {
            console.error('خطأ في تحميل النشاط الأخير:', error);
            document.getElementById('recent-activity').innerHTML = `
                <div class="text-center text-danger py-4">
                    <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                    <p>خطأ في تحميل البيانات</p>
                </div>
            `;
        }
    }
    
    // Refresh activity
    function refreshActivity() {
        loadRecentActivity();
        loadStatistics();
    }
    
    // Quick actions
    function quickVisualScan() {
        window.location.href = '/scan/visual';
    }
    
    function quickNetworkScan() {
        window.location.href = '/scan/network';
    }
    
    async function exportResults() {
        try {
            const response = await fetch('/api/export');
            const data = await response.json();
            
            if (data.success) {
                alert('تم تصدير النتائج بنجاح!');
            } else {
                alert('خطأ في التصدير: ' + data.error);
            }
        } catch (error) {
            alert('خطأ في التصدير: ' + error.message);
        }
    }
    
    function clearDatabase() {
        if (confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
            // Implementation for clearing database
            alert('تم مسح قاعدة البيانات');
            refreshActivity();
        }
    }
    
    // Socket.IO events for real-time updates
    socket.on('scan_completed', function(data) {
        loadStatistics();
        loadRecentActivity();
        
        // Show notification
        const toast = document.createElement('div');
        toast.className = 'toast show position-fixed top-0 end-0 m-3';
        toast.innerHTML = `
            <div class="toast-header">
                <i class="fas fa-check-circle text-success me-2"></i>
                <strong class="me-auto">فحص مكتمل</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                تم اكتشاف: ${data.brand} ${data.model}
            </div>
        `;
        document.body.appendChild(toast);
        
        setTimeout(() => toast.remove(), 5000);
    });
</script>
{% endblock %}
