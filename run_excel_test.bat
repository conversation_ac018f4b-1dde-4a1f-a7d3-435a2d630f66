@echo off
chcp 65001 > nul
title اختبار مدير Excel

echo ========================================
echo    اختبار مدير Excel
echo    Excel Manager Test
echo ========================================
echo.

echo جاري اختبار Python...
py --version
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت أو غير متاح
    echo يرجى تثبيت Python من python.org
    pause
    exit /b 1
)

echo.
echo جاري اختبار المكتبات المطلوبة...

echo تجربة pandas...
py -c "import pandas; print('✅ pandas متوفر')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ pandas غير متوفر - جاري التثبيت...
    py -m pip install pandas
)

echo تجربة openpyxl...
py -c "import openpyxl; print('✅ openpyxl متوفر')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ openpyxl غير متوفر - جاري التثبيت...
    py -m pip install openpyxl
)

echo تجربة matplotlib...
py -c "import matplotlib; print('✅ matplotlib متوفر')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ matplotlib غير متوفر - جاري التثبيت...
    py -m pip install matplotlib
)

echo.
echo ========================================
echo جاري تشغيل اختبار Excel...
echo ========================================
echo.

py test_excel_simple.py

echo.
echo ========================================
echo انتهى الاختبار
echo ========================================
pause
