===============================================
    دليل الاستخدام الشامل
    كاميرا آيـدي - Camera ID
    مع مدير Excel المتقدم
===============================================

🚀 التشغيل السريع
==================

الطريقة الأسهل (مع Excel):
1. انقر نقراً مزدوجاً على "run_camera_id_excel.bat"
2. انتظر حتى يتم تثبيت المكتبات تلقائياً
3. سيفتح التطبيق مع جميع الميزات

الطريقة التقليدية:
1. انقر نقراً مزدوجاً على "run_camera_id.bat"
2. أو شغل "start_app.py" مباشرة

🎯 الميزات الرئيسية
====================

📷 الفحص البصري:
- تشغيل الكاميرا المباشر
- تحميل صور من الملفات
- تحديد نوع الكاميرا تلقائياً
- عرض درجة الثقة

🌐 الفحص الشبكي:
- مسح نطاقات IP
- اكتشاف كاميرات ONVIF
- فحص المنافذ المفتوحة
- تحديد معلومات الأجهزة

📊 مدير Excel المتقدم (جديد!):
- فتح وتحرير ملفات Excel
- قوالب احترافية جاهزة
- رسوم بيانية تلقائية
- تحويل إلى CSV, JSON, HTML
- فحص صحة الملفات
- تقارير مفصلة

📄 تقارير PDF احترافية:
- تقارير مع رسوم بيانية
- تصميم عربي أنيق
- تصدير شامل للبيانات

🔒 نظام الأمان والتشفير:
- تشفير البيانات الحساسة
- إدارة جلسات آمنة
- تدقيق أمني شامل

📋 كيفية الاستخدام
===================

1️⃣ الفحص البصري:
   - انقر على "فحص بصري"
   - اختر "تشغيل الكاميرا" أو "تحميل صورة"
   - انتظر النتيجة مع درجة الثقة

2️⃣ الفحص الشبكي:
   - انقر على "فحص شبكي"
   - أدخل نطاق IP (مثل: ***********/24)
   - انقر على "بدء الفحص"
   - اعرض الأجهزة المكتشفة

3️⃣ مدير Excel (جديد!):
   - انقر على زر "Excel" في شريط القائمة العلوي
   - اختر "فتح ملف" لتحرير ملف موجود
   - أو اختر "إنشاء تقرير" لإنشاء تقرير جديد
   - استخدم "إضافة رسوم بيانية" لتحسين التقارير
   - جرب "تحويل الصيغة" لتصدير البيانات

4️⃣ عرض النتائج:
   - انقر على "النتائج"
   - تصفح جميع نتائج الفحص السابقة
   - استخدم البحث للعثور على نتائج محددة
   - صدر النتائج كـ PDF أو Excel

📊 ميزات Excel الجديدة
========================

✨ القوالب الاحترافية:
- قالب تقرير الكاميرات
- قالب تقرير الشبكة
- قالب تقرير الأمان
- قالب الإحصائيات

✨ الرسوم البيانية:
- رسوم دائرية للتوزيعات
- رسوم عمودية للمقارنات
- رسوم خطية للاتجاهات
- إضافة تلقائية للرسوم

✨ تحويل الصيغ:
- تصدير إلى CSV للتحليل
- تصدير إلى JSON للبرمجة
- تصدير إلى HTML للعرض
- دعم التحويل المتعدد

✨ فحص الملفات:
- التحقق من صحة البيانات
- إحصائيات مفصلة
- تقارير الأخطاء
- معلومات الملف

🛠️ استكشاف الأخطاء
====================

❌ التطبيق لا يفتح:
   - تأكد من تثبيت Python
   - شغل install_requirements.bat
   - تحقق من رسائل الخطأ

❌ مدير Excel لا يعمل:
   - تأكد من تثبيت pandas و openpyxl
   - شغل run_excel_test.bat للاختبار
   - تحقق من صلاحيات الكتابة

❌ الكاميرا لا تعمل:
   - تأكد من توصيل الكاميرا
   - أعط الصلاحيات للتطبيق
   - جرب كاميرا أخرى

❌ فحص الشبكة لا يعمل:
   - تأكد من الاتصال بالشبكة
   - تحقق من إعدادات الجدار الناري
   - جرب نطاق IP مختلف

💡 نصائح مفيدة
===============

✅ للحصول على أفضل النتائج:
- استخدم صور واضحة وعالية الجودة
- تأكد من إضاءة جيدة للكاميرا
- اختر نطاق IP مناسب للشبكة

✅ لاستخدام Excel بكفاءة:
- استخدم القوالب الجاهزة
- أضف الرسوم البيانية لتوضيح البيانات
- احفظ نسخ احتياطية من الملفات المهمة
- استخدم تحويل الصيغ لمشاركة البيانات

✅ للأمان:
- جميع البيانات تبقى محلية
- لا يتم إرسال أي معلومات للإنترنت
- يمكنك استخدام التطبيق دون اتصال
- البيانات الحساسة مشفرة

✅ للدعم:
- راجع ملف README_EXCEL.md لتفاصيل Excel
- راجع ملف "دليل_مدير_Excel.txt"
- تحقق من ملفات السجل في حالة الأخطاء
- تواصل مع فريق الدعم عند الحاجة

🎁 ملفات مفيدة
===============

📖 الوثائق:
- README.md - الدليل الشامل
- README_EXCEL.md - دليل مدير Excel
- دليل_مدير_Excel.txt - دليل مفصل

🧪 الاختبار:
- test_excel_simple.py - اختبار Excel بسيط
- run_excel_test.bat - اختبار شامل
- create_sample_excel.py - إنشاء ملفات تجريبية

🚀 التشغيل:
- run_camera_id_excel.bat - تشغيل مع Excel
- run_camera_id.bat - تشغيل تقليدي
- start_app.py - تشغيل مباشر

📁 المجلدات المهمة:
- excel_files/ - ملفات Excel والتقارير
- reports/ - تقارير PDF
- data/ - قواعد البيانات المحلية
- src/ - كود المصدر
- templates/ - قوالب التقارير

🔧 المتطلبات التقنية
=====================

المتطلبات الأساسية:
- Python 3.8 أو أحدث
- Windows 10/11 أو Linux
- 4 GB RAM (8 GB مُوصى به)
- 2 GB مساحة فارغة

المكتبات المطلوبة:
- customtkinter - واجهة المستخدم الحديثة
- opencv-python - معالجة الصور
- pandas - تحليل البيانات
- openpyxl - ملفات Excel
- matplotlib - الرسوم البيانية
- cryptography - التشفير والأمان
- reportlab - تقارير PDF
- tensorflow - الذكاء الاصطناعي

🌟 ميزات متقدمة
================

🤖 الذكاء الاصطناعي:
- نماذج تعلم عميق لتحديد الكاميرات
- تحليل الميزات البصرية
- تحسين دقة التعرف

🔍 التحليل المتقدم:
- إحصائيات شاملة
- تحليل الاتجاهات
- تقارير مقارنة

🎨 التخصيص:
- مظاهر متعددة
- إعدادات قابلة للتخصيص
- قوالب مخصصة

🔐 الأمان المتقدم:
- تشفير قوي للبيانات
- إدارة المفاتيح
- تدقيق شامل

===============================================
🎯 صُنع بـ ❤️ للمجتمع العربي
مع مدير Excel المتقدم الجديد!
===============================================
