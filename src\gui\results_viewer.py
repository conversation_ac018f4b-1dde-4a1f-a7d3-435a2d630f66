#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عارض النتائج - Results Viewer
واجهة عرض وإدارة نتائج الفحص
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import customtkinter as ctk
from datetime import datetime
import json
import os

class ResultsViewerFrame(ctk.CTkFrame):
    """إطار عارض النتائج"""
    
    def __init__(self, parent, config):
        super().__init__(parent)
        self.config = config
        self.results_data = []
        
        self.create_widgets()
        self.load_results()
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        
        # العنوان
        title = ctk.CTkLabel(
            self,
            text="عارض النتائج",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title.pack(pady=20)
        
        # شريط الأدوات
        toolbar_frame = ctk.CTkFrame(self)
        toolbar_frame.pack(fill="x", padx=20, pady=10)
        
        # أزرار الأدوات
        tools_frame = ctk.CTkFrame(toolbar_frame, fg_color="transparent")
        tools_frame.pack(side="left", padx=10, pady=10)
        
        self.refresh_btn = ctk.CTkButton(
            tools_frame,
            text="🔄 تحديث",
            command=self.load_results,
            width=100
        )
        self.refresh_btn.pack(side="left", padx=5)
        
        self.export_btn = ctk.CTkButton(
            tools_frame,
            text="📤 تصدير",
            command=self.export_results,
            width=100
        )
        self.export_btn.pack(side="left", padx=5)
        
        self.clear_btn = ctk.CTkButton(
            tools_frame,
            text="🗑️ مسح الكل",
            command=self.clear_all_results,
            width=100
        )
        self.clear_btn.pack(side="left", padx=5)
        
        # مرشحات البحث
        filter_frame = ctk.CTkFrame(toolbar_frame, fg_color="transparent")
        filter_frame.pack(side="right", padx=10, pady=10)
        
        filter_label = ctk.CTkLabel(filter_frame, text="البحث:")
        filter_label.pack(side="right", padx=5)
        
        self.search_entry = ctk.CTkEntry(
            filter_frame,
            placeholder_text="ابحث في النتائج...",
            width=200
        )
        self.search_entry.pack(side="right", padx=5)
        self.search_entry.bind("<KeyRelease>", self.filter_results)
        
        # إطار النتائج الرئيسي
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # قائمة النتائج
        list_frame = ctk.CTkFrame(main_frame)
        list_frame.pack(side="left", fill="both", expand=True, padx=(10, 5), pady=10)
        
        list_title = ctk.CTkLabel(
            list_frame,
            text="قائمة النتائج",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        list_title.pack(pady=10)
        
        # جدول النتائج
        self.results_tree = ttk.Treeview(
            list_frame,
            columns=("date", "type", "target", "result"),
            show="headings",
            height=20
        )
        
        # تعيين عناوين الأعمدة
        self.results_tree.heading("date", text="التاريخ")
        self.results_tree.heading("type", text="نوع الفحص")
        self.results_tree.heading("target", text="الهدف")
        self.results_tree.heading("result", text="النتيجة")
        
        # تعيين عرض الأعمدة
        self.results_tree.column("date", width=150)
        self.results_tree.column("type", width=100)
        self.results_tree.column("target", width=150)
        self.results_tree.column("result", width=200)
        
        # ربط حدث التحديد
        self.results_tree.bind("<<TreeviewSelect>>", self.on_result_select)
        
        # شريط التمرير للجدول
        tree_scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=tree_scrollbar.set)
        
        # تعبئة الجدول وشريط التمرير
        tree_container = ctk.CTkFrame(list_frame, fg_color="transparent")
        tree_container.pack(fill="both", expand=True, padx=10, pady=10)
        
        self.results_tree.pack(side="left", fill="both", expand=True)
        tree_scrollbar.pack(side="right", fill="y")
        
        # لوحة التفاصيل
        details_frame = ctk.CTkFrame(main_frame)
        details_frame.pack(side="right", fill="both", expand=True, padx=(5, 10), pady=10)
        
        details_title = ctk.CTkLabel(
            details_frame,
            text="تفاصيل النتيجة",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        details_title.pack(pady=10)
        
        # منطقة عرض التفاصيل
        self.details_text = ctk.CTkTextbox(
            details_frame,
            font=ctk.CTkFont(size=12),
            wrap="word"
        )
        self.details_text.pack(fill="both", expand=True, padx=10, pady=10)
        
        # أزرار العمليات
        actions_frame = ctk.CTkFrame(details_frame, fg_color="transparent")
        actions_frame.pack(fill="x", padx=10, pady=10)
        
        self.view_image_btn = ctk.CTkButton(
            actions_frame,
            text="🖼️ عرض الصورة",
            command=self.view_image,
            width=120,
            state="disabled"
        )
        self.view_image_btn.pack(side="left", padx=5)
        
        self.generate_report_btn = ctk.CTkButton(
            actions_frame,
            text="📄 تقرير PDF",
            command=self.generate_pdf_report,
            width=120,
            state="disabled"
        )
        self.generate_report_btn.pack(side="left", padx=5)
        
        self.delete_result_btn = ctk.CTkButton(
            actions_frame,
            text="🗑️ حذف",
            command=self.delete_selected_result,
            width=120,
            state="disabled"
        )
        self.delete_result_btn.pack(side="left", padx=5)
    
    def load_results(self):
        """تحميل النتائج المحفوظة"""
        # مسح الجدول الحالي
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        
        # تحميل النتائج من الملفات (محاكاة)
        self.results_data = self.load_sample_results()
        
        # إضافة النتائج إلى الجدول
        for result in self.results_data:
            self.results_tree.insert("", "end", values=(
                result["date"],
                result["type"],
                result["target"],
                result["summary"]
            ))
    
    def load_sample_results(self):
        """تحميل نتائج تجريبية"""
        sample_results = [
            {
                "id": "1",
                "date": "2024-01-15 14:30:25",
                "type": "فحص بصري",
                "target": "كاميرا مكتبية",
                "summary": "Hikvision DS-2CD2142FWD-I",
                "details": {
                    "brand": "Hikvision",
                    "model": "DS-2CD2142FWD-I",
                    "confidence": 0.89,
                    "features": ["4MP", "IR Night Vision", "IP67"],
                    "vulnerabilities": ["CVE-2017-7921"],
                    "image_path": None
                }
            },
            {
                "id": "2",
                "date": "2024-01-15 15:45:12",
                "type": "فحص شبكي",
                "target": "*************",
                "summary": "كاميرا IP - Dahua",
                "details": {
                    "ip": "*************",
                    "ports": [80, 554, 8000],
                    "device_type": "كاميرا IP",
                    "brand": "Dahua",
                    "model": "IPC-HDW4431C-A",
                    "onvif_support": True,
                    "vulnerabilities": ["Default credentials"]
                }
            },
            {
                "id": "3",
                "date": "2024-01-14 09:20:33",
                "type": "فحص بصري",
                "target": "كاميرا خارجية",
                "summary": "Axis P3375-V",
                "details": {
                    "brand": "Axis",
                    "model": "P3375-V",
                    "confidence": 0.92,
                    "features": ["Fixed Dome", "HDTV 1080p", "Day/Night"],
                    "vulnerabilities": [],
                    "image_path": "temp/camera_capture_20240114_092033.jpg"
                }
            }
        ]
        
        return sample_results
    
    def filter_results(self, event=None):
        """تصفية النتائج حسب البحث"""
        search_term = self.search_entry.get().lower()
        
        # مسح الجدول
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        
        # إضافة النتائج المطابقة فقط
        for result in self.results_data:
            if (search_term in result["target"].lower() or 
                search_term in result["summary"].lower() or
                search_term in result["type"].lower()):
                
                self.results_tree.insert("", "end", values=(
                    result["date"],
                    result["type"],
                    result["target"],
                    result["summary"]
                ))
    
    def on_result_select(self, event):
        """معالج تحديد نتيجة"""
        selection = self.results_tree.selection()
        if not selection:
            self.details_text.delete("1.0", "end")
            self.view_image_btn.configure(state="disabled")
            self.generate_report_btn.configure(state="disabled")
            self.delete_result_btn.configure(state="disabled")
            return
        
        # الحصول على البيانات المحددة
        item = self.results_tree.item(selection[0])
        values = item["values"]
        
        # البحث عن النتيجة في البيانات
        selected_result = None
        for result in self.results_data:
            if (result["date"] == values[0] and 
                result["target"] == values[2]):
                selected_result = result
                break
        
        if selected_result:
            self.display_result_details(selected_result)
            
            # تفعيل الأزرار
            self.generate_report_btn.configure(state="normal")
            self.delete_result_btn.configure(state="normal")
            
            # تفعيل زر عرض الصورة إذا كانت متوفرة
            if (selected_result["details"].get("image_path") and 
                os.path.exists(selected_result["details"]["image_path"])):
                self.view_image_btn.configure(state="normal")
            else:
                self.view_image_btn.configure(state="disabled")
    
    def display_result_details(self, result):
        """عرض تفاصيل النتيجة"""
        self.details_text.delete("1.0", "end")
        
        details_text = f"""
تفاصيل النتيجة
================

التاريخ: {result['date']}
نوع الفحص: {result['type']}
الهدف: {result['target']}

"""
        
        if result["type"] == "فحص بصري":
            details = result["details"]
            details_text += f"""
نتائج الفحص البصري:
--------------------
الماركة: {details.get('brand', 'غير محدد')}
الموديل: {details.get('model', 'غير محدد')}
درجة الثقة: {details.get('confidence', 0):.2%}

المميزات:
"""
            for feature in details.get('features', []):
                details_text += f"• {feature}\n"
            
            details_text += "\nالثغرات الأمنية:\n"
            vulnerabilities = details.get('vulnerabilities', [])
            if vulnerabilities:
                for vuln in vulnerabilities:
                    details_text += f"⚠️ {vuln}\n"
            else:
                details_text += "✅ لم يتم العثور على ثغرات معروفة\n"
        
        elif result["type"] == "فحص شبكي":
            details = result["details"]
            details_text += f"""
نتائج الفحص الشبكي:
-------------------
عنوان IP: {details.get('ip', 'غير محدد')}
المنافذ المفتوحة: {', '.join(map(str, details.get('ports', [])))}
نوع الجهاز: {details.get('device_type', 'غير محدد')}
الماركة: {details.get('brand', 'غير محدد')}
الموديل: {details.get('model', 'غير محدد')}
دعم ONVIF: {'نعم' if details.get('onvif_support') else 'لا'}

الثغرات الأمنية:
"""
            vulnerabilities = details.get('vulnerabilities', [])
            if vulnerabilities:
                for vuln in vulnerabilities:
                    details_text += f"⚠️ {vuln}\n"
            else:
                details_text += "✅ لم يتم العثور على ثغرات معروفة\n"
        
        self.details_text.insert("1.0", details_text)
    
    def view_image(self):
        """عرض الصورة المرتبطة بالنتيجة"""
        selection = self.results_tree.selection()
        if not selection:
            return
        
        # الحصول على النتيجة المحددة
        item = self.results_tree.item(selection[0])
        values = item["values"]
        
        selected_result = None
        for result in self.results_data:
            if (result["date"] == values[0] and 
                result["target"] == values[2]):
                selected_result = result
                break
        
        if selected_result and selected_result["details"].get("image_path"):
            image_path = selected_result["details"]["image_path"]
            if os.path.exists(image_path):
                # فتح الصورة في نافذة منفصلة
                self.show_image_window(image_path)
            else:
                messagebox.showerror("خطأ", "لم يتم العثور على الصورة")
    
    def show_image_window(self, image_path):
        """عرض الصورة في نافذة منفصلة"""
        image_window = ctk.CTkToplevel(self)
        image_window.title("عرض الصورة")
        image_window.geometry("800x600")
        
        try:
            from PIL import Image, ImageTk
            
            # تحميل وعرض الصورة
            image = Image.open(image_path)
            image.thumbnail((750, 550), Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(image)
            
            image_label = ctk.CTkLabel(image_window, image=photo, text="")
            image_label.pack(expand=True, fill="both", padx=20, pady=20)
            
            # الاحتفاظ بمرجع للصورة
            image_label.image = photo
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في عرض الصورة: {str(e)}")
            image_window.destroy()
    
    def generate_pdf_report(self):
        """إنشاء تقرير PDF"""
        selection = self.results_tree.selection()
        if not selection:
            return
        
        # اختيار مكان حفظ التقرير
        filename = filedialog.asksaveasfilename(
            title="حفظ التقرير",
            defaultextension=".pdf",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                # محاكاة إنشاء تقرير PDF
                messagebox.showinfo("تم", f"تم إنشاء التقرير بنجاح:\n{filename}")
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في إنشاء التقرير: {str(e)}")
    
    def delete_selected_result(self):
        """حذف النتيجة المحددة"""
        selection = self.results_tree.selection()
        if not selection:
            return
        
        if messagebox.askyesno("تأكيد الحذف", "هل تريد حذف هذه النتيجة؟"):
            # حذف من الجدول
            self.results_tree.delete(selection[0])
            
            # مسح التفاصيل
            self.details_text.delete("1.0", "end")
            self.view_image_btn.configure(state="disabled")
            self.generate_report_btn.configure(state="disabled")
            self.delete_result_btn.configure(state="disabled")
    
    def clear_all_results(self):
        """مسح جميع النتائج"""
        if messagebox.askyesno("تأكيد المسح", "هل تريد مسح جميع النتائج؟"):
            # مسح الجدول
            for item in self.results_tree.get_children():
                self.results_tree.delete(item)
            
            # مسح البيانات
            self.results_data = []
            
            # مسح التفاصيل
            self.details_text.delete("1.0", "end")
            self.view_image_btn.configure(state="disabled")
            self.generate_report_btn.configure(state="disabled")
            self.delete_result_btn.configure(state="disabled")
    
    def export_results(self):
        """تصدير النتائج"""
        if not self.results_data:
            messagebox.showwarning("تحذير", "لا توجد نتائج للتصدير")
            return
        
        filename = filedialog.asksaveasfilename(
            title="تصدير النتائج",
            defaultextension=".json",
            filetypes=[
                ("JSON files", "*.json"),
                ("CSV files", "*.csv"),
                ("All files", "*.*")
            ]
        )
        
        if filename:
            try:
                if filename.endswith('.json'):
                    with open(filename, 'w', encoding='utf-8') as f:
                        json.dump(self.results_data, f, ensure_ascii=False, indent=2)
                else:
                    # تصدير CSV (مبسط)
                    import csv
                    with open(filename, 'w', newline='', encoding='utf-8') as f:
                        writer = csv.writer(f)
                        writer.writerow(['التاريخ', 'نوع الفحص', 'الهدف', 'النتيجة'])
                        for result in self.results_data:
                            writer.writerow([
                                result['date'],
                                result['type'],
                                result['target'],
                                result['summary']
                            ])
                
                messagebox.showinfo("تم", f"تم تصدير النتائج بنجاح:\n{filename}")
                
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في التصدير: {str(e)}")
