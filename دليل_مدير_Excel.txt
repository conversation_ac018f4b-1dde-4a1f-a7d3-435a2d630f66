===============================================
    دليل استخدام مدير Excel المتقدم
    Advanced Excel Manager User Guide
===============================================

🎯 نظرة عامة
============
مدير Excel المتقدم هو جزء من تطبيق كاميرا آيـدي يوفر إمكانيات شاملة 
لإدارة وتحليل البيانات باستخدام ملفات Excel.

📋 الميزات الرئيسية
==================

✅ إدارة الملفات:
- فتح وتحرير ملفات Excel (.xlsx, .xls)
- إنشاء ملفات Excel جديدة
- حفظ الملفات بصيغ متعددة
- فحص صحة الملفات

✅ القوالب الاحترافية:
- قالب تقرير الكاميرات
- قالب تقرير الشبكة
- قالب تقرير الأمان
- قالب الإحصائيات

✅ الرسوم البيانية:
- رسوم بيانية دائرية
- رسوم بيانية عمودية
- رسوم بيانية خطية
- إضافة تلقائية للرسوم

✅ تحويل الصيغ:
- تصدير إلى CSV
- تصدير إلى JSON
- تصدير إلى HTML
- دعم التحويل المتعدد

🚀 كيفية الاستخدام
==================

1️⃣ الوصول إلى مدير Excel:
   - شغل تطبيق كاميرا آيـدي
   - انقر على زر "Excel" في شريط القائمة العلوي
   - ستفتح واجهة مدير Excel

2️⃣ فتح ملف Excel موجود:
   - انقر على "فتح ملف"
   - اختر ملف Excel من جهازك
   - سيتم عرض البيانات في أوراق منفصلة
   - يمكنك التنقل بين الأوراق

3️⃣ إنشاء تقرير جديد:
   - انقر على أحد أزرار التقارير
   - اختر مكان الحفظ
   - سيتم إنشاء التقرير باستخدام القالب المناسب

4️⃣ إضافة رسوم بيانية:
   - افتح ملف Excel
   - انقر على "إضافة رسوم بيانية"
   - سيتم إضافة ورقة جديدة تحتوي على الرسوم

5️⃣ تحويل الصيغة:
   - افتح ملف Excel
   - انقر على "تحويل الصيغة"
   - اختر الصيغ المطلوبة
   - انقر على "تحويل"

6️⃣ فحص الملف:
   - افتح ملف Excel
   - انقر على "فحص الملف"
   - ستظهر معلومات مفصلة عن الملف

📊 أمثلة على التقارير
=======================

🎥 تقرير الكاميرات:
- الرقم التسلسلي
- الماركة (Hikvision, Dahua, Axis)
- الموديل
- النوع (Fixed Dome, PTZ)
- الدقة (4MP, 1080p)
- مستوى الثقة
- تاريخ الاكتشاف
- ملاحظات

🌐 تقرير الشبكة:
- الرقم التسلسلي
- عنوان IP
- عنوان MAC
- المصنع
- المنافذ المفتوحة
- نوع الجهاز
- دعم ONVIF
- HTTP Banner
- الحالة

🔒 تقرير الأمان:
- الرقم التسلسلي
- CVE ID
- الماركة
- الموديل
- مستوى الخطورة
- الوصف
- الحل المقترح
- تاريخ النشر

🎨 التنسيق والتصميم
====================

🎨 الألوان المستخدمة:
- الأزرق (#4472C4): العناوين الرئيسية
- الأخضر (#70AD47): رؤوس الأعمدة
- الأحمر (#E74C3C): تقارير الشبكة
- البرتقالي (#FD7E14): تقارير الأمان
- الرمادي الفاتح (#F8F9FA): الصفوف البديلة

📝 التنسيق:
- خطوط واضحة ومقروءة
- حدود للجداول
- تلوين بالتناوب للصفوف
- محاذاة مناسبة للنصوص
- عرض أعمدة محسن

🔧 الإعدادات المتقدمة
======================

📁 تخصيص القوالب:
يمكنك تعديل القوالب في:
excel_files/templates/
- camera_report_template.xlsx
- network_report_template.xlsx
- security_report_template.xlsx
- statistics_template.xlsx

📊 إعدادات الرسوم البيانية:
- الحجم الافتراضي: [10, 6]
- الألوان: ['#2E8B57', '#FFD700', '#DC143C']
- حجم الخط: 12
- الدقة: 300 DPI

🐛 استكشاف الأخطاء
===================

❌ خطأ في فتح الملف:
السبب: ملف تالف أو صيغة غير مدعومة
الحل: تأكد من أن الملف بصيغة .xlsx أو .xls وغير تالف

❌ فشل في إنشاء التقرير:
السبب: عدم وجود صلاحيات كتابة
الحل: تأكد من وجود صلاحيات الكتابة في مجلد الحفظ

❌ مشكلة في الرسوم البيانية:
السبب: بيانات غير صحيحة أو مفقودة
الحل: تأكد من وجود بيانات صحيحة في الملف

❌ خطأ في تحويل الصيغة:
السبب: مشكلة في مكتبة pandas
الحل: تأكد من تثبيت pandas بشكل صحيح

📚 المكتبات المطلوبة
=====================

openpyxl==3.1.2      # قراءة وكتابة ملفات Excel
pandas==2.0.3        # تحليل البيانات
xlsxwriter==3.1.9    # كتابة ملفات Excel متقدمة
xlrd==2.0.1          # قراءة ملفات Excel القديمة
matplotlib==3.7.2    # الرسوم البيانية
seaborn==0.12.2      # رسوم بيانية متقدمة

🔮 ميزات مستقبلية
==================

- تحرير مباشر: تحرير البيانات داخل التطبيق
- قوالب مخصصة: إنشاء قوالب مخصصة
- تصدير PDF: تحويل مباشر إلى PDF
- ربط قواعد البيانات: ربط مع قواعد بيانات خارجية
- تحليل متقدم: تحليل إحصائي أكثر تفصيلاً
- تعاون فريق: مشاركة الملفات مع الفريق

💡 نصائح للاستخدام الأمثل
============================

1. استخدم القوالب: القوالب الجاهزة توفر تنسيقاً احترافياً
2. فحص الملفات: افحص الملفات دورياً للتأكد من سلامتها
3. النسخ الاحتياطية: احتفظ بنسخ احتياطية من الملفات المهمة
4. تحويل الصيغ: استخدم تحويل الصيغ لمشاركة البيانات
5. الرسوم البيانية: أضف الرسوم البيانية لتوضيح البيانات

📞 الدعم والمساعدة
==================

للحصول على المساعدة:
- راجع الوثائق الكاملة
- تواصل مع فريق الدعم
- شارك في المجتمع

===============================================
🎯 صُنع بـ ❤️ للمجتمع العربي
===============================================
