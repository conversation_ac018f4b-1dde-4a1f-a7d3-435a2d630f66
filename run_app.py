#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل تطبيق كاميرا آيـدي
Camera ID Application Launcher
"""

import sys
import os
import subprocess
import platform

def check_python():
    """فحص إصدار Python"""
    if sys.version_info < (3, 8):
        print("خطأ: يتطلب Python 3.8 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    return True

def install_requirements():
    """تثبيت المكتبات المطلوبة"""
    print("تثبيت المكتبات المطلوبة...")
    try:
        # قائمة المكتبات الأساسية
        requirements = [
            'customtkinter',
            'opencv-python',
            'Pillow',
            'pyyaml',
            'requests'
        ]

        for package in requirements:
            print(f"تثبيت {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])

        print("تم تثبيت جميع المكتبات بنجاح!")
        return True

    except subprocess.CalledProcessError as e:
        print(f"فشل في تثبيت المكتبات: {e}")
        return False

def run_application():
    """تشغيل التطبيق"""
    try:
        print("تشغيل تطبيق كاميرا آيـدي...")
        import main
        main.main()
    except ImportError as e:
        print(f"خطأ في الاستيراد: {e}")
        print("جاري تثبيت المكتبات المطلوبة...")
        if install_requirements():
            print("إعادة تشغيل التطبيق...")
            run_application()
        else:
            print("فشل في تثبيت المكتبات. يرجى تثبيتها يدوياً:")
            print("pip install -r requirements.txt")
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")

def main():
    """الدالة الرئيسية"""
    print("كاميرا آيـدي - Camera ID")
    print("=" * 30)

    # فحص Python
    if not check_python():
        input("اضغط Enter للخروج...")
        return

    # تشغيل التطبيق
    run_application()

if __name__ == "__main__":
    main()