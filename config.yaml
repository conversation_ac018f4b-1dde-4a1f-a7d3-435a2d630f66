app:
  name: "كاميرا آيـدي"
  version: "1.0.0"
  language: "ar"
  theme: "dark"
  debug_mode: false

gui:
  window_width: 1200
  window_height: 800
  min_width: 800
  min_height: 600
  font_family: "Arial"
  font_size: 12
  rtl_support: true

cv:
  model_path: "models/camera_detector.tflite"
  confidence_threshold: 0.7
  max_image_size: [1024, 1024]
  supported_formats: [".jpg", ".jpeg", ".png", ".bmp", ".tiff"]

network:
  onvif_timeout: 10
  http_timeout: 5
  max_concurrent_scans: 50
  common_ports: [80, 443, 554, 8080, 8000, 37777]
  default_credentials:
    "admin": "admin"
    "admin_pass": "12345"
    "root": "root"
    "user": "user"

database:
  camera_db_path: "data/cameras.json"
  vulnerability_db_path: "data/vulnerabilities.json"
  oui_db_path: "data/oui.json"
  update_interval: 24

security:
  privacy_mode: true
  local_only: false
  data_retention_days: 30
  auto_check_vulnerabilities: true
  allowed_scan_networks: ["192.168.0.0/16", "10.0.0.0/8", "172.16.0.0/12"]
