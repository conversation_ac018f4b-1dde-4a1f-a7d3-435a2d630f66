#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير Excel المتقدم - Advanced Excel Manager
"""

import pandas as pd
import openpyxl
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.chart import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, Reference
import xlsxwriter
from datetime import datetime
import json
import os
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
import threading

class ExcelManager:
    """مدير Excel الرئيسي"""
    
    def __init__(self, config, logger):
        self.config = config
        self.logger = logger
        
        # إعداد مجلد Excel
        self.excel_dir = config.base_dir / "excel_files"
        self.excel_dir.mkdir(exist_ok=True)
        
        # إعداد القوالب
        self.templates_dir = self.excel_dir / "templates"
        self.templates_dir.mkdir(exist_ok=True)
        
        # إنشاء القوالب الافتراضية
        self.create_default_templates()
        
        self.lock = threading.Lock()
    
    def create_default_templates(self):
        """إنشاء قوالب Excel الافتراضية"""
        try:
            # قالب تقرير الكاميرات
            self.create_camera_report_template()
            
            # قالب تقرير الشبكة
            self.create_network_report_template()
            
            # قالب الثغرات الأمنية
            self.create_security_report_template()
            
            # قالب الإحصائيات
            self.create_statistics_template()
            
            self.logger.info("تم إنشاء قوالب Excel الافتراضية")
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء قوالب Excel: {e}")
    
    def create_camera_report_template(self):
        """إنشاء قالب تقرير الكاميرات"""
        try:
            template_path = self.templates_dir / "camera_report_template.xlsx"
            
            # إنشاء مصنف جديد
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "تقرير الكاميرات"
            
            # تنسيق العنوان
            ws['A1'] = "تقرير الكاميرات المكتشفة"
            ws['A1'].font = Font(size=16, bold=True, color="FFFFFF")
            ws['A1'].fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
            ws['A1'].alignment = Alignment(horizontal="center")
            ws.merge_cells('A1:H1')
            
            # معلومات التقرير
            ws['A3'] = "تاريخ الإنشاء:"
            ws['B3'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            ws['A4'] = "النسخة:"
            ws['B4'] = self.config.get('app.version', '1.0.0')
            
            # رؤوس الأعمدة
            headers = ['الرقم', 'الماركة', 'الموديل', 'النوع', 'الدقة', 'مستوى الثقة', 'تاريخ الاكتشاف', 'ملاحظات']
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=6, column=col, value=header)
                cell.font = Font(bold=True, color="FFFFFF")
                cell.fill = PatternFill(start_color="70AD47", end_color="70AD47", fill_type="solid")
                cell.alignment = Alignment(horizontal="center")
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
            
            # تنسيق العرض
            column_widths = [8, 15, 20, 15, 12, 12, 18, 25]
            for col, width in enumerate(column_widths, 1):
                ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = width
            
            # حفظ القالب
            wb.save(template_path)
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء قالب تقرير الكاميرات: {e}")
    
    def create_network_report_template(self):
        """إنشاء قالب تقرير الشبكة"""
        try:
            template_path = self.templates_dir / "network_report_template.xlsx"
            
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "تقرير الشبكة"
            
            # العنوان
            ws['A1'] = "تقرير فحص الشبكة"
            ws['A1'].font = Font(size=16, bold=True, color="FFFFFF")
            ws['A1'].fill = PatternFill(start_color="E74C3C", end_color="E74C3C", fill_type="solid")
            ws['A1'].alignment = Alignment(horizontal="center")
            ws.merge_cells('A1:I1')
            
            # معلومات التقرير
            ws['A3'] = "تاريخ الفحص:"
            ws['B3'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            ws['A4'] = "نطاق الشبكة:"
            ws['B4'] = "***********/24"
            
            # رؤوس الأعمدة
            headers = ['الرقم', 'عنوان IP', 'عنوان MAC', 'المصنع', 'المنافذ المفتوحة', 'نوع الجهاز', 'دعم ONVIF', 'HTTP Banner', 'الحالة']
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=6, column=col, value=header)
                cell.font = Font(bold=True, color="FFFFFF")
                cell.fill = PatternFill(start_color="3498DB", end_color="3498DB", fill_type="solid")
                cell.alignment = Alignment(horizontal="center")
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
            
            # تنسيق العرض
            column_widths = [8, 15, 18, 15, 20, 15, 12, 25, 12]
            for col, width in enumerate(column_widths, 1):
                ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = width
            
            wb.save(template_path)
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء قالب تقرير الشبكة: {e}")
    
    def create_security_report_template(self):
        """إنشاء قالب تقرير الثغرات الأمنية"""
        try:
            template_path = self.templates_dir / "security_report_template.xlsx"
            
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "تقرير الثغرات الأمنية"
            
            # العنوان
            ws['A1'] = "تقرير الثغرات الأمنية"
            ws['A1'].font = Font(size=16, bold=True, color="FFFFFF")
            ws['A1'].fill = PatternFill(start_color="DC3545", end_color="DC3545", fill_type="solid")
            ws['A1'].alignment = Alignment(horizontal="center")
            ws.merge_cells('A1:H1')
            
            # معلومات التقرير
            ws['A3'] = "تاريخ التدقيق:"
            ws['B3'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            ws['A4'] = "مستوى التدقيق:"
            ws['B4'] = "شامل"
            
            # رؤوس الأعمدة
            headers = ['الرقم', 'CVE ID', 'الماركة', 'الموديل', 'مستوى الخطورة', 'الوصف', 'الحل المقترح', 'تاريخ النشر']
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=6, column=col, value=header)
                cell.font = Font(bold=True, color="FFFFFF")
                cell.fill = PatternFill(start_color="FD7E14", end_color="FD7E14", fill_type="solid")
                cell.alignment = Alignment(horizontal="center")
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
            
            # تنسيق العرض
            column_widths = [8, 15, 15, 20, 15, 30, 30, 15]
            for col, width in enumerate(column_widths, 1):
                ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = width
            
            wb.save(template_path)
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء قالب تقرير الثغرات: {e}")
    
    def create_statistics_template(self):
        """إنشاء قالب الإحصائيات"""
        try:
            template_path = self.templates_dir / "statistics_template.xlsx"
            
            wb = openpyxl.Workbook()
            
            # ورقة الإحصائيات العامة
            ws1 = wb.active
            ws1.title = "الإحصائيات العامة"
            
            # العنوان
            ws1['A1'] = "الإحصائيات العامة"
            ws1['A1'].font = Font(size=16, bold=True, color="FFFFFF")
            ws1['A1'].fill = PatternFill(start_color="28A745", end_color="28A745", fill_type="solid")
            ws1['A1'].alignment = Alignment(horizontal="center")
            ws1.merge_cells('A1:D1')
            
            # جدول الإحصائيات
            stats_headers = ['المؤشر', 'القيمة', 'النسبة المئوية', 'التغيير']
            for col, header in enumerate(stats_headers, 1):
                cell = ws1.cell(row=3, column=col, value=header)
                cell.font = Font(bold=True, color="FFFFFF")
                cell.fill = PatternFill(start_color="6C757D", end_color="6C757D", fill_type="solid")
                cell.alignment = Alignment(horizontal="center")
            
            # ورقة الرسوم البيانية
            ws2 = wb.create_sheet("الرسوم البيانية")
            ws2['A1'] = "الرسوم البيانية"
            ws2['A1'].font = Font(size=16, bold=True)
            ws2['A1'].alignment = Alignment(horizontal="center")
            
            wb.save(template_path)
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء قالب الإحصائيات: {e}")
    
    def load_excel_file(self, file_path: str) -> Optional[Dict[str, pd.DataFrame]]:
        """تحميل ملف Excel"""
        try:
            with self.lock:
                # قراءة جميع الأوراق
                excel_data = pd.read_excel(file_path, sheet_name=None, engine='openpyxl')
                
                self.logger.info(f"تم تحميل ملف Excel: {file_path}")
                return excel_data
                
        except Exception as e:
            self.logger.error(f"خطأ في تحميل ملف Excel: {e}")
            return None
    
    def save_excel_file(self, data: Dict[str, pd.DataFrame], file_path: str, 
                       template_type: str = "custom") -> bool:
        """حفظ ملف Excel"""
        try:
            with self.lock:
                if template_type == "custom":
                    # حفظ بسيط
                    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                        for sheet_name, df in data.items():
                            df.to_excel(writer, sheet_name=sheet_name, index=False)
                else:
                    # استخدام القالب المحدد
                    self.save_with_template(data, file_path, template_type)
                
                self.logger.info(f"تم حفظ ملف Excel: {file_path}")
                return True
                
        except Exception as e:
            self.logger.error(f"خطأ في حفظ ملف Excel: {e}")
            return False
    
    def save_with_template(self, data: Dict[str, pd.DataFrame], file_path: str, template_type: str):
        """حفظ باستخدام قالب محدد"""
        try:
            template_path = self.templates_dir / f"{template_type}_template.xlsx"
            
            if not template_path.exists():
                raise FileNotFoundError(f"القالب غير موجود: {template_path}")
            
            # تحميل القالب
            wb = openpyxl.load_workbook(template_path)
            
            # إضافة البيانات
            for sheet_name, df in data.items():
                if sheet_name in wb.sheetnames:
                    ws = wb[sheet_name]
                    
                    # البحث عن بداية البيانات (بعد الرؤوس)
                    start_row = 7  # افتراضي
                    
                    # إضافة البيانات
                    for r_idx, row in enumerate(dataframe_to_rows(df, index=False, header=False), start_row):
                        for c_idx, value in enumerate(row, 1):
                            cell = ws.cell(row=r_idx, column=c_idx, value=value)
                            
                            # تنسيق الخلايا
                            cell.border = Border(
                                left=Side(style='thin'),
                                right=Side(style='thin'),
                                top=Side(style='thin'),
                                bottom=Side(style='thin')
                            )
                            
                            # تلوين الصفوف بالتناوب
                            if r_idx % 2 == 0:
                                cell.fill = PatternFill(start_color="F8F9FA", end_color="F8F9FA", fill_type="solid")
            
            # حفظ الملف
            wb.save(file_path)
            
        except Exception as e:
            self.logger.error(f"خطأ في حفظ القالب: {e}")
            raise
    
    def create_camera_report(self, camera_data: List[Dict[str, Any]], output_path: str) -> bool:
        """إنشاء تقرير الكاميرات"""
        try:
            # تحويل البيانات إلى DataFrame
            df = pd.DataFrame(camera_data)
            
            # إعادة ترتيب الأعمدة
            columns_order = ['brand', 'model', 'type', 'resolution', 'confidence', 'created_at', 'notes']
            df = df.reindex(columns=[col for col in columns_order if col in df.columns])
            
            # إضافة رقم تسلسلي
            df.insert(0, 'الرقم', range(1, len(df) + 1))
            
            # تنسيق الأعمدة
            if 'confidence' in df.columns:
                df['confidence'] = df['confidence'].apply(lambda x: f"{x:.2%}" if pd.notnull(x) else "غير محدد")
            
            if 'created_at' in df.columns:
                df['created_at'] = pd.to_datetime(df['created_at']).dt.strftime('%Y-%m-%d %H:%M')
            
            # إعادة تسمية الأعمدة للعربية
            column_names = {
                'brand': 'الماركة',
                'model': 'الموديل',
                'type': 'النوع',
                'resolution': 'الدقة',
                'confidence': 'مستوى الثقة',
                'created_at': 'تاريخ الاكتشاف',
                'notes': 'ملاحظات'
            }
            df.rename(columns=column_names, inplace=True)
            
            # حفظ التقرير
            data = {"تقرير الكاميرات": df}
            return self.save_with_template(data, output_path, "camera_report")
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء تقرير الكاميرات: {e}")
            return False
    
    def create_network_report(self, network_data: List[Dict[str, Any]], output_path: str) -> bool:
        """إنشاء تقرير الشبكة"""
        try:
            df = pd.DataFrame(network_data)
            
            # إضافة رقم تسلسلي
            df.insert(0, 'الرقم', range(1, len(df) + 1))
            
            # تنسيق البيانات
            if 'open_ports' in df.columns:
                df['open_ports'] = df['open_ports'].apply(
                    lambda x: ', '.join(map(str, x)) if isinstance(x, list) else str(x)
                )
            
            if 'onvif_support' in df.columns:
                df['onvif_support'] = df['onvif_support'].apply(lambda x: "نعم" if x else "لا")
            
            # إعادة تسمية الأعمدة
            column_names = {
                'ip_address': 'عنوان IP',
                'mac_address': 'عنوان MAC',
                'vendor': 'المصنع',
                'open_ports': 'المنافذ المفتوحة',
                'device_type': 'نوع الجهاز',
                'onvif_support': 'دعم ONVIF',
                'http_banner': 'HTTP Banner',
                'status': 'الحالة'
            }
            df.rename(columns=column_names, inplace=True)
            
            # حفظ التقرير
            data = {"تقرير الشبكة": df}
            return self.save_with_template(data, output_path, "network_report")
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء تقرير الشبكة: {e}")
            return False
    
    def create_security_report(self, vulnerability_data: List[Dict[str, Any]], output_path: str) -> bool:
        """إنشاء تقرير الثغرات الأمنية"""
        try:
            df = pd.DataFrame(vulnerability_data)
            
            # إضافة رقم تسلسلي
            df.insert(0, 'الرقم', range(1, len(df) + 1))
            
            # تنسيق التواريخ
            if 'published_date' in df.columns:
                df['published_date'] = pd.to_datetime(df['published_date']).dt.strftime('%Y-%m-%d')
            
            # إعادة تسمية الأعمدة
            column_names = {
                'cve_id': 'CVE ID',
                'brand': 'الماركة',
                'model': 'الموديل',
                'severity': 'مستوى الخطورة',
                'description': 'الوصف',
                'solution': 'الحل المقترح',
                'published_date': 'تاريخ النشر'
            }
            df.rename(columns=column_names, inplace=True)
            
            # حفظ التقرير
            data = {"تقرير الثغرات الأمنية": df}
            return self.save_with_template(data, output_path, "security_report")
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء تقرير الثغرات: {e}")
            return False
    
    def add_charts_to_excel(self, file_path: str, chart_data: Dict[str, Any]) -> bool:
        """إضافة رسوم بيانية إلى ملف Excel"""
        try:
            wb = openpyxl.load_workbook(file_path)
            
            # إنشاء ورقة للرسوم البيانية
            if "الرسوم البيانية" not in wb.sheetnames:
                ws_charts = wb.create_sheet("الرسوم البيانية")
            else:
                ws_charts = wb["الرسوم البيانية"]
            
            # رسم بياني دائري للماركات
            if 'brands' in chart_data:
                self.add_pie_chart(ws_charts, chart_data['brands'], "توزيع الماركات", "A1")
            
            # رسم بياني عمودي للثقة
            if 'confidence_levels' in chart_data:
                self.add_bar_chart(ws_charts, chart_data['confidence_levels'], "مستويات الثقة", "H1")
            
            # رسم بياني خطي للزمن
            if 'timeline' in chart_data:
                self.add_line_chart(ws_charts, chart_data['timeline'], "التطور الزمني", "A20")
            
            wb.save(file_path)
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في إضافة الرسوم البيانية: {e}")
            return False
    
    def add_pie_chart(self, worksheet, data: Dict[str, int], title: str, position: str):
        """إضافة رسم بياني دائري"""
        try:
            # إضافة البيانات
            row_start = int(position[1:]) + 1
            col_start = ord(position[0]) - ord('A') + 1
            
            for i, (label, value) in enumerate(data.items()):
                worksheet.cell(row=row_start + i, column=col_start, value=label)
                worksheet.cell(row=row_start + i, column=col_start + 1, value=value)
            
            # إنشاء الرسم البياني
            chart = PieChart()
            chart.title = title
            
            # تحديد البيانات
            data_ref = Reference(worksheet, min_col=col_start + 1, min_row=row_start, 
                               max_row=row_start + len(data) - 1)
            labels_ref = Reference(worksheet, min_col=col_start, min_row=row_start, 
                                 max_row=row_start + len(data) - 1)
            
            chart.add_data(data_ref)
            chart.set_categories(labels_ref)
            
            # إضافة الرسم إلى الورقة
            chart_position = f"{chr(ord(position[0]) + 3)}{position[1:]}"
            worksheet.add_chart(chart, chart_position)
            
        except Exception as e:
            self.logger.error(f"خطأ في إضافة الرسم الدائري: {e}")
    
    def add_bar_chart(self, worksheet, data: Dict[str, int], title: str, position: str):
        """إضافة رسم بياني عمودي"""
        try:
            # إضافة البيانات
            row_start = int(position[1:]) + 1
            col_start = ord(position[0]) - ord('A') + 1
            
            for i, (label, value) in enumerate(data.items()):
                worksheet.cell(row=row_start + i, column=col_start, value=label)
                worksheet.cell(row=row_start + i, column=col_start + 1, value=value)
            
            # إنشاء الرسم البياني
            chart = BarChart()
            chart.title = title
            chart.x_axis.title = "الفئات"
            chart.y_axis.title = "القيم"
            
            # تحديد البيانات
            data_ref = Reference(worksheet, min_col=col_start + 1, min_row=row_start, 
                               max_row=row_start + len(data) - 1)
            labels_ref = Reference(worksheet, min_col=col_start, min_row=row_start, 
                                 max_row=row_start + len(data) - 1)
            
            chart.add_data(data_ref)
            chart.set_categories(labels_ref)
            
            # إضافة الرسم إلى الورقة
            chart_position = f"{chr(ord(position[0]) + 3)}{position[1:]}"
            worksheet.add_chart(chart, chart_position)
            
        except Exception as e:
            self.logger.error(f"خطأ في إضافة الرسم العمودي: {e}")
    
    def add_line_chart(self, worksheet, data: Dict[str, int], title: str, position: str):
        """إضافة رسم بياني خطي"""
        try:
            # إضافة البيانات
            row_start = int(position[1:]) + 1
            col_start = ord(position[0]) - ord('A') + 1
            
            for i, (label, value) in enumerate(data.items()):
                worksheet.cell(row=row_start + i, column=col_start, value=label)
                worksheet.cell(row=row_start + i, column=col_start + 1, value=value)
            
            # إنشاء الرسم البياني
            chart = LineChart()
            chart.title = title
            chart.x_axis.title = "الزمن"
            chart.y_axis.title = "العدد"
            
            # تحديد البيانات
            data_ref = Reference(worksheet, min_col=col_start + 1, min_row=row_start, 
                               max_row=row_start + len(data) - 1)
            labels_ref = Reference(worksheet, min_col=col_start, min_row=row_start, 
                                 max_row=row_start + len(data) - 1)
            
            chart.add_data(data_ref)
            chart.set_categories(labels_ref)
            
            # إضافة الرسم إلى الورقة
            chart_position = f"{chr(ord(position[0]) + 3)}{position[1:]}"
            worksheet.add_chart(chart, chart_position)
            
        except Exception as e:
            self.logger.error(f"خطأ في إضافة الرسم الخطي: {e}")
    
    def validate_excel_file(self, file_path: str) -> Dict[str, Any]:
        """التحقق من صحة ملف Excel"""
        try:
            validation_result = {
                "is_valid": True,
                "errors": [],
                "warnings": [],
                "sheets": [],
                "total_rows": 0,
                "file_size": 0
            }
            
            # فحص وجود الملف
            if not os.path.exists(file_path):
                validation_result["is_valid"] = False
                validation_result["errors"].append("الملف غير موجود")
                return validation_result
            
            # فحص حجم الملف
            file_size = os.path.getsize(file_path)
            validation_result["file_size"] = file_size
            
            if file_size > 50 * 1024 * 1024:  # 50 MB
                validation_result["warnings"].append("حجم الملف كبير (أكثر من 50 ميجابايت)")
            
            # فحص محتوى الملف
            try:
                excel_data = pd.read_excel(file_path, sheet_name=None)
                
                for sheet_name, df in excel_data.items():
                    sheet_info = {
                        "name": sheet_name,
                        "rows": len(df),
                        "columns": len(df.columns),
                        "empty_rows": df.isnull().all(axis=1).sum()
                    }
                    validation_result["sheets"].append(sheet_info)
                    validation_result["total_rows"] += len(df)
                
                if validation_result["total_rows"] == 0:
                    validation_result["warnings"].append("الملف فارغ")
                
            except Exception as e:
                validation_result["is_valid"] = False
                validation_result["errors"].append(f"خطأ في قراءة الملف: {str(e)}")
            
            return validation_result
            
        except Exception as e:
            return {
                "is_valid": False,
                "errors": [f"خطأ في التحقق من الملف: {str(e)}"],
                "warnings": [],
                "sheets": [],
                "total_rows": 0,
                "file_size": 0
            }
    
    def get_excel_info(self, file_path: str) -> Dict[str, Any]:
        """الحصول على معلومات ملف Excel"""
        try:
            info = {
                "file_name": os.path.basename(file_path),
                "file_size": os.path.getsize(file_path),
                "created_date": datetime.fromtimestamp(os.path.getctime(file_path)).strftime('%Y-%m-%d %H:%M:%S'),
                "modified_date": datetime.fromtimestamp(os.path.getmtime(file_path)).strftime('%Y-%m-%d %H:%M:%S'),
                "sheets": []
            }
            
            # قراءة معلومات الأوراق
            wb = openpyxl.load_workbook(file_path, read_only=True)
            
            for sheet_name in wb.sheetnames:
                ws = wb[sheet_name]
                sheet_info = {
                    "name": sheet_name,
                    "max_row": ws.max_row,
                    "max_column": ws.max_column,
                    "has_data": ws.max_row > 1 or ws.max_column > 1
                }
                info["sheets"].append(sheet_info)
            
            wb.close()
            return info
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على معلومات Excel: {e}")
            return {}
    
    def convert_to_formats(self, excel_path: str, output_formats: List[str]) -> Dict[str, str]:
        """تحويل ملف Excel إلى صيغ أخرى"""
        try:
            results = {}
            
            # قراءة ملف Excel
            excel_data = pd.read_excel(excel_path, sheet_name=None)
            
            base_name = os.path.splitext(os.path.basename(excel_path))[0]
            output_dir = os.path.dirname(excel_path)
            
            for format_type in output_formats:
                if format_type.lower() == 'csv':
                    # تحويل إلى CSV (الورقة الأولى فقط)
                    first_sheet = list(excel_data.values())[0]
                    csv_path = os.path.join(output_dir, f"{base_name}.csv")
                    first_sheet.to_csv(csv_path, index=False, encoding='utf-8-sig')
                    results['csv'] = csv_path
                
                elif format_type.lower() == 'json':
                    # تحويل إلى JSON
                    json_data = {}
                    for sheet_name, df in excel_data.items():
                        json_data[sheet_name] = df.to_dict('records')
                    
                    json_path = os.path.join(output_dir, f"{base_name}.json")
                    with open(json_path, 'w', encoding='utf-8') as f:
                        json.dump(json_data, f, ensure_ascii=False, indent=2)
                    results['json'] = json_path
                
                elif format_type.lower() == 'html':
                    # تحويل إلى HTML
                    html_path = os.path.join(output_dir, f"{base_name}.html")
                    with open(html_path, 'w', encoding='utf-8') as f:
                        f.write('<html><head><meta charset="utf-8"><title>Excel Data</title></head><body>')
                        for sheet_name, df in excel_data.items():
                            f.write(f'<h2>{sheet_name}</h2>')
                            f.write(df.to_html(index=False, escape=False))
                        f.write('</body></html>')
                    results['html'] = html_path
            
            return results
            
        except Exception as e:
            self.logger.error(f"خطأ في تحويل الملف: {e}")
            return {}
