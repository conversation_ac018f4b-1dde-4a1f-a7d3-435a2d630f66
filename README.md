# كاميرا آيـدي - Camera ID

تطبيق سطح المكتب لتحديد نوع وموديل كاميرات المراقبة باستخدام الرؤية الحاسوبية والفحص الشبكي.

## المميزات الرئيسية

- **الفحص البصري**: تحديد نوع الكاميرا من الصورة باستخدام الذكاء الاصطناعي
- **الفحص الشبكي**: اكتشاف كاميرات IP في الشبكة وتحديد معلوماتها
- **تقارير أمنية**: تحديد الثغرات الأمنية المعروفة
- **واجهة عربية**: دعم كامل للغة العربية
- **تشغيل محلي**: حماية الخصوصية بالعمل دون الحاجة للإنترنت

## متطلبات النظام

- Windows 10/11 أو Linux
- Python 3.8 أو أحدث
- كاميرا ويب (للفحص البصري)
- اتصال بالشبكة المحلية (للفحص الشبكي)

## التثبيت والتشغيل

### الطريقة السهلة (Windows)

1. تأكد من تثبيت Python على النظام
2. انقر نقراً مزدوجاً على `run_camera_id.bat`
3. سيتم تثبيت المكتبات المطلوبة تلقائياً

### الطريقة اليدوية

1. تثبيت المكتبات المطلوبة:
```bash
pip install -r requirements.txt
```

2. تشغيل التطبيق:
```bash
python main.py
```

أو:
```bash
python run_simple.py
```

## كيفية الاستخدام

### الفحص البصري

1. اختر "فحص بصري" من القائمة الجانبية
2. انقر على "تشغيل الكاميرا" أو "تحميل صورة"
3. وجه الكاميرا نحو كاميرا المراقبة المراد فحصها
4. انقر على "تحليل الصورة"
5. ستظهر النتائج مع معلومات الكاميرا والثغرات الأمنية

### الفحص الشبكي

1. اختر "فحص شبكي" من القائمة الجانبية
2. أدخل نطاق الشبكة (مثل: ***********/24)
3. حدد المنافذ المراد فحصها
4. اختر أنواع الفحص المطلوبة
5. انقر على "بدء الفحص"
6. ستظهر النتائج في الجدول

### عرض النتائج

1. اختر "النتائج" من القائمة الجانبية
2. تصفح النتائج السابقة
3. انقر على أي نتيجة لعرض التفاصيل
4. يمكنك تصدير النتائج أو إنشاء تقارير PDF

## هيكل المشروع

```
EGbank/
├── src/
│   ├── core/           # الوحدات الأساسية
│   ├── gui/            # واجهة المستخدم
│   └── utils/          # الأدوات المساعدة
├── data/               # البيانات والنماذج
├── logs/               # ملفات السجلات
├── temp/               # الملفات المؤقتة
├── config.yaml         # ملف الإعدادات
├── requirements.txt    # المكتبات المطلوبة
├── main.py            # الملف الرئيسي
├── run_simple.py      # مشغل مبسط
└── run_camera_id.bat  # مشغل Windows
```

## الإعدادات

يمكن تخصيص التطبيق من خلال ملف `config.yaml`:

- **المظهر**: light, dark, system
- **دقة الكاميرا**: [640, 480]
- **نطاق الشبكة الافتراضي**: ***********/24
- **إعدادات الخصوصية**: العمل المحلي فقط

## الأمان والخصوصية

- التطبيق يعمل محلياً بشكل افتراضي
- لا يتم رفع البيانات للخوادم الخارجية
- يمكن تعطيل حفظ الصور
- فحص الشبكة يتم بإذن المستخدم فقط

## المساهمة

نرحب بالمساهمات لتطوير التطبيق:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. إضافة التحسينات
4. إرسال Pull Request

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والتجاري.

## الدعم

للحصول على المساعدة أو الإبلاغ عن المشاكل، يرجى إنشاء Issue في المستودع.

---

**تحذير**: يرجى استخدام هذا التطبيق بمسؤولية وعدم فحص أجهزة لا تملك إذناً للوصول إليها.
