<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}كاميرا آيـدي - التطبيق الهجين{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --info-color: #06b6d4;
            --dark-color: #1e293b;
            --light-color: #f8fafc;
        }
        
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary-color) !important;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            margin: 2rem auto;
            padding: 2rem;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }
        
        .btn {
            border-radius: 10px;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, var(--primary-color), #3b82f6);
            border: none;
        }
        
        .btn-primary:hover {
            background: linear-gradient(45deg, #1d4ed8, var(--primary-color));
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: linear-gradient(45deg, var(--success-color), #059669);
            border: none;
        }
        
        .btn-danger {
            background: linear-gradient(45deg, var(--danger-color), #dc2626);
            border: none;
        }
        
        .btn-warning {
            background: linear-gradient(45deg, var(--warning-color), #d97706);
            border: none;
        }
        
        .btn-info {
            background: linear-gradient(45deg, var(--info-color), #0891b2);
            border: none;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e2e8f0;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .progress {
            height: 10px;
            border-radius: 10px;
        }
        
        .progress-bar {
            border-radius: 10px;
        }
        
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .table thead th {
            background: var(--primary-color);
            color: white;
            border: none;
            font-weight: 600;
        }
        
        .badge {
            font-size: 0.8rem;
            padding: 0.5rem 0.75rem;
            border-radius: 8px;
        }
        
        .spinner-border {
            width: 1.5rem;
            height: 1.5rem;
        }
        
        .footer {
            background: rgba(30, 41, 59, 0.95);
            color: white;
            padding: 2rem 0;
            margin-top: 3rem;
        }
        
        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .slide-in {
            animation: slideIn 0.5s ease-out;
        }
        
        @keyframes slideIn {
            from { transform: translateX(-100%); }
            to { transform: translateX(0); }
        }
        
        /* Status Indicators */
        .status-online {
            color: var(--success-color);
        }
        
        .status-offline {
            color: var(--danger-color);
        }
        
        .status-scanning {
            color: var(--warning-color);
        }
        
        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 10px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 10px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #1d4ed8;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .main-container {
                margin: 1rem;
                padding: 1rem;
            }
            
            .navbar-brand {
                font-size: 1.2rem;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light sticky-top">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-video me-2"></i>
                كاميرا آيـدي
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-home me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="scanDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-search me-1"></i>
                            الفحص
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/scan/visual">
                                <i class="fas fa-camera me-2"></i>فحص بصري
                            </a></li>
                            <li><a class="dropdown-item" href="/scan/network">
                                <i class="fas fa-network-wired me-2"></i>فحص شبكي
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/results">
                            <i class="fas fa-chart-bar me-1"></i>
                            النتائج
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/database">
                            <i class="fas fa-database me-1"></i>
                            قاعدة البيانات
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <span class="nav-link">
                            <i class="fas fa-circle status-online me-1"></i>
                            <span id="connection-status">متصل</span>
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container">
        <div class="main-container">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'info-circle' }} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            {% block content %}{% endblock %}
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer text-center">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5><i class="fas fa-video me-2"></i>كاميرا آيـدي</h5>
                    <p class="text-muted">تطبيق هجين لتحديد كاميرات المراقبة</p>
                </div>
                <div class="col-md-4">
                    <h6>الميزات</h6>
                    <ul class="list-unstyled text-muted">
                        <li><i class="fas fa-check me-1"></i>فحص بصري ذكي</li>
                        <li><i class="fas fa-check me-1"></i>فحص شبكي متقدم</li>
                        <li><i class="fas fa-check me-1"></i>تقارير شاملة</li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h6>معلومات</h6>
                    <p class="text-muted">
                        الإصدار: 2.0.0<br>
                        النوع: تطبيق هجين<br>
                        <span id="server-time"></span>
                    </p>
                </div>
            </div>
            <hr class="my-4">
            <p class="text-muted">
                &copy; 2024 كاميرا آيـدي. جميع الحقوق محفوظة.
                <span class="float-end">
                    صُنع بـ <i class="fas fa-heart text-danger"></i> للمجتمع العربي
                </span>
            </p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    
    <script>
        // Socket.IO Connection
        const socket = io();
        
        socket.on('connect', function() {
            document.getElementById('connection-status').textContent = 'متصل';
            document.querySelector('.status-online').className = 'fas fa-circle status-online me-1';
        });
        
        socket.on('disconnect', function() {
            document.getElementById('connection-status').textContent = 'منقطع';
            document.querySelector('.status-online').className = 'fas fa-circle status-offline me-1';
        });
        
        // Update server time
        function updateServerTime() {
            const now = new Date();
            document.getElementById('server-time').textContent = 
                'الوقت: ' + now.toLocaleString('ar-SA');
        }
        
        updateServerTime();
        setInterval(updateServerTime, 1000);
        
        // Add fade-in animation to main content
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('.main-container').classList.add('fade-in');
        });
        
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                if (alert.classList.contains('show')) {
                    alert.classList.remove('show');
                    setTimeout(() => alert.remove(), 150);
                }
            });
        }, 5000);
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
