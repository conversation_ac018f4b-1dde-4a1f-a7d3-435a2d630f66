# 📊 مدير Excel المتقدم - Advanced Excel Manager

## نظرة عامة

مدير Excel المتقدم هو جزء من تطبيق كاميرا آيـدي، يوفر إمكانيات شاملة لإدارة وتحليل البيانات باستخدام ملفات Excel.

## ✨ الميزات الرئيسية

### 📁 **إدارة الملفات**
- فتح وتحرير ملفات Excel موجودة (.xlsx, .xls)
- إنشاء ملفات Excel جديدة
- حفظ الملفات بصيغ متعددة
- فحص صحة الملفات والتحقق من البيانات

### 📋 **القوالب الاحترافية**
- **قالب تقرير الكاميرات**: تقرير مفصل للكاميرات المكتشفة
- **قالب تقرير الشبكة**: تحليل شامل لأجهزة الشبكة
- **قالب تقرير الأمان**: تقرير الثغرات الأمنية
- **قالب الإحصائيات**: تحليل إحصائي متقدم

### 📊 **الرسوم البيانية**
- رسوم بيانية دائرية (Pie Charts)
- رسوم بيانية عمودية (Bar Charts)
- رسوم بيانية خطية (Line Charts)
- إضافة تلقائية للرسوم البيانية

### 🔄 **تحويل الصيغ**
- تصدير إلى CSV
- تصدير إلى JSON
- تصدير إلى HTML
- دعم التحويل المتعدد

### ✅ **فحص وتحقق**
- فحص صحة الملفات
- تحليل البيانات المفقودة
- إحصائيات مفصلة للملف
- تقارير التحقق

## 🚀 كيفية الاستخدام

### 1. الوصول إلى مدير Excel
```
1. شغل تطبيق كاميرا آيـدي
2. انقر على زر "Excel" في شريط القائمة العلوي
3. ستفتح واجهة مدير Excel
```

### 2. فتح ملف Excel موجود
```
1. انقر على "فتح ملف"
2. اختر ملف Excel من جهازك
3. سيتم عرض البيانات في أوراق منفصلة
4. يمكنك التنقل بين الأوراق باستخدام التبويبات
```

### 3. إنشاء تقرير جديد
```
1. انقر على أحد أزرار التقارير:
   - "تقرير الكاميرات"
   - "تقرير الشبكة" 
   - "تقرير الأمان"
2. اختر مكان الحفظ
3. سيتم إنشاء التقرير باستخدام القالب المناسب
```

### 4. إضافة رسوم بيانية
```
1. افتح ملف Excel
2. انقر على "إضافة رسوم بيانية"
3. سيتم إضافة ورقة جديدة تحتوي على الرسوم البيانية
```

### 5. تحويل الصيغة
```
1. افتح ملف Excel
2. انقر على "تحويل الصيغة"
3. اختر الصيغ المطلوبة (CSV, JSON, HTML)
4. انقر على "تحويل"
```

### 6. فحص الملف
```
1. افتح ملف Excel
2. انقر على "فحص الملف"
3. ستظهر نافذة تحتوي على:
   - حالة الملف (صحيح/يحتوي على أخطاء)
   - معلومات مفصلة عن الملف
   - الأخطاء والتحذيرات (إن وجدت)
```

## 📊 أمثلة على التقارير

### تقرير الكاميرات
```
الأعمدة المتضمنة:
- الرقم التسلسلي
- الماركة (Hikvision, Dahua, Axis, إلخ)
- الموديل
- النوع (Fixed Dome, PTZ, إلخ)
- الدقة (4MP, 1080p, إلخ)
- مستوى الثقة
- تاريخ الاكتشاف
- ملاحظات
```

### تقرير الشبكة
```
الأعمدة المتضمنة:
- الرقم التسلسلي
- عنوان IP
- عنوان MAC
- المصنع
- المنافذ المفتوحة
- نوع الجهاز
- دعم ONVIF
- HTTP Banner
- الحالة
```

### تقرير الأمان
```
الأعمدة المتضمنة:
- الرقم التسلسلي
- CVE ID
- الماركة
- الموديل
- مستوى الخطورة
- الوصف
- الحل المقترح
- تاريخ النشر
```

## 🎨 التنسيق والتصميم

### الألوان المستخدمة:
- **الأزرق (#4472C4)**: العناوين الرئيسية
- **الأخضر (#70AD47)**: رؤوس الأعمدة
- **الأحمر (#E74C3C)**: تقارير الشبكة
- **البرتقالي (#FD7E14)**: تقارير الأمان
- **الرمادي الفاتح (#F8F9FA)**: الصفوف البديلة

### التنسيق:
- خطوط واضحة ومقروءة
- حدود للجداول
- تلوين بالتناوب للصفوف
- محاذاة مناسبة للنصوص
- عرض أعمدة محسن

## 🔧 الإعدادات المتقدمة

### تخصيص القوالب:
```
يمكنك تعديل القوالب في:
excel_files/templates/
- camera_report_template.xlsx
- network_report_template.xlsx
- security_report_template.xlsx
- statistics_template.xlsx
```

### إعدادات الرسوم البيانية:
```yaml
charts:
  default_size: [10, 6]
  colors: ['#2E8B57', '#FFD700', '#DC143C']
  font_size: 12
  dpi: 300
```

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

**1. خطأ في فتح الملف**
```
السبب: ملف تالف أو صيغة غير مدعومة
الحل: تأكد من أن الملف بصيغة .xlsx أو .xls وغير تالف
```

**2. فشل في إنشاء التقرير**
```
السبب: عدم وجود صلاحيات كتابة
الحل: تأكد من وجود صلاحيات الكتابة في مجلد الحفظ
```

**3. مشكلة في الرسوم البيانية**
```
السبب: بيانات غير صحيحة أو مفقودة
الحل: تأكد من وجود بيانات صحيحة في الملف
```

**4. خطأ في تحويل الصيغة**
```
السبب: مشكلة في مكتبة pandas
الحل: تأكد من تثبيت pandas بشكل صحيح
```

## 📚 مكتبات مطلوبة

```python
openpyxl==3.1.2      # قراءة وكتابة ملفات Excel
pandas==2.0.3        # تحليل البيانات
xlsxwriter==3.1.9    # كتابة ملفات Excel متقدمة
xlrd==2.0.1          # قراءة ملفات Excel القديمة
matplotlib==3.7.2    # الرسوم البيانية
seaborn==0.12.2      # رسوم بيانية متقدمة
```

## 🔮 ميزات مستقبلية

- **تحرير مباشر**: تحرير البيانات داخل التطبيق
- **قوالب مخصصة**: إنشاء قوالب مخصصة
- **تصدير PDF**: تحويل مباشر إلى PDF
- **ربط قواعد البيانات**: ربط مع قواعد بيانات خارجية
- **تحليل متقدم**: تحليل إحصائي أكثر تفصيلاً
- **تعاون فريق**: مشاركة الملفات مع الفريق

## 💡 نصائح للاستخدام الأمثل

1. **استخدم القوالب**: القوالب الجاهزة توفر تنسيقاً احترافياً
2. **فحص الملفات**: افحص الملفات دورياً للتأكد من سلامتها
3. **النسخ الاحتياطية**: احتفظ بنسخ احتياطية من الملفات المهمة
4. **تحويل الصيغ**: استخدم تحويل الصيغ لمشاركة البيانات
5. **الرسوم البيانية**: أضف الرسوم البيانية لتوضيح البيانات

---

**📞 للدعم والمساعدة:**
- راجع الوثائق الكاملة
- تواصل مع فريق الدعم
- شارك في المجتمع

**🎯 صُنع بـ ❤️ للمجتمع العربي**
