#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل مبسط لتطبيق كاميرا آيـدي
Simple launcher for Camera ID application
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# إضافة مسار المشروع إلى Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_dependencies():
    """فحص المكتبات المطلوبة"""
    missing_packages = []
    
    try:
        import customtkinter
    except ImportError:
        missing_packages.append("customtkinter")
    
    try:
        import cv2
    except ImportError:
        missing_packages.append("opencv-python")
    
    try:
        from PIL import Image
    except ImportError:
        missing_packages.append("Pillow")
    
    try:
        import numpy
    except ImportError:
        missing_packages.append("numpy")
    
    try:
        import yaml
    except ImportError:
        missing_packages.append("pyyaml")
    
    return missing_packages

def install_dependencies(packages):
    """تثبيت المكتبات المفقودة"""
    import subprocess
    
    for package in packages:
        try:
            print(f"تثبيت {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"تم تثبيت {package} بنجاح")
        except subprocess.CalledProcessError:
            print(f"فشل في تثبيت {package}")
            return False
    return True

def main():
    """تشغيل التطبيق"""
    print("بدء تشغيل تطبيق كاميرا آيـدي...")
    
    # فحص المكتبات المطلوبة
    missing = check_dependencies()
    
    if missing:
        print(f"المكتبات المفقودة: {', '.join(missing)}")
        
        # إنشاء نافذة تأكيد
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        
        response = messagebox.askyesno(
            "مكتبات مفقودة",
            f"المكتبات التالية مطلوبة:\n{chr(10).join(missing)}\n\nهل تريد تثبيتها الآن؟"
        )
        
        root.destroy()
        
        if response:
            if install_dependencies(missing):
                print("تم تثبيت جميع المكتبات بنجاح")
            else:
                print("فشل في تثبيت بعض المكتبات")
                return
        else:
            print("تم إلغاء التثبيت")
            return
    
    try:
        # استيراد وتشغيل التطبيق
        from src.gui.main_window import CameraIDApp
        from src.core.config import Config
        from src.utils.logger import setup_logger
        import customtkinter as ctk
        
        # إعداد المظهر
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        
        # إنشاء وتشغيل التطبيق
        print("تشغيل التطبيق...")
        app = CameraIDApp()
        app.run()
        
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        
        # عرض رسالة خطأ
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("خطأ", f"حدث خطأ في تشغيل التطبيق:\n{str(e)}")
        root.destroy()

if __name__ == "__main__":
    main()
