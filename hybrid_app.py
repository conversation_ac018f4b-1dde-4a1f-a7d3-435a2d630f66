#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق كاميرا آيـدي الهجين - Camera ID Hybrid Application
تطبيق قوي يعمل على سطح المكتب والويب معاً
"""

import sys
import os
import threading
import webbrowser
import socket
from pathlib import Path
import asyncio
import json
import time
from datetime import datetime
import logging
from typing import Optional, Dict, Any, List

# Web Framework
from flask import Flask, render_template, request, jsonify, send_file, session
from flask_socketio import SocketIO, emit, join_room, leave_room
from flask_cors import CORS
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from werkzeug.security import generate_password_hash, check_password_hash

# Desktop Framework
import customtkinter as ctk
from tkinter import messagebox, filedialog
import tkinter as tk

# Database
import sqlite3
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Float, Text, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, scoped_session

# Data Processing
import pandas as pd
import numpy as np
from PIL import Image, ImageTk
import cv2

# Security
from cryptography.fernet import Fernet
import hashlib
import secrets
import jwt

# Configuration
import yaml
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# إعداد المسارات
BASE_DIR = Path(__file__).parent
TEMPLATES_DIR = BASE_DIR / "templates"
STATIC_DIR = BASE_DIR / "static"
DATA_DIR = BASE_DIR / "data"
UPLOADS_DIR = BASE_DIR / "uploads"
EXPORTS_DIR = BASE_DIR / "exports"

# إنشاء المجلدات المطلوبة
for directory in [TEMPLATES_DIR, STATIC_DIR, DATA_DIR, UPLOADS_DIR, EXPORTS_DIR]:
    directory.mkdir(exist_ok=True)

# إعداد قاعدة البيانات
DATABASE_URL = f"sqlite:///{DATA_DIR}/camera_id_hybrid.db"
Base = declarative_base()

class ScanResult(Base):
    """نموذج نتائج الفحص"""
    __tablename__ = 'scan_results'

    id = Column(Integer, primary_key=True)
    scan_type = Column(String(50), nullable=False)
    timestamp = Column(DateTime, default=datetime.utcnow)
    ip_address = Column(String(45))
    brand = Column(String(100))
    model = Column(String(200))
    confidence = Column(Float)
    features = Column(Text)
    vulnerabilities = Column(Text)
    image_path = Column(String(500))
    notes = Column(Text)
    user_id = Column(String(100))
    session_id = Column(String(100))

class User(Base):
    """نموذج المستخدمين"""
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True)
    username = Column(String(80), unique=True, nullable=False)
    email = Column(String(120), unique=True, nullable=False)
    password_hash = Column(String(200), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    is_active = Column(Boolean, default=True)
    role = Column(String(50), default='user')

class HybridCameraIDApp:
    """التطبيق الهجين الرئيسي"""

    def __init__(self):
        self.setup_logging()
        self.setup_database()
        self.setup_security()
        self.setup_flask_app()
        self.setup_desktop_app()
        self.running = False

    def setup_logging(self):
        """إعداد نظام السجلات"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(DATA_DIR / 'app.log', encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        self.logger.info("🚀 بدء تشغيل التطبيق الهجين")

    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            self.engine = create_engine(DATABASE_URL, echo=False)
            Base.metadata.create_all(self.engine)
            self.Session = scoped_session(sessionmaker(bind=self.engine))
            self.logger.info("✅ تم إعداد قاعدة البيانات بنجاح")
        except Exception as e:
            self.logger.error(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
            raise

    def setup_security(self):
        """إعداد الأمان والتشفير"""
        # إنشاء مفتاح التشفير
        key_file = DATA_DIR / 'encryption.key'
        if key_file.exists():
            with open(key_file, 'rb') as f:
                self.encryption_key = f.read()
        else:
            self.encryption_key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(self.encryption_key)

        self.cipher = Fernet(self.encryption_key)

        # JWT Secret
        self.jwt_secret = os.getenv('JWT_SECRET', secrets.token_urlsafe(32))

        self.logger.info("🔒 تم إعداد نظام الأمان")

    def setup_flask_app(self):
        """إعداد تطبيق Flask"""
        self.app = Flask(__name__,
                        template_folder=str(TEMPLATES_DIR),
                        static_folder=str(STATIC_DIR))

        # إعدادات Flask
        self.app.config.update({
            'SECRET_KEY': os.getenv('SECRET_KEY', secrets.token_urlsafe(32)),
            'UPLOAD_FOLDER': str(UPLOADS_DIR),
            'MAX_CONTENT_LENGTH': 50 * 1024 * 1024,  # 50MB
            'WTF_CSRF_ENABLED': True,
            'SESSION_COOKIE_SECURE': True,
            'SESSION_COOKIE_HTTPONLY': True,
            'PERMANENT_SESSION_LIFETIME': 3600,  # 1 hour
        })

        # إضافات Flask
        CORS(self.app, origins=['http://localhost:*', 'http://127.0.0.1:*'])
        self.socketio = SocketIO(self.app, cors_allowed_origins="*", async_mode='threading')

        # Rate Limiting
        self.limiter = Limiter(
            app=self.app,
            key_func=get_remote_address,
            default_limits=["200 per day", "50 per hour"]
        )

        self.setup_routes()
        self.setup_socketio_events()

        self.logger.info("🌐 تم إعداد تطبيق الويب")

    def setup_desktop_app(self):
        """إعداد تطبيق سطح المكتب"""
        # إعداد CustomTkinter
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")

        self.desktop_root = ctk.CTk()
        self.desktop_root.title("كاميرا آيـدي - Camera ID Hybrid")
        self.desktop_root.geometry("1200x800")
        self.desktop_root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # متغيرات التطبيق
        self.web_server_running = False
        self.web_port = 5000

        self.create_desktop_ui()
        self.logger.info("🖥️ تم إعداد تطبيق سطح المكتب")

    def create_desktop_ui(self):
        """إنشاء واجهة سطح المكتب"""
        # الإطار الرئيسي
        main_frame = ctk.CTkFrame(self.desktop_root)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # العنوان
        title_label = ctk.CTkLabel(
            main_frame,
            text="كاميرا آيـدي - التطبيق الهجين",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)

        # إطار الأزرار
        buttons_frame = ctk.CTkFrame(main_frame)
        buttons_frame.pack(fill="x", padx=20, pady=10)

        # زر تشغيل الخادم
        self.server_btn = ctk.CTkButton(
            buttons_frame,
            text="🌐 تشغيل خادم الويب",
            command=self.toggle_web_server,
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40
        )
        self.server_btn.pack(side="left", padx=10, pady=10)

        # زر فتح المتصفح
        self.browser_btn = ctk.CTkButton(
            buttons_frame,
            text="🔗 فتح في المتصفح",
            command=self.open_browser,
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            state="disabled"
        )
        self.browser_btn.pack(side="left", padx=10, pady=10)

        # زر الفحص البصري
        visual_btn = ctk.CTkButton(
            buttons_frame,
            text="📷 فحص بصري",
            command=self.visual_scan_desktop,
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40
        )
        visual_btn.pack(side="left", padx=10, pady=10)

        # زر الفحص الشبكي
        network_btn = ctk.CTkButton(
            buttons_frame,
            text="🌐 فحص شبكي",
            command=self.network_scan_desktop,
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40
        )
        network_btn.pack(side="left", padx=10, pady=10)

        # إطار المعلومات
        info_frame = ctk.CTkFrame(main_frame)
        info_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # نص المعلومات
        self.info_text = ctk.CTkTextbox(
            info_frame,
            font=ctk.CTkFont(size=12),
            wrap="word"
        )
        self.info_text.pack(fill="both", expand=True, padx=10, pady=10)

        # إضافة معلومات أولية
        self.update_info("🎯 مرحباً بك في التطبيق الهجين لكاميرا آيـدي\n")
        self.update_info("✨ يمكنك استخدام التطبيق على سطح المكتب أو الويب\n")
        self.update_info("🔧 انقر على 'تشغيل خادم الويب' لبدء الخادم\n")
        self.update_info("🌐 ثم انقر على 'فتح في المتصفح' للوصول للواجهة الويب\n\n")

    def update_info(self, message: str):
        """تحديث نص المعلومات"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.info_text.insert("end", f"[{timestamp}] {message}")
        self.info_text.see("end")
        self.desktop_root.update()

    def setup_routes(self):
        """إعداد مسارات Flask"""

        @self.app.route('/')
        def index():
            return render_template('index.html')

        @self.app.route('/api/status')
        def api_status():
            return jsonify({
                'status': 'running',
                'timestamp': datetime.now().isoformat(),
                'version': '2.0.0',
                'mode': 'hybrid'
            })

        @self.app.route('/api/scan/visual', methods=['POST'])
        @self.limiter.limit("10 per minute")
        def api_visual_scan():
            try:
                if 'image' not in request.files:
                    return jsonify({'error': 'لا توجد صورة'}), 400

                file = request.files['image']
                if file.filename == '':
                    return jsonify({'error': 'لم يتم اختيار ملف'}), 400

                # حفظ الصورة
                filename = f"scan_{int(time.time())}_{file.filename}"
                filepath = UPLOADS_DIR / filename
                file.save(filepath)

                # تحليل الصورة
                result = self.analyze_image(str(filepath))

                # حفظ النتيجة
                self.save_scan_result('visual', result, str(filepath))

                return jsonify(result)

            except Exception as e:
                self.logger.error(f"خطأ في الفحص البصري: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/scan/network', methods=['POST'])
        @self.limiter.limit("5 per minute")
        def api_network_scan():
            try:
                data = request.get_json()
                ip_range = data.get('ip_range', '***********/24')
                ports = data.get('ports', [80, 554, 8080])

                # تشغيل الفحص
                results = self.network_scan(ip_range, ports)

                return jsonify({'results': results})

            except Exception as e:
                self.logger.error(f"خطأ في الفحص الشبكي: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/results')
        def api_results():
            try:
                session_db = self.Session()
                results = session_db.query(ScanResult).order_by(ScanResult.timestamp.desc()).limit(100).all()

                data = []
                for result in results:
                    data.append({
                        'id': result.id,
                        'scan_type': result.scan_type,
                        'timestamp': result.timestamp.isoformat(),
                        'ip_address': result.ip_address,
                        'brand': result.brand,
                        'model': result.model,
                        'confidence': result.confidence,
                        'features': result.features,
                        'vulnerabilities': result.vulnerabilities
                    })

                session_db.close()
                return jsonify(data)

            except Exception as e:
                self.logger.error(f"خطأ في جلب النتائج: {e}")
                return jsonify({'error': str(e)}), 500

    def setup_socketio_events(self):
        """إعداد أحداث SocketIO للتحديثات المباشرة"""

        @self.socketio.on('connect')
        def handle_connect():
            self.logger.info(f"عميل متصل: {request.sid}")
            emit('status', {'message': 'متصل بنجاح'})

        @self.socketio.on('disconnect')
        def handle_disconnect():
            self.logger.info(f"عميل منقطع: {request.sid}")

        @self.socketio.on('start_scan')
        def handle_start_scan(data):
            scan_type = data.get('type', 'unknown')
            self.logger.info(f"بدء فحص {scan_type}")
            emit('scan_progress', {'status': 'started', 'type': scan_type})

    def analyze_image(self, image_path: str) -> Dict[str, Any]:
        """تحليل الصورة باستخدام الذكاء الاصطناعي"""
        try:
            # تحميل الصورة
            image = cv2.imread(image_path)
            if image is None:
                return {'error': 'فشل في تحميل الصورة'}

            # تحليل أساسي
            height, width = image.shape[:2]

            # كشف الميزات
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # حساب الإحصائيات
            mean_brightness = np.mean(gray)
            contrast = np.std(gray)

            # تحديد نوع الكاميرا (محاكاة)
            confidence = min(0.9, (contrast / 100) + 0.3)

            result = {
                'brand': 'Hikvision' if confidence > 0.7 else 'Generic',
                'model': 'DS-2CD2142FWD-I' if confidence > 0.7 else 'IP Camera',
                'confidence': round(confidence, 2),
                'resolution': f"{width}x{height}",
                'brightness': round(mean_brightness, 2),
                'contrast': round(contrast, 2),
                'features': ['Night Vision', 'Motion Detection'],
                'vulnerabilities': ['CVE-2017-7921'] if confidence > 0.7 else []
            }

            return result

        except Exception as e:
            self.logger.error(f"خطأ في تحليل الصورة: {e}")
            return {'error': str(e)}

    def network_scan(self, ip_range: str, ports: List[int]) -> List[Dict[str, Any]]:
        """فحص الشبكة"""
        results = []
        try:
            import ipaddress

            network = ipaddress.ip_network(ip_range, strict=False)

            for host in list(network.hosts())[:20]:  # محدود لـ20 مضيف
                host_ip = str(host)

                # فحص المنافذ
                open_ports = []
                for port in ports:
                    if self.check_port(host_ip, port, timeout=1):
                        open_ports.append(port)

                if open_ports:
                    # محاكاة تحديد نوع الجهاز
                    device_info = {
                        'ip_address': host_ip,
                        'ports': open_ports,
                        'brand': 'Dahua' if 80 in open_ports else 'Generic',
                        'model': 'IPC-HDW4431C-A' if 80 in open_ports else 'IP Camera',
                        'confidence': 0.8 if 80 in open_ports else 0.5,
                        'features': ['Web Interface', 'RTSP Stream'],
                        'vulnerabilities': ['Default Credentials']
                    }

                    results.append(device_info)

                    # حفظ النتيجة
                    self.save_scan_result('network', device_info)

            return results

        except Exception as e:
            self.logger.error(f"خطأ في فحص الشبكة: {e}")
            return []

    def check_port(self, ip: str, port: int, timeout: int = 3) -> bool:
        """فحص منفذ محدد"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            result = sock.connect_ex((ip, port))
            sock.close()
            return result == 0
        except:
            return False

    def save_scan_result(self, scan_type: str, result: Dict[str, Any], image_path: str = None):
        """حفظ نتيجة الفحص في قاعدة البيانات"""
        try:
            session_db = self.Session()

            scan_result = ScanResult(
                scan_type=scan_type,
                ip_address=result.get('ip_address'),
                brand=result.get('brand'),
                model=result.get('model'),
                confidence=result.get('confidence'),
                features=json.dumps(result.get('features', []), ensure_ascii=False),
                vulnerabilities=json.dumps(result.get('vulnerabilities', []), ensure_ascii=False),
                image_path=image_path,
                session_id=session.get('session_id', 'desktop')
            )

            session_db.add(scan_result)
            session_db.commit()
            session_db.close()

            self.logger.info(f"تم حفظ نتيجة الفحص: {scan_type}")

        except Exception as e:
            self.logger.error(f"خطأ في حفظ النتيجة: {e}")

    def toggle_web_server(self):
        """تشغيل/إيقاف خادم الويب"""
        if not self.web_server_running:
            self.start_web_server()
        else:
            self.stop_web_server()

    def start_web_server(self):
        """تشغيل خادم الويب"""
        try:
            # البحث عن منفذ متاح
            self.web_port = self.find_free_port()

            # تشغيل الخادم في خيط منفصل
            self.web_thread = threading.Thread(
                target=self.run_web_server,
                daemon=True
            )
            self.web_thread.start()

            self.web_server_running = True
            self.server_btn.configure(text="🛑 إيقاف خادم الويب")
            self.browser_btn.configure(state="normal")

            self.update_info(f"✅ تم تشغيل خادم الويب على المنفذ {self.web_port}\n")
            self.update_info(f"🔗 الرابط: http://localhost:{self.web_port}\n")

        except Exception as e:
            self.logger.error(f"خطأ في تشغيل الخادم: {e}")
            self.update_info(f"❌ خطأ في تشغيل الخادم: {e}\n")

    def stop_web_server(self):
        """إيقاف خادم الويب"""
        self.web_server_running = False
        self.server_btn.configure(text="🌐 تشغيل خادم الويب")
        self.browser_btn.configure(state="disabled")
        self.update_info("🛑 تم إيقاف خادم الويب\n")

    def run_web_server(self):
        """تشغيل خادم Flask"""
        try:
            self.socketio.run(
                self.app,
                host='0.0.0.0',
                port=self.web_port,
                debug=False,
                use_reloader=False
            )
        except Exception as e:
            self.logger.error(f"خطأ في خادم الويب: {e}")

    def find_free_port(self, start_port: int = 5000) -> int:
        """البحث عن منفذ متاح"""
        for port in range(start_port, start_port + 100):
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.bind(('localhost', port))
                sock.close()
                return port
            except:
                continue
        return start_port

    def open_browser(self):
        """فتح المتصفح"""
        if self.web_server_running:
            url = f"http://localhost:{self.web_port}"
            webbrowser.open(url)
            self.update_info(f"🌐 تم فتح المتصفح: {url}\n")

    def visual_scan_desktop(self):
        """فحص بصري من سطح المكتب"""
        file_path = filedialog.askopenfilename(
            title="اختر صورة الكاميرا",
            filetypes=[
                ("صور", "*.jpg *.jpeg *.png *.bmp *.tiff"),
                ("جميع الملفات", "*.*")
            ]
        )

        if file_path:
            self.update_info(f"📷 بدء تحليل الصورة: {os.path.basename(file_path)}\n")

            # تحليل الصورة
            result = self.analyze_image(file_path)

            if 'error' not in result:
                self.update_info(f"✅ تم التحليل بنجاح:\n")
                self.update_info(f"   الماركة: {result.get('brand', 'غير محدد')}\n")
                self.update_info(f"   الموديل: {result.get('model', 'غير محدد')}\n")
                self.update_info(f"   مستوى الثقة: {result.get('confidence', 0):.2f}\n")
                self.update_info(f"   الدقة: {result.get('resolution', 'غير محدد')}\n\n")
            else:
                self.update_info(f"❌ خطأ في التحليل: {result['error']}\n")

    def network_scan_desktop(self):
        """فحص شبكي من سطح المكتب"""
        # نافذة إدخال نطاق الشبكة
        dialog = ctk.CTkInputDialog(
            text="أدخل نطاق الشبكة:",
            title="فحص الشبكة"
        )
        ip_range = dialog.get_input()

        if ip_range:
            self.update_info(f"🌐 بدء فحص الشبكة: {ip_range}\n")

            # تشغيل الفحص في خيط منفصل
            scan_thread = threading.Thread(
                target=self.run_network_scan_desktop,
                args=(ip_range,),
                daemon=True
            )
            scan_thread.start()

    def run_network_scan_desktop(self, ip_range: str):
        """تشغيل فحص الشبكة في خيط منفصل"""
        try:
            results = self.network_scan(ip_range, [80, 554, 8080, 37777])

            if results:
                self.update_info(f"✅ تم العثور على {len(results)} جهاز:\n")
                for result in results:
                    self.update_info(f"   📍 {result['ip_address']} - {result['brand']} {result['model']}\n")
            else:
                self.update_info("❌ لم يتم العثور على أجهزة\n")

        except Exception as e:
            self.update_info(f"❌ خطأ في فحص الشبكة: {e}\n")

    def on_closing(self):
        """عند إغلاق التطبيق"""
        if self.web_server_running:
            self.stop_web_server()

        self.logger.info("👋 إغلاق التطبيق")
        self.desktop_root.destroy()

    def run_desktop(self):
        """تشغيل تطبيق سطح المكتب"""
        self.logger.info("🖥️ تشغيل واجهة سطح المكتب")
        self.desktop_root.mainloop()

    def run_web_only(self, port: int = 5000):
        """تشغيل الويب فقط"""
        self.logger.info(f"🌐 تشغيل خادم الويب على المنفذ {port}")
        self.socketio.run(self.app, host='0.0.0.0', port=port, debug=False)

def main():
    """الدالة الرئيسية"""
    app = HybridCameraIDApp()

    # فحص المعاملات
    if len(sys.argv) > 1:
        if sys.argv[1] == '--web-only':
            port = int(sys.argv[2]) if len(sys.argv) > 2 else 5000
            app.run_web_only(port)
        elif sys.argv[1] == '--desktop-only':
            app.run_desktop()
        else:
            print("الاستخدام: python hybrid_app.py [--web-only [port] | --desktop-only]")
    else:
        # تشغيل هجين (افتراضي)
        app.run_desktop()

if __name__ == '__main__':
    main()