#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الإعدادات المتقدمة - Advanced Settings
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, colorchooser
import customtkinter as ctk
from PIL import Image, ImageTk
import json
import os

class AdvancedSettingsWindow:
    """نافذة الإعدادات المتقدمة"""
    
    def __init__(self, parent, config):
        self.parent = parent
        self.config = config
        self.changes_made = False
        
        # إنشاء النافذة
        self.window = ctk.CTkToplevel(parent)
        self.setup_window()
        self.create_widgets()
        self.load_settings()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("الإعدادات المتقدمة")
        self.window.geometry("800x600")
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # وضع النافذة في المنتصف
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (800 // 2)
        y = (self.window.winfo_screenheight() // 2) - (600 // 2)
        self.window.geometry(f"800x600+{x}+{y}")
        
        # معالج إغلاق النافذة
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        
        # الشريط العلوي
        header_frame = ctk.CTkFrame(self.window)
        header_frame.pack(fill="x", padx=20, pady=(20, 10))
        
        title_label = ctk.CTkLabel(
            header_frame,
            text="⚙️ الإعدادات المتقدمة",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
        
        # الإطار الرئيسي
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # إنشاء التبويبات
        self.tabview = ctk.CTkTabview(main_frame)
        self.tabview.pack(fill="both", expand=True, padx=10, pady=10)
        
        # إضافة التبويبات
        self.create_appearance_tab()
        self.create_camera_tab()
        self.create_network_tab()
        self.create_ai_tab()
        self.create_security_tab()
        self.create_advanced_tab()
        
        # شريط الأزرار
        buttons_frame = ctk.CTkFrame(self.window)
        buttons_frame.pack(fill="x", padx=20, pady=(10, 20))
        
        # أزرار التحكم
        self.save_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ",
            command=self.save_settings,
            width=100,
            height=35
        )
        self.save_btn.pack(side="left", padx=10, pady=10)
        
        self.reset_btn = ctk.CTkButton(
            buttons_frame,
            text="🔄 إعادة تعيين",
            command=self.reset_settings,
            width=120,
            height=35
        )
        self.reset_btn.pack(side="left", padx=10, pady=10)
        
        self.export_btn = ctk.CTkButton(
            buttons_frame,
            text="📤 تصدير",
            command=self.export_settings,
            width=100,
            height=35
        )
        self.export_btn.pack(side="left", padx=10, pady=10)
        
        self.import_btn = ctk.CTkButton(
            buttons_frame,
            text="📥 استيراد",
            command=self.import_settings,
            width=100,
            height=35
        )
        self.import_btn.pack(side="left", padx=10, pady=10)
        
        self.cancel_btn = ctk.CTkButton(
            buttons_frame,
            text="❌ إلغاء",
            command=self.cancel_settings,
            width=100,
            height=35
        )
        self.cancel_btn.pack(side="right", padx=10, pady=10)
    
    def create_appearance_tab(self):
        """تبويب المظهر"""
        tab = self.tabview.add("المظهر")
        
        # إعدادات المظهر
        appearance_frame = ctk.CTkFrame(tab)
        appearance_frame.pack(fill="x", padx=10, pady=10)
        
        appearance_title = ctk.CTkLabel(
            appearance_frame,
            text="إعدادات المظهر",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        appearance_title.pack(pady=10)
        
        # اختيار المظهر
        theme_frame = ctk.CTkFrame(appearance_frame, fg_color="transparent")
        theme_frame.pack(fill="x", padx=10, pady=5)
        
        theme_label = ctk.CTkLabel(theme_frame, text="المظهر:")
        theme_label.pack(side="right", padx=10)
        
        self.theme_var = tk.StringVar()
        self.theme_menu = ctk.CTkOptionMenu(
            theme_frame,
            values=["light", "dark", "system"],
            variable=self.theme_var,
            command=self.on_theme_change
        )
        self.theme_menu.pack(side="left", padx=10)
        
        # اختيار اللون الأساسي
        color_frame = ctk.CTkFrame(appearance_frame, fg_color="transparent")
        color_frame.pack(fill="x", padx=10, pady=5)
        
        color_label = ctk.CTkLabel(color_frame, text="اللون الأساسي:")
        color_label.pack(side="right", padx=10)
        
        self.color_var = tk.StringVar()
        self.color_menu = ctk.CTkOptionMenu(
            color_frame,
            values=["blue", "green", "dark-blue", "red", "orange"],
            variable=self.color_var
        )
        self.color_menu.pack(side="left", padx=10)
        
        # حجم الخط
        font_frame = ctk.CTkFrame(appearance_frame, fg_color="transparent")
        font_frame.pack(fill="x", padx=10, pady=5)
        
        font_label = ctk.CTkLabel(font_frame, text="حجم الخط:")
        font_label.pack(side="right", padx=10)
        
        self.font_size_var = tk.IntVar()
        self.font_size_slider = ctk.CTkSlider(
            font_frame,
            from_=10,
            to=20,
            number_of_steps=10,
            variable=self.font_size_var
        )
        self.font_size_slider.pack(side="left", padx=10, fill="x", expand=True)
        
        self.font_size_label = ctk.CTkLabel(font_frame, text="12")
        self.font_size_label.pack(side="left", padx=5)
        
        # ربط تحديث قيمة الخط
        self.font_size_slider.configure(command=self.update_font_size_label)
    
    def create_camera_tab(self):
        """تبويب الكاميرا"""
        tab = self.tabview.add("الكاميرا")
        
        # إعدادات الكاميرا
        camera_frame = ctk.CTkFrame(tab)
        camera_frame.pack(fill="x", padx=10, pady=10)
        
        camera_title = ctk.CTkLabel(
            camera_frame,
            text="إعدادات الكاميرا",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        camera_title.pack(pady=10)
        
        # جهاز الكاميرا الافتراضي
        device_frame = ctk.CTkFrame(camera_frame, fg_color="transparent")
        device_frame.pack(fill="x", padx=10, pady=5)
        
        device_label = ctk.CTkLabel(device_frame, text="جهاز الكاميرا:")
        device_label.pack(side="right", padx=10)
        
        self.camera_device_var = tk.IntVar()
        self.camera_device_entry = ctk.CTkEntry(
            device_frame,
            textvariable=self.camera_device_var,
            width=100
        )
        self.camera_device_entry.pack(side="left", padx=10)
        
        # دقة الكاميرا
        resolution_frame = ctk.CTkFrame(camera_frame, fg_color="transparent")
        resolution_frame.pack(fill="x", padx=10, pady=5)
        
        resolution_label = ctk.CTkLabel(resolution_frame, text="الدقة:")
        resolution_label.pack(side="right", padx=10)
        
        self.resolution_var = tk.StringVar()
        self.resolution_menu = ctk.CTkOptionMenu(
            resolution_frame,
            values=["640x480", "800x600", "1024x768", "1280x720", "1920x1080"],
            variable=self.resolution_var
        )
        self.resolution_menu.pack(side="left", padx=10)
        
        # معدل الإطارات
        fps_frame = ctk.CTkFrame(camera_frame, fg_color="transparent")
        fps_frame.pack(fill="x", padx=10, pady=5)
        
        fps_label = ctk.CTkLabel(fps_frame, text="معدل الإطارات:")
        fps_label.pack(side="right", padx=10)
        
        self.fps_var = tk.IntVar()
        self.fps_slider = ctk.CTkSlider(
            fps_frame,
            from_=15,
            to=60,
            number_of_steps=9,
            variable=self.fps_var
        )
        self.fps_slider.pack(side="left", padx=10, fill="x", expand=True)
        
        self.fps_label = ctk.CTkLabel(fps_frame, text="30")
        self.fps_label.pack(side="left", padx=5)
        
        self.fps_slider.configure(command=self.update_fps_label)
    
    def create_network_tab(self):
        """تبويب الشبكة"""
        tab = self.tabview.add("الشبكة")
        
        # إعدادات الشبكة
        network_frame = ctk.CTkFrame(tab)
        network_frame.pack(fill="x", padx=10, pady=10)
        
        network_title = ctk.CTkLabel(
            network_frame,
            text="إعدادات الشبكة",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        network_title.pack(pady=10)
        
        # نطاق الشبكة الافتراضي
        range_frame = ctk.CTkFrame(network_frame, fg_color="transparent")
        range_frame.pack(fill="x", padx=10, pady=5)
        
        range_label = ctk.CTkLabel(range_frame, text="نطاق الشبكة:")
        range_label.pack(side="right", padx=10)
        
        self.network_range_var = tk.StringVar()
        self.network_range_entry = ctk.CTkEntry(
            range_frame,
            textvariable=self.network_range_var,
            width=200
        )
        self.network_range_entry.pack(side="left", padx=10)
        
        # مهلة الاتصال
        timeout_frame = ctk.CTkFrame(network_frame, fg_color="transparent")
        timeout_frame.pack(fill="x", padx=10, pady=5)
        
        timeout_label = ctk.CTkLabel(timeout_frame, text="مهلة الاتصال (ثانية):")
        timeout_label.pack(side="right", padx=10)
        
        self.timeout_var = tk.IntVar()
        self.timeout_slider = ctk.CTkSlider(
            timeout_frame,
            from_=1,
            to=10,
            number_of_steps=9,
            variable=self.timeout_var
        )
        self.timeout_slider.pack(side="left", padx=10, fill="x", expand=True)
        
        self.timeout_label = ctk.CTkLabel(timeout_frame, text="5")
        self.timeout_label.pack(side="left", padx=5)
        
        self.timeout_slider.configure(command=self.update_timeout_label)
        
        # عدد الخيوط
        threads_frame = ctk.CTkFrame(network_frame, fg_color="transparent")
        threads_frame.pack(fill="x", padx=10, pady=5)
        
        threads_label = ctk.CTkLabel(threads_frame, text="عدد الخيوط:")
        threads_label.pack(side="right", padx=10)
        
        self.threads_var = tk.IntVar()
        self.threads_slider = ctk.CTkSlider(
            threads_frame,
            from_=1,
            to=20,
            number_of_steps=19,
            variable=self.threads_var
        )
        self.threads_slider.pack(side="left", padx=10, fill="x", expand=True)
        
        self.threads_label = ctk.CTkLabel(threads_frame, text="10")
        self.threads_label.pack(side="left", padx=5)
        
        self.threads_slider.configure(command=self.update_threads_label)
    
    def create_ai_tab(self):
        """تبويب الذكاء الاصطناعي"""
        tab = self.tabview.add("الذكاء الاصطناعي")
        
        # إعدادات الذكاء الاصطناعي
        ai_frame = ctk.CTkFrame(tab)
        ai_frame.pack(fill="x", padx=10, pady=10)
        
        ai_title = ctk.CTkLabel(
            ai_frame,
            text="إعدادات الذكاء الاصطناعي",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        ai_title.pack(pady=10)
        
        # عتبة الثقة
        confidence_frame = ctk.CTkFrame(ai_frame, fg_color="transparent")
        confidence_frame.pack(fill="x", padx=10, pady=5)
        
        confidence_label = ctk.CTkLabel(confidence_frame, text="عتبة الثقة:")
        confidence_label.pack(side="right", padx=10)
        
        self.confidence_var = tk.DoubleVar()
        self.confidence_slider = ctk.CTkSlider(
            confidence_frame,
            from_=0.1,
            to=1.0,
            number_of_steps=18,
            variable=self.confidence_var
        )
        self.confidence_slider.pack(side="left", padx=10, fill="x", expand=True)
        
        self.confidence_label = ctk.CTkLabel(confidence_frame, text="0.7")
        self.confidence_label.pack(side="left", padx=5)
        
        self.confidence_slider.configure(command=self.update_confidence_label)
        
        # استخدام GPU
        gpu_frame = ctk.CTkFrame(ai_frame, fg_color="transparent")
        gpu_frame.pack(fill="x", padx=10, pady=5)
        
        self.use_gpu_var = tk.BooleanVar()
        self.use_gpu_check = ctk.CTkCheckBox(
            gpu_frame,
            text="استخدام GPU للتسريع",
            variable=self.use_gpu_var
        )
        self.use_gpu_check.pack(side="right", padx=10)
        
        # مسار النموذج
        model_frame = ctk.CTkFrame(ai_frame, fg_color="transparent")
        model_frame.pack(fill="x", padx=10, pady=5)
        
        model_label = ctk.CTkLabel(model_frame, text="مسار النموذج:")
        model_label.pack(side="right", padx=10)
        
        self.model_path_var = tk.StringVar()
        self.model_path_entry = ctk.CTkEntry(
            model_frame,
            textvariable=self.model_path_var,
            width=300
        )
        self.model_path_entry.pack(side="left", padx=10, fill="x", expand=True)
        
        self.browse_model_btn = ctk.CTkButton(
            model_frame,
            text="تصفح",
            command=self.browse_model_path,
            width=80
        )
        self.browse_model_btn.pack(side="left", padx=5)
    
    def create_security_tab(self):
        """تبويب الأمان"""
        tab = self.tabview.add("الأمان")
        
        # إعدادات الأمان
        security_frame = ctk.CTkFrame(tab)
        security_frame.pack(fill="x", padx=10, pady=10)
        
        security_title = ctk.CTkLabel(
            security_frame,
            text="إعدادات الأمان والخصوصية",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        security_title.pack(pady=10)
        
        # العمل المحلي فقط
        local_frame = ctk.CTkFrame(security_frame, fg_color="transparent")
        local_frame.pack(fill="x", padx=10, pady=5)
        
        self.local_only_var = tk.BooleanVar()
        self.local_only_check = ctk.CTkCheckBox(
            local_frame,
            text="العمل المحلي فقط (بدون إنترنت)",
            variable=self.local_only_var
        )
        self.local_only_check.pack(side="right", padx=10)
        
        # حفظ الصور
        save_images_frame = ctk.CTkFrame(security_frame, fg_color="transparent")
        save_images_frame.pack(fill="x", padx=10, pady=5)
        
        self.save_images_var = tk.BooleanVar()
        self.save_images_check = ctk.CTkCheckBox(
            save_images_frame,
            text="حفظ الصور المحللة",
            variable=self.save_images_var
        )
        self.save_images_check.pack(side="right", padx=10)
        
        # التقارير المجهولة
        anonymous_frame = ctk.CTkFrame(security_frame, fg_color="transparent")
        anonymous_frame.pack(fill="x", padx=10, pady=5)
        
        self.anonymous_var = tk.BooleanVar()
        self.anonymous_check = ctk.CTkCheckBox(
            anonymous_frame,
            text="إرسال تقارير مجهولة لتحسين التطبيق",
            variable=self.anonymous_var
        )
        self.anonymous_check.pack(side="right", padx=10)
    
    def create_advanced_tab(self):
        """تبويب الإعدادات المتقدمة"""
        tab = self.tabview.add("متقدم")
        
        # إعدادات متقدمة
        advanced_frame = ctk.CTkFrame(tab)
        advanced_frame.pack(fill="x", padx=10, pady=10)
        
        advanced_title = ctk.CTkLabel(
            advanced_frame,
            text="إعدادات متقدمة",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        advanced_title.pack(pady=10)
        
        # مستوى السجلات
        log_frame = ctk.CTkFrame(advanced_frame, fg_color="transparent")
        log_frame.pack(fill="x", padx=10, pady=5)
        
        log_label = ctk.CTkLabel(log_frame, text="مستوى السجلات:")
        log_label.pack(side="right", padx=10)
        
        self.log_level_var = tk.StringVar()
        self.log_level_menu = ctk.CTkOptionMenu(
            log_frame,
            values=["DEBUG", "INFO", "WARNING", "ERROR"],
            variable=self.log_level_var
        )
        self.log_level_menu.pack(side="left", padx=10)
        
        # التحديثات التلقائية
        auto_update_frame = ctk.CTkFrame(advanced_frame, fg_color="transparent")
        auto_update_frame.pack(fill="x", padx=10, pady=5)
        
        self.auto_update_var = tk.BooleanVar()
        self.auto_update_check = ctk.CTkCheckBox(
            auto_update_frame,
            text="فحص التحديثات تلقائياً",
            variable=self.auto_update_var
        )
        self.auto_update_check.pack(side="right", padx=10)
        
        # مجلد البيانات
        data_dir_frame = ctk.CTkFrame(advanced_frame, fg_color="transparent")
        data_dir_frame.pack(fill="x", padx=10, pady=5)
        
        data_dir_label = ctk.CTkLabel(data_dir_frame, text="مجلد البيانات:")
        data_dir_label.pack(side="right", padx=10)
        
        self.data_dir_var = tk.StringVar()
        self.data_dir_entry = ctk.CTkEntry(
            data_dir_frame,
            textvariable=self.data_dir_var,
            width=300
        )
        self.data_dir_entry.pack(side="left", padx=10, fill="x", expand=True)
        
        self.browse_data_btn = ctk.CTkButton(
            data_dir_frame,
            text="تصفح",
            command=self.browse_data_dir,
            width=80
        )
        self.browse_data_btn.pack(side="left", padx=5)
    
    def load_settings(self):
        """تحميل الإعدادات الحالية"""
        # تحميل إعدادات المظهر
        self.theme_var.set(self.config.get("ui.theme", "dark"))
        self.color_var.set(self.config.get("ui.color_theme", "blue"))
        self.font_size_var.set(self.config.get("ui.font_size", 12))
        
        # تحميل إعدادات الكاميرا
        self.camera_device_var.set(self.config.get("camera.default_device", 0))
        resolution = self.config.get("camera.resolution", [640, 480])
        self.resolution_var.set(f"{resolution[0]}x{resolution[1]}")
        self.fps_var.set(self.config.get("camera.fps", 30))
        
        # تحميل إعدادات الشبكة
        self.network_range_var.set(self.config.get("network.scan_range", "192.168.1.0/24"))
        self.timeout_var.set(self.config.get("network.timeout", 5))
        self.threads_var.set(self.config.get("network.max_threads", 10))
        
        # تحميل إعدادات الذكاء الاصطناعي
        self.confidence_var.set(self.config.get("ml.confidence_threshold", 0.7))
        self.use_gpu_var.set(self.config.get("ml.use_gpu", False))
        self.model_path_var.set(self.config.get("ml.model_path", "models/camera_classifier.h5"))
        
        # تحميل إعدادات الأمان
        self.local_only_var.set(self.config.get("privacy.local_only", True))
        self.save_images_var.set(self.config.get("privacy.save_images", False))
        self.anonymous_var.set(self.config.get("privacy.anonymous_reporting", False))
        
        # تحميل الإعدادات المتقدمة
        self.log_level_var.set(self.config.get("logging.level", "INFO"))
        self.auto_update_var.set(self.config.get("updates.auto_check", True))
        self.data_dir_var.set(str(self.config.data_dir))
        
        # تحديث التسميات
        self.update_font_size_label(self.font_size_var.get())
        self.update_fps_label(self.fps_var.get())
        self.update_timeout_label(self.timeout_var.get())
        self.update_threads_label(self.threads_var.get())
        self.update_confidence_label(self.confidence_var.get())
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # حفظ إعدادات المظهر
            self.config.set("ui.theme", self.theme_var.get())
            self.config.set("ui.color_theme", self.color_var.get())
            self.config.set("ui.font_size", self.font_size_var.get())
            
            # حفظ إعدادات الكاميرا
            self.config.set("camera.default_device", self.camera_device_var.get())
            resolution_str = self.resolution_var.get()
            width, height = map(int, resolution_str.split('x'))
            self.config.set("camera.resolution", [width, height])
            self.config.set("camera.fps", self.fps_var.get())
            
            # حفظ إعدادات الشبكة
            self.config.set("network.scan_range", self.network_range_var.get())
            self.config.set("network.timeout", self.timeout_var.get())
            self.config.set("network.max_threads", self.threads_var.get())
            
            # حفظ إعدادات الذكاء الاصطناعي
            self.config.set("ml.confidence_threshold", self.confidence_var.get())
            self.config.set("ml.use_gpu", self.use_gpu_var.get())
            self.config.set("ml.model_path", self.model_path_var.get())
            
            # حفظ إعدادات الأمان
            self.config.set("privacy.local_only", self.local_only_var.get())
            self.config.set("privacy.save_images", self.save_images_var.get())
            self.config.set("privacy.anonymous_reporting", self.anonymous_var.get())
            
            # حفظ الإعدادات المتقدمة
            self.config.set("logging.level", self.log_level_var.get())
            self.config.set("updates.auto_check", self.auto_update_var.get())
            
            messagebox.showinfo("تم", "تم حفظ الإعدادات بنجاح")
            self.changes_made = False
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الإعدادات: {str(e)}")
    
    def reset_settings(self):
        """إعادة تعيين الإعدادات للقيم الافتراضية"""
        if messagebox.askyesno("تأكيد", "هل تريد إعادة تعيين جميع الإعدادات للقيم الافتراضية؟"):
            # إعادة تعيين القيم الافتراضية
            self.theme_var.set("dark")
            self.color_var.set("blue")
            self.font_size_var.set(12)
            
            self.camera_device_var.set(0)
            self.resolution_var.set("640x480")
            self.fps_var.set(30)
            
            self.network_range_var.set("192.168.1.0/24")
            self.timeout_var.set(5)
            self.threads_var.set(10)
            
            self.confidence_var.set(0.7)
            self.use_gpu_var.set(False)
            self.model_path_var.set("models/camera_classifier.h5")
            
            self.local_only_var.set(True)
            self.save_images_var.set(False)
            self.anonymous_var.set(False)
            
            self.log_level_var.set("INFO")
            self.auto_update_var.set(True)
            
            # تحديث التسميات
            self.update_font_size_label(12)
            self.update_fps_label(30)
            self.update_timeout_label(5)
            self.update_threads_label(10)
            self.update_confidence_label(0.7)
            
            self.changes_made = True
    
    def export_settings(self):
        """تصدير الإعدادات"""
        filename = filedialog.asksaveasfilename(
            title="تصدير الإعدادات",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                settings_data = {
                    "ui": {
                        "theme": self.theme_var.get(),
                        "color_theme": self.color_var.get(),
                        "font_size": self.font_size_var.get()
                    },
                    "camera": {
                        "default_device": self.camera_device_var.get(),
                        "resolution": self.resolution_var.get(),
                        "fps": self.fps_var.get()
                    },
                    "network": {
                        "scan_range": self.network_range_var.get(),
                        "timeout": self.timeout_var.get(),
                        "max_threads": self.threads_var.get()
                    },
                    "ml": {
                        "confidence_threshold": self.confidence_var.get(),
                        "use_gpu": self.use_gpu_var.get(),
                        "model_path": self.model_path_var.get()
                    },
                    "privacy": {
                        "local_only": self.local_only_var.get(),
                        "save_images": self.save_images_var.get(),
                        "anonymous_reporting": self.anonymous_var.get()
                    },
                    "advanced": {
                        "log_level": self.log_level_var.get(),
                        "auto_update": self.auto_update_var.get()
                    }
                }
                
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(settings_data, f, ensure_ascii=False, indent=2)
                
                messagebox.showinfo("تم", f"تم تصدير الإعدادات إلى:\n{filename}")
                
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في تصدير الإعدادات: {str(e)}")
    
    def import_settings(self):
        """استيراد الإعدادات"""
        filename = filedialog.askopenfilename(
            title="استيراد الإعدادات",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    settings_data = json.load(f)
                
                # تطبيق الإعدادات المستوردة
                if "ui" in settings_data:
                    ui = settings_data["ui"]
                    self.theme_var.set(ui.get("theme", "dark"))
                    self.color_var.set(ui.get("color_theme", "blue"))
                    self.font_size_var.set(ui.get("font_size", 12))
                
                if "camera" in settings_data:
                    camera = settings_data["camera"]
                    self.camera_device_var.set(camera.get("default_device", 0))
                    self.resolution_var.set(camera.get("resolution", "640x480"))
                    self.fps_var.set(camera.get("fps", 30))
                
                # ... باقي الإعدادات
                
                messagebox.showinfo("تم", "تم استيراد الإعدادات بنجاح")
                self.changes_made = True
                
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في استيراد الإعدادات: {str(e)}")
    
    def cancel_settings(self):
        """إلغاء التغييرات"""
        if self.changes_made:
            if messagebox.askyesno("تأكيد", "هل تريد إلغاء التغييرات؟"):
                self.window.destroy()
        else:
            self.window.destroy()
    
    def on_closing(self):
        """معالج إغلاق النافذة"""
        self.cancel_settings()
    
    def on_theme_change(self, theme):
        """معالج تغيير المظهر"""
        ctk.set_appearance_mode(theme)
        self.changes_made = True
    
    def update_font_size_label(self, value):
        """تحديث تسمية حجم الخط"""
        self.font_size_label.configure(text=str(int(value)))
        self.changes_made = True
    
    def update_fps_label(self, value):
        """تحديث تسمية معدل الإطارات"""
        self.fps_label.configure(text=str(int(value)))
        self.changes_made = True
    
    def update_timeout_label(self, value):
        """تحديث تسمية مهلة الاتصال"""
        self.timeout_label.configure(text=str(int(value)))
        self.changes_made = True
    
    def update_threads_label(self, value):
        """تحديث تسمية عدد الخيوط"""
        self.threads_label.configure(text=str(int(value)))
        self.changes_made = True
    
    def update_confidence_label(self, value):
        """تحديث تسمية عتبة الثقة"""
        self.confidence_label.configure(text=f"{value:.2f}")
        self.changes_made = True
    
    def browse_model_path(self):
        """تصفح مسار النموذج"""
        filename = filedialog.askopenfilename(
            title="اختر ملف النموذج",
            filetypes=[("Model files", "*.h5 *.pb *.onnx"), ("All files", "*.*")]
        )
        
        if filename:
            self.model_path_var.set(filename)
            self.changes_made = True
    
    def browse_data_dir(self):
        """تصفح مجلد البيانات"""
        directory = filedialog.askdirectory(title="اختر مجلد البيانات")
        
        if directory:
            self.data_dir_var.set(directory)
            self.changes_made = True
