========================================
    كاميرا آيـدي - دليل الاستخدام السريع
========================================

🚀 التشغيل السريع:
==================

1. تشغيل مباشر (الأسهل):
   - انقر نقراً مزدوجاً على: run_camera_id.bat

2. تشغيل يدوي:
   - افتح Command Prompt
   - اكتب: python start_app.py

3. إذا ظهرت رسالة "مكتبة مفقودة":
   - انقر نقراً مزدوجاً على: install_requirements.bat
   - انتظر انتهاء التثبيت
   - ثم شغل التطبيق مرة أخرى

📱 استخدام التطبيق:
==================

الفحص البصري:
--------------
1. اختر "فحص بصري" من القائمة اليمنى
2. انقر "تشغيل الكاميرا" أو "تحميل صورة"
3. وجه الكاميرا نحو كاميرا المراقبة
4. انقر "تحليل الصورة"
5. ستظهر النتائج مع نوع الكاميرا والثغرات

الفحص الشبكي:
--------------
1. اختر "فحص شبكي" من القائمة اليمنى
2. أدخل نطاق الشبكة (مثل: 192.168.1.0/24)
3. اختر المنافذ المراد فحصها
4. انقر "بدء الفحص"
5. ستظهر الكاميرات المكتشفة في الجدول

عرض النتائج:
------------
1. اختر "النتائج" من القائمة اليمنى
2. تصفح النتائج السابقة
3. انقر على أي نتيجة لعرض التفاصيل
4. يمكنك تصدير النتائج أو طباعة تقارير

⚙️ الإعدادات:
=============
- انقر على "الإعدادات" في الأعلى
- يمكنك تغيير المظهر (فاتح/داكن)
- تعديل إعدادات الكاميرا والشبكة

🔒 الأمان:
==========
- التطبيق يعمل محلياً فقط
- لا يرسل بيانات للإنترنت
- استخدم فقط على الشبكات التي تملك إذناً لفحصها

❓ المساعدة:
============
- انقر على "المساعدة" في الأعلى للمزيد من التفاصيل
- راجع ملف README.md للمعلومات الكاملة

🐛 حل المشاكل:
===============
- إذا لم تعمل الكاميرا: تأكد من توصيلها وعدم استخدامها من تطبيق آخر
- إذا لم يعمل الفحص الشبكي: تأكد من الاتصال بالشبكة
- إذا ظهرت أخطاء: تأكد من تثبيت جميع المكتبات المطلوبة

========================================
تم تطوير التطبيق بواسطة فريق كاميرا آيـدي
========================================
