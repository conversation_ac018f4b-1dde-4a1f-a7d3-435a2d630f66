#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التحديثات الاحترافي - Professional Update System
"""

import requests
import json
import os
import zipfile
import shutil
import threading
from datetime import datetime, timedelta
from pathlib import Path
import hashlib
import subprocess
import sys

class UpdateManager:
    """مدير التحديثات"""
    
    def __init__(self, config, logger):
        self.config = config
        self.logger = logger
        self.current_version = config.version
        self.update_server = "https://api.cameraid.com/updates"  # خادم وهمي
        self.update_available = False
        self.latest_version = None
        self.update_info = None
    
    def check_for_updates(self, silent=True):
        """فحص التحديثات المتاحة"""
        try:
            # محاكاة فحص التحديثات
            self.logger.info("فحص التحديثات...")
            
            # في التطبيق الحقيقي، سيتم الاتصال بالخادم
            # response = requests.get(f"{self.update_server}/check", 
            #                        params={"version": self.current_version})
            
            # محاكاة استجابة الخادم
            mock_response = {
                "update_available": True,
                "latest_version": "1.1.0",
                "release_date": "2024-02-01",
                "download_url": "https://releases.cameraid.com/v1.1.0/camera-id-v1.1.0.zip",
                "changelog": [
                    "إضافة دعم كاميرات جديدة",
                    "تحسين دقة التعرف",
                    "إصلاح مشاكل الأداء",
                    "تحديث قاعدة بيانات الثغرات الأمنية"
                ],
                "size_mb": 45.2,
                "critical": False,
                "min_version": "1.0.0"
            }
            
            if self.is_newer_version(mock_response["latest_version"]):
                self.update_available = True
                self.latest_version = mock_response["latest_version"]
                self.update_info = mock_response
                
                if not silent:
                    self.logger.info(f"تحديث متاح: الإصدار {self.latest_version}")
                
                return True
            else:
                self.update_available = False
                if not silent:
                    self.logger.info("التطبيق محدث إلى أحدث إصدار")
                
                return False
                
        except Exception as e:
            self.logger.error(f"خطأ في فحص التحديثات: {e}")
            return False
    
    def is_newer_version(self, version):
        """فحص ما إذا كان الإصدار أحدث"""
        try:
            current_parts = [int(x) for x in self.current_version.split('.')]
            new_parts = [int(x) for x in version.split('.')]
            
            # مقارنة أجزاء الإصدار
            for i in range(max(len(current_parts), len(new_parts))):
                current_part = current_parts[i] if i < len(current_parts) else 0
                new_part = new_parts[i] if i < len(new_parts) else 0
                
                if new_part > current_part:
                    return True
                elif new_part < current_part:
                    return False
            
            return False
        except:
            return False
    
    def download_update(self, progress_callback=None):
        """تحميل التحديث"""
        if not self.update_available:
            return False
        
        try:
            self.logger.info(f"بدء تحميل التحديث {self.latest_version}")
            
            # إنشاء مجلد التحديثات
            updates_dir = self.config.base_dir / "updates"
            updates_dir.mkdir(exist_ok=True)
            
            # مسار ملف التحديث
            update_file = updates_dir / f"update-{self.latest_version}.zip"
            
            # محاكاة تحميل الملف
            total_size = int(self.update_info["size_mb"] * 1024 * 1024)
            downloaded = 0
            chunk_size = 8192
            
            # محاكاة عملية التحميل
            with open(update_file, 'wb') as f:
                while downloaded < total_size:
                    chunk_size_actual = min(chunk_size, total_size - downloaded)
                    f.write(b'0' * chunk_size_actual)
                    downloaded += chunk_size_actual
                    
                    if progress_callback:
                        progress = (downloaded / total_size) * 100
                        progress_callback(progress)
                    
                    # محاكاة سرعة التحميل
                    import time
                    time.sleep(0.01)
            
            self.logger.info("تم تحميل التحديث بنجاح")
            return str(update_file)
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل التحديث: {e}")
            return False
    
    def install_update(self, update_file):
        """تثبيت التحديث"""
        try:
            self.logger.info("بدء تثبيت التحديث...")
            
            # إنشاء نسخة احتياطية
            backup_dir = self.config.base_dir / "backup" / f"backup-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # نسخ الملفات الحالية
            important_files = ["src", "config.yaml", "main.py"]
            for item in important_files:
                src_path = self.config.base_dir / item
                if src_path.exists():
                    if src_path.is_dir():
                        shutil.copytree(src_path, backup_dir / item)
                    else:
                        shutil.copy2(src_path, backup_dir / item)
            
            # استخراج التحديث (محاكاة)
            self.logger.info("استخراج ملفات التحديث...")
            
            # في التطبيق الحقيقي، سيتم استخراج الملفات الفعلية
            # with zipfile.ZipFile(update_file, 'r') as zip_ref:
            #     zip_ref.extractall(self.config.base_dir)
            
            # تحديث ملف الإعدادات
            self.config.version = self.latest_version
            self.config.set("app.last_update", datetime.now().isoformat())
            
            self.logger.info("تم تثبيت التحديث بنجاح")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في تثبيت التحديث: {e}")
            return False
    
    def schedule_auto_check(self):
        """جدولة فحص التحديثات التلقائي"""
        def auto_check():
            while True:
                try:
                    # فحص كل 24 ساعة
                    import time
                    time.sleep(24 * 60 * 60)
                    
                    if self.config.get("updates.auto_check", True):
                        self.check_for_updates(silent=True)
                        
                except Exception as e:
                    self.logger.error(f"خطأ في الفحص التلقائي: {e}")
        
        # تشغيل في خيط منفصل
        auto_thread = threading.Thread(target=auto_check)
        auto_thread.daemon = True
        auto_thread.start()
    
    def get_changelog(self):
        """الحصول على قائمة التغييرات"""
        if self.update_info:
            return self.update_info.get("changelog", [])
        return []
    
    def get_update_size(self):
        """الحصول على حجم التحديث"""
        if self.update_info:
            return self.update_info.get("size_mb", 0)
        return 0
    
    def is_critical_update(self):
        """فحص ما إذا كان التحديث حرج"""
        if self.update_info:
            return self.update_info.get("critical", False)
        return False

class UpdateDialog:
    """نافذة حوار التحديث"""
    
    def __init__(self, parent, update_manager):
        self.parent = parent
        self.update_manager = update_manager
        self.result = None
        
        # إنشاء النافذة
        import customtkinter as ctk
        self.dialog = ctk.CTkToplevel(parent)
        self.setup_dialog()
    
    def setup_dialog(self):
        """إعداد نافذة الحوار"""
        import customtkinter as ctk
        
        self.dialog.title("تحديث متاح")
        self.dialog.geometry("500x400")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # وضع النافذة في المنتصف
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"500x400+{x}+{y}")
        
        # المحتوى
        main_frame = ctk.CTkFrame(self.dialog)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = ctk.CTkLabel(
            main_frame,
            text="🔄 تحديث جديد متاح",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=20)
        
        # معلومات التحديث
        info_frame = ctk.CTkFrame(main_frame)
        info_frame.pack(fill="x", padx=10, pady=10)
        
        version_label = ctk.CTkLabel(
            info_frame,
            text=f"الإصدار الجديد: {self.update_manager.latest_version}",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        version_label.pack(pady=10)
        
        size_label = ctk.CTkLabel(
            info_frame,
            text=f"حجم التحديث: {self.update_manager.get_update_size():.1f} ميجابايت"
        )
        size_label.pack(pady=5)
        
        # قائمة التغييرات
        changelog_label = ctk.CTkLabel(
            main_frame,
            text="التحسينات الجديدة:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        changelog_label.pack(pady=(20, 10))
        
        changelog_text = ctk.CTkTextbox(main_frame, height=150)
        changelog_text.pack(fill="x", padx=10, pady=10)
        
        # إضافة قائمة التغييرات
        changelog = self.update_manager.get_changelog()
        for item in changelog:
            changelog_text.insert("end", f"• {item}\n")
        
        changelog_text.configure(state="disabled")
        
        # الأزرار
        buttons_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", pady=20)
        
        update_btn = ctk.CTkButton(
            buttons_frame,
            text="تحديث الآن",
            command=self.update_now,
            width=120,
            height=35
        )
        update_btn.pack(side="left", padx=10)
        
        later_btn = ctk.CTkButton(
            buttons_frame,
            text="لاحقاً",
            command=self.update_later,
            width=120,
            height=35
        )
        later_btn.pack(side="left", padx=10)
        
        skip_btn = ctk.CTkButton(
            buttons_frame,
            text="تخطي هذا الإصدار",
            command=self.skip_version,
            width=120,
            height=35
        )
        skip_btn.pack(side="right", padx=10)
    
    def update_now(self):
        """تحديث الآن"""
        self.result = "update"
        self.dialog.destroy()
    
    def update_later(self):
        """تحديث لاحقاً"""
        self.result = "later"
        self.dialog.destroy()
    
    def skip_version(self):
        """تخطي هذا الإصدار"""
        self.result = "skip"
        self.dialog.destroy()
    
    def show(self):
        """عرض النافذة والانتظار للنتيجة"""
        self.dialog.wait_window()
        return self.result
