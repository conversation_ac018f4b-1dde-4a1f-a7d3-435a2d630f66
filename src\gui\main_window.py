
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النافذة الرئيسية - Main Window
واجهة المستخدم الرئيسية لتطبيق كاميرا آيـدي
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import customtkinter as ctk
from PIL import Image, ImageTk
import cv2
import threading
import os
from datetime import datetime
from typing import Optional, Dict, Any, Callable

from ..core.config import Config
from ..utils.logger import setup_logger

class ArabicLabel(ctk.CTkLabel):
    """تسمية نصية مع دعم اللغة العربية"""
    
    def __init__(self, master, text="", **kwargs):
        # تعيين الخط والاتجاه للنص العربي
        if 'font' not in kwargs:
            kwargs['font'] = ctk.CTkFont(family="Arial", size=12)
        super().__init__(master, text=text, **kwargs)

class ArabicButton(ctk.CTkButton):
    """زر مع دعم اللغة العربية"""
    
    def __init__(self, master, text="", **kwargs):
        if 'font' not in kwargs:
            kwargs['font'] = ctk.CTkFont(family="Arial", size=12, weight="bold")
        super().__init__(master, text=text, **kwargs)

class ScanModeFrame(ctk.CTkFrame):
    """إطار اختيار نوع الفحص"""
    
    def __init__(self, master, on_mode_select: Callable):
        super().__init__(master)
        self.on_mode_select = on_mode_select
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # عنوان الإطار
        title_label = ArabicLabel(
            self, 
            text="اختر طريقة الفحص",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=20)
        
        # أزرار أنواع الفحص
        scan_modes = [
            {
                "text": "🔍 مسح بصري (Computer Vision)",
                "description": "التعرف على الكاميرا من الصورة باستخدام الذكاء الاصطناعي",
                "mode": "visual",
                "color": "#1f538d"
            },
            {
                "text": "🌐 فحص شبكي (ONVIF/HTTP)",
                "description": "فحص الكاميرات عبر الشبكة واستخراج المعلومات",
                "mode": "network",
                "color": "#14a085"
            },
            {
                "text": "🔌 توصيل مباشر (USB/Ethernet)",
                "description": "فحص الكاميرات المتصلة مباشرة بالجهاز",
                "mode": "direct",
                "color": "#d63031"
            }
        ]
        
        for mode_info in scan_modes:
            # إطار الزر مع الوصف
            mode_frame = ctk.CTkFrame(self)
            mode_frame.pack(pady=10, padx=20, fill="x")
            
            # زر نوع الفحص
            mode_button = ArabicButton(
                mode_frame,
                text=mode_info["text"],
                font=ctk.CTkFont(size=14, weight="bold"),
                fg_color=mode_info["color"],
                height=50,
                command=lambda m=mode_info["mode"]: self.on_mode_select(m)
            )
            mode_button.pack(pady=10, padx=15, fill="x")
            
            # وصف نوع الفحص
            desc_label = ArabicLabel(
                mode_frame,
                text=mode_info["description"],
                font=ctk.CTkFont(size=11),
                text_color="gray70"
            )
            desc_label.pack(pady=(0, 10), padx=15)

class VisualScanFrame(ctk.CTkFrame):
    """إطار الفحص البصري"""
    
    def __init__(self, master, on_back: Callable, on_scan: Callable):
        super().__init__(master)
        self.on_back = on_back
        self.on_scan = on_scan
        self.camera_active = False
        self.video_capture = None
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # شريط العنوان
        header_frame = ctk.CTkFrame(self)
        header_frame.pack(fill="x", padx=10, pady=(10, 0))
        
        back_button = ArabicButton(
            header_frame,
            text="← رجوع",
            width=80,
            height=30,
            command=self.on_back
        )
        back_button.pack(side="left", padx=10, pady=10)
        
        title_label = ArabicLabel(
            header_frame,
            text="الفحص البصري للكاميرا",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(side="left", padx=20, pady=10)
        
        # منطقة عرض الكاميرا أو الصورة
        self.display_frame = ctk.CTkFrame(self)
        self.display_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # تسمية عرض الصورة
        self.image_label = ctk.CTkLabel(
            self.display_frame,
            text="انقر لتحميل صورة أو تشغيل الكاميرا",
            font=ctk.CTkFont(size=14),
            width=400,
            height=300
        )
        self.image_label.pack(expand=True, pady=20)
        
        # أزرار التحكم
        control_frame = ctk.CTkFrame(self)
        control_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        # زر تحميل صورة
        upload_button = ArabicButton(
            control_frame,
            text="📁 تحميل صورة",
            command=self.upload_image
        )
        upload_button.pack(side="left", padx=10, pady=10)
        
        # زر تشغيل الكاميرا
        self.camera_button = ArabicButton(
            control_frame,
            text="📷 تشغيل الكاميرا",
            command=self.toggle_camera
        )
        self.camera_button.pack(side="left", padx=10, pady=10)
        
        # زر التحليل
        self.analyze_button = ArabicButton(
            control_frame,
            text="🔍 تحليل الصورة",
            state="disabled",
            command=self.analyze_image,
            fg_color="#1f538d"
        )
        self.analyze_button.pack(side="right", padx=10, pady=10)
        
        # شريط التقدم
        self.progress_bar = ctk.CTkProgressBar(control_frame)
        self.progress_bar.pack(side="right", padx=20, pady=10)
        self.progress_bar.pack_forget()  # إخفاء في البداية
        
    def upload_image(self):
        """تحميل صورة من الملفات"""
        file_types = [
            ("صور", "*.jpg *.jpeg *.png *.bmp *.tiff"),
            ("جميع الملفات", "*.*")
        ]
        
        file_path = filedialog.askopenfilename(
            title="اختر صورة الكاميرا",
            filetypes=file_types
        )
        
        if file_path:
            self.load_image(file_path)
            
    def load_image(self, file_path: str):
        """تحميل وعرض الصورة"""
        try:
            # قراءة الصورة
            image = Image.open(file_path)
            
            # تغيير حجم الصورة للعرض
            display_size = (400, 300)
            image.thumbnail(display_size, Image.Resampling.LANCZOS)
            
            # تحويل للعرض في tkinter
            photo = ImageTk.PhotoImage(image)
            
            # عرض الصورة
            self.image_label.configure(image=photo, text="")
            self.image_label.image = photo  # حفظ مرجع للصورة
            
            # تفعيل زر التحليل
            self.analyze_button.configure(state="normal")
            
            # حفظ مسار الصورة
            self.current_image_path = file_path
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الصورة:\n{str(e)}")
            
    def toggle_camera(self):
        """تشغيل/إيقاف الكاميرا"""
        if not self.camera_active:
            self.start_camera()
        else:
            self.stop_camera()
            
    def start_camera(self):
        """تشغيل الكاميرا"""
        try:
            self.video_capture = cv2.VideoCapture(0)
            if not self.video_capture.isOpened():
                messagebox.showerror("خطأ", "فشل في الوصول للكاميرا")
                return
                
            self.camera_active = True
            self.camera_button.configure(text="⏹ إيقاف الكاميرا")
            self.update_camera_feed()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تشغيل الكاميرا:\n{str(e)}")
            
    def stop_camera(self):
        """إيقاف الكاميرا"""
        self.camera_active = False
        if self.video_capture:
            self.video_capture.release()
            self.video_capture = None
        self.camera_button.configure(text="📷 تشغيل الكاميرا")
        
    def update_camera_feed(self):
        """تحديث عرض الكاميرا"""
        if self.camera_active and self.video_capture:
            ret, frame = self.video_capture.read()
            if ret:
                # تحويل من BGR إلى RGB
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                
                # تغيير حجم الإطار
                height, width = frame_rgb.shape[:2]
                max_width, max_height = 400, 300
                
                if width > max_width or height > max_height:
                    scale = min(max_width/width, max_height/height)
                    new_width = int(width * scale)
                    new_height = int(height * scale)
                    frame_rgb = cv2.resize(frame_rgb, (new_width, new_height))
                
                # تحويل للعرض في tkinter
                image = Image.fromarray(frame_rgb)
                photo = ImageTk.PhotoImage(image)
                
                # عرض الإطار
                self.image_label.configure(image=photo, text="")
                self.image_label.image = photo
                
                # تفعيل زر التحليل
                self.analyze_button.configure(state="normal")
                
            # جدولة التحديث التالي
            self.after(30, self.update_camera_feed)
            
    def analyze_image(self):
        """تحليل الصورة المعروضة"""
        self.progress_bar.pack(side="right", padx=20, pady=10)
        self.progress_bar.set(0)
        self.analyze_button.configure(state="disabled", text="جاري التحليل...")
        
        # تشغيل التحليل في خيط منفصل
        threading.Thread(target=self._run_analysis, daemon=True).start()
        
    def _run_analysis(self):
        """تشغيل عملية التحليل"""
        try:
            # محاكاة عملية التحليل
            for i in range(101):
                self.progress_bar.set(i / 100)
                self.update_idletasks()
                threading.Event().wait(0.02)  # محاكاة المعالجة
                
            # إخفاء شريط التقدم
            self.progress_bar.pack_forget()
            self.analyze_button.configure(state="normal", text="🔍 تحليل الصورة")
            
            # عرض النتائج (محاكاة)
            result = {
                "model": "Hikvision DS-2CD2043G0-I",
                "confidence": 0.87,
                "brand": "Hikvision",
                "vulnerabilities": ["CVE-2017-7921"]
            }
            
            self.on_scan("visual", result)
            
        except Exception as e:
            self.progress_bar.pack_forget()
            self.analyze_button.configure(state="normal", text="🔍 تحليل الصورة")
            messagebox.showerror("خطأ", f"فشل في التحليل:\n{str(e)}")

class NetworkScanFrame(ctk.CTkFrame):
    """إطار فحص الشبكة"""
    
    def __init__(self, master, on_back: Callable, on_scan: Callable):
        super().__init__(master)
        self.on_back = on_back
        self.on_scan = on_scan
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # شريط العنوان
        header_frame = ctk.CTkFrame(self)
        header_frame.pack(fill="x", padx=10, pady=(10, 0))
        
        back_button = ArabicButton(
            header_frame,
            text="← رجوع",
            width=80,
            height=30,
            command=self.on_back
        )
        back_button.pack(side="left", padx=10, pady=10)
        
        title_label = ArabicLabel(
            header_frame,
            text="فحص كاميرات الشبكة",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(side="left", padx=20, pady=10)
        
        # إعدادات الفحص
        settings_frame = ctk.CTkFrame(self)
        settings_frame.pack(fill="x", padx=10, pady=10)
        
        # عنوان الإعدادات
        settings_title = ArabicLabel(
            settings_frame,
            text="إعدادات الفحص:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        settings_title.pack(anchor="w", padx=15, pady=(15, 5))
        
        # نطاق IP
        ip_frame = ctk.CTkFrame(settings_frame)
        ip_frame.pack(fill="x", padx=15, pady=5)
        
        ip_label = ArabicLabel(ip_frame, text="نطاق الشبكة:")
        ip_label.pack(side="left", padx=10, pady=10)
        
        self.ip_entry = ctk.CTkEntry(
            ip_frame,
            placeholder_text="***********/24",
            width=200
        )
        self.ip_entry.pack(side="left", padx=10, pady=10)
        self.ip_entry.insert(0, "***********/24")
        
        # منافذ الفحص
        ports_frame = ctk.CTkFrame(settings_frame)
        ports_frame.pack(fill="x", padx=15, pady=5)
        
        ports_label = ArabicLabel(ports_frame, text="المنافذ:")
        ports_label.pack(side="left", padx=10, pady=10)
        
        self.ports_entry = ctk.CTkEntry(
            ports_frame,
            placeholder_text="80,554,8080",
            width=200
        )
        self.ports_entry.pack(side="left", padx=10, pady=10)
        self.ports_entry.insert(0, "80,554,8080,37777")
        
        # خيارات إضافية
        options_frame = ctk.CTkFrame(settings_frame)
        options_frame.pack(fill="x", padx=15, pady=(5, 15))
        
        self.onvif_check = ctk.CTkCheckBox(
            options_frame,
            text="فحص ONVIF",
            font=ctk.CTkFont(size=12)
        )
        self.onvif_check.pack(side="left", padx=10, pady=10)
        self.onvif_check.select()
        
        self.http_check = ctk.CTkCheckBox(
            options_frame,
            text="فحص HTTP Banners",
            font=ctk.CTkFont(size=12)
        )
        self.http_check.pack(side="left", padx=20, pady=10)
        self.http_check.select()
        
        self.mac_check = ctk.CTkCheckBox(
            options_frame,
            text="فحص MAC Address",
            font=ctk.CTkFont(size=12)
        )
        self.mac_check.pack(side="left", padx=20, pady=10)
        self.mac_check.select()
        
        # منطقة النتائج
        results_frame = ctk.CTkFrame(self)
        results_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        results_title = ArabicLabel(
            results_frame,
            text="نتائج الفحص:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        results_title.pack(anchor="w", padx=15, pady=(15, 5))
        
        # جدول النتائج
        self.results_tree = ttk.Treeview(
            results_frame,
            columns=("IP", "Port", "Model", "Confidence"),
            show="headings",
            height=8
        )
        
        # تكوين الأعمدة
        self.results_tree.heading("IP", text="عنوان IP")
        self.results_tree.heading("Port", text="المنفذ")
        self.results_tree.heading("Model", text="النموذج")
        self.results_tree.heading("Confidence", text="الثقة")
        
        self.results_tree.column("IP", width=120)
        self.results_tree.column("Port", width=80)
        self.results_tree.column("Model", width=200)
        self.results_tree.column("Confidence", width=80)
        
        # شريط التمرير للجدول
        scrollbar = ttk.Scrollbar(results_frame, orient="vertical", command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=scrollbar.set)
        
        self.results_tree.pack(side="left", fill="both", expand=True, padx=(15, 0), pady=(0, 15))
        scrollbar.pack(side="right", fill="y", padx=(0, 15), pady=(0, 15))
        
        # أزرار التحكم
        control_frame = ctk.CTkFrame(self)
        control_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        # زر بدء الفحص
        self.scan_button = ArabicButton(
            control_frame,
            text="🔍 بدء فحص الشبكة",
            command=self.start_network_scan,
            fg_color="#14a085"
        )
        self.scan_button.pack(side="left", padx=10, pady=10)
        
        # شريط التقدم
        self.progress_bar = ctk.CTkProgressBar(control_frame)
        self.progress_bar.pack(side="left", padx=20, pady=10)
        self.progress_bar.pack_forget()
        
        # تسمية الحالة
        self.status_label = ArabicLabel(
            control_frame,
            text="جاهز للفحص",
            font=ctk.CTkFont(size=12)
        )
        self.status_label.pack(side="left", padx=20, pady=10)
        
        # زر إظهار التفاصيل
        self.details_button = ArabicButton(
            control_frame,
            text="📋 تفاصيل النتائج",
            command=self.show_details,
            state="disabled"
        )
        self.details_button.pack(side="right", padx=10, pady=10)
        
    def start_network_scan(self):
        """بدء فحص الشبكة"""
        # تنظيف النتائج السابقة
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
            
        self.progress_bar.pack(side="left", padx=20, pady=10)
        self.progress_bar.set(0)
        self.scan_button.configure(state="disabled", text="جاري الفحص...")
        self.status_label.configure(text="جاري فحص الشبكة...")
        
        # تشغيل الفحص في خيط منفصل
        threading.Thread(target=self._run_network_scan, daemon=True).start()
        
    def _run_network_scan(self):
        """تشغيل عملية فحص الشبكة"""
        try:
            # محاكاة فحص الشبكة
            ip_range = self.ip_entry.get()
            ports = self.ports_entry.get().split(',')
            
            # نتائج محاكاة
            sample_results = [
                {"ip": "*************", "port": 80, "model": "Hikvision DS-2CD2043G0", "confidence": 0.95},
                {"ip": "*************", "port": 554, "model": "Dahua IPC-HDBW4431R-ZS", "confidence": 0.89},
                {"ip": "*************", "port": 8080, "model": "Axis P3245-LV", "confidence": 0.78},
                {"ip": "*************", "port": 80, "model": "Bosch NBE-5503-AL", "confidence": 0.82},
            ]
            
            for i, result in enumerate(sample_results):
                # تحديث شريط التقدم
                progress = (i + 1) / len(sample_results)
                self.progress_bar.set(progress)
                
                # إضافة النتيجة للجدول
                self.results_tree.insert(
                    "",
                    "end",
                    values=(
                        result["ip"],
                        result["port"],
                        result["model"],
                        f"{result['confidence']:.2f}"
                    )
                )
                
                # تحديث حالة الفحص
                self.status_label.configure(text=f"تم العثور على {i+1} كاميرا")
                
                # انتظار قصير لمحاكاة الفحص
                threading.Event().wait(1)
                
            # انتهاء الفحص
            self.progress_bar.pack_forget()
            self.scan_button.configure(state="normal", text="🔍 بدء فحص الشبكة")
            self.status_label.configure(text=f"انتهى الفحص - تم العثور على {len(sample_results)} كاميرا")
            self.details_button.configure(state="normal")
            
        except Exception as e:
            self.progress_bar.pack_forget()
            self.scan_button.configure(state="normal", text="🔍 بدء فحص الشبكة")
            self.status_label.configure(text="فشل في الفحص")
            messagebox.showerror("خطأ", f"فشل في فحص الشبكة:\n{str(e)}")
            
    def show_details(self):
        """عرض تفاصيل العنصر المحدد"""
        selection = self.results_tree.selection()
        if selection:
            item = self.results_tree.item(selection[0])
            values = item['values']
            
            # إنشاء نافذة التفاصيل
            details_window = ctk.CTkToplevel(self)
            details_window.title("تفاصيل الكاميرا")
            details_window.geometry("400x500")
            
            # محتوى التفاصيل
            details_text = f"""
تفاصيل الكاميرا المكتشفة:

عنوان IP: {values[0]}
المنفذ: {values[1]}
النموذج: {values[2]}
درجة الثقة: {values[3]}

معلومات إضافية:
- نوع الاتصال: شبكي
- البروتوكول: ONVIF/HTTP
- حالة الأمان: يتطلب فحص
- التحديث الأخير: {datetime.now().strftime('%Y/%m/%d %H:%M')}

الثغرات المحتملة:
- كلمة مرور افتراضية
- إصدار قديم من البرنامج الثابت
- تشفير ضعيف
            """
            
            text_widget = ctk.CTkTextbox(details_window)
            text_widget.pack(fill="both", expand=True, padx=20, pady=20)
            text_widget.insert("0.0", details_text)
            text_widget.configure(state="disabled")

class ResultsFrame(ctk.CTkFrame):
    """إطار عرض النتائج"""
    
    def __init__(self, master, on_back: Callable, on_report: Callable):
        super().__init__(master)
        self.on_back = on_back
        self.on_report = on_report
        self.scan_result = None
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # شريط العنوان
        header_frame = ctk.CTkFrame(self)
        header_frame.pack(fill="x", padx=10, pady=(10, 0))
        
        back_button = ArabicButton(
            header_frame,
            text="← رجوع",
            width=80,
            height=30,
            command=self.on_back
        )
        back_button.pack(side="left", padx=10, pady=10)
        
        self.title_label = ArabicLabel(
            header_frame,
            text="نتائج الفحص",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        self.title_label.pack(side="left", padx=20, pady=10)
        
        # منطقة النتائج الرئيسية
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # معلومات الكاميرا المكتشفة
        self.camera_info_frame = ctk.CTkFrame(main_frame)
        self.camera_info_frame.pack(fill="x", padx=15, pady=15)
        
        # أزرار التحكم
        control_frame = ctk.CTkFrame(self)
        control_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        # زر حفظ النتائج
        save_button = ArabicButton(
            control_frame,
            text="💾 حفظ النتائج",
            command=self.save_results
        )
        save_button.pack(side="left", padx=10, pady=10)
        
        # زر إنشاء تقرير
        report_button = ArabicButton(
            control_frame,
            text="📄 إنشاء تقرير PDF",
            command=self.generate_report,
            fg_color="#d63031"
        )
        report_button.pack(side="left", padx=10, pady=10)
        
        # زر مشاركة
        share_button = ArabicButton(
            control_frame,
            text="📤 مشاركة",
            command=self.share_results
        )
        share_button.pack(side="right", padx=10, pady=10)
        
    def set_results(self, scan_type: str, results: Dict[str, Any]):
        """تعيين نتائج الفحص للعرض"""
        self.scan_result = {"type": scan_type, "data": results}
        self.display_results()
        
    def display_results(self):
        """عرض النتائج"""
        if not self.scan_result:
            return
            
        # تنظيف الإطار
        for widget in self.camera_info_frame.winfo_children():
            widget.destroy()
            
        results = self.scan_result["data"]
        scan_type = self.scan_result["type"]
        
        # عنوان النتيجة
        result_title = ArabicLabel(
            self.camera_info_frame,
            text=f"نتيجة الفحص {self._get_scan_type_name(scan_type)}",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        result_title.pack(pady=(15, 10))
        
        # معلومات الكاميرا
        if isinstance(results, dict):
            self._display_single_camera(results)
        elif isinstance(results, list):
            self._display_multiple_cameras(results)
            
    def _get_scan_type_name(self, scan_type: str) -> str:
        """الحصول على اسم نوع الفحص بالعربية"""
        names = {
            "visual": "البصري",
            "network": "الشبكي",
            "direct": "المباشر"
        }
        return names.get(scan_type, scan_type)
        
    def _display_single_camera(self, result: Dict[str, Any]):
        """عرض نتيجة كاميرا واحدة"""
        
        # إطار معلومات الكاميرا
        info_frame = ctk.CTkFrame(self.camera_info_frame)
        info_frame.pack(fill="x", padx=15, pady=10)
        
        # النموذج المكتشف
        model_label = ArabicLabel(
            info_frame,
            text=f"النموذج المكتشف: {result.get('model', 'غير محدد')}",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        model_label.pack(anchor="w", padx=15, pady=(15, 5))
        
        # درجة الثقة
        confidence = result.get('confidence', 0)
        confidence_label = ArabicLabel(
            info_frame,
            text=f"درجة الثقة: {confidence:.2%}",
            font=ctk.CTkFont(size=12)
        )
        confidence_label.pack(anchor="w", padx=15, pady=2)
        
        # شريط الثقة البصري
        confidence_bar = ctk.CTkProgressBar(info_frame)
        confidence_bar.pack(fill="x", padx=15, pady=5)
        confidence_bar.set(confidence)
        
        # معلومات إضافية
        if 'brand' in result:
            brand_label = ArabicLabel(
                info_frame,
                text=f"الشركة المصنعة: {result['brand']}",
                font=ctk.CTkFont(size=12)
            )
            brand_label.pack(anchor="w", padx=15, pady=2)
            
        # الثغرات الأمنية
        if 'vulnerabilities' in result and result['vulnerabilities']:
            vuln_frame = ctk.CTkFrame(info_frame, fg_color=("gray80", "gray20"))
            vuln_frame.pack(fill="x", padx=15, pady=(10, 15))
            
            vuln_title = ArabicLabel(
                vuln_frame,
                text="⚠️ تحذيرات أمنية:",
                font=ctk.CTkFont(size=12, weight="bold"),
                text_color=("red", "orange")
            )
            vuln_title.pack(anchor="w", padx=10, pady=(10, 5))
            
            for vuln in result['vulnerabilities']:
                vuln_label = ArabicLabel(
                    vuln_frame,
                    text=f"• {vuln}",
                    font=ctk.CTkFont(size=11),
                    text_color=("red", "orange")
                )
                vuln_label.pack(anchor="w", padx=20, pady=1)
                
            # توصيات أمنية
            recommendations = [
                "قم بتغيير كلمة المرور الافتراضية",
                "تحقق من توفر تحديثات للبرنامج الثابت",
                "فعّل التشفير إذا كان متوفراً",
                "اعزل الكاميرا عن الإنترنت إذا أمكن"
            ]
            
            rec_label = ArabicLabel(
                vuln_frame,
                text="التوصيات:",
                font=ctk.CTkFont(size=12, weight="bold")
            )
            rec_label.pack(anchor="w", padx=10, pady=(10, 5))
            
            for rec in recommendations:
                rec_item = ArabicLabel(
                    vuln_frame,
                    text=f"✓ {rec}",
                    font=ctk.CTkFont(size=11)
                )
                rec_item.pack(anchor="w", padx=20, pady=1)
                
            vuln_frame.pack_configure(pady=(10, 15))
            
    def _display_multiple_cameras(self, results: list):
        """عرض نتائج متعددة للكاميرات"""
        
        summary_label = ArabicLabel(
            self.camera_info_frame,
            text=f"تم العثور على {len(results)} كاميرا",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        summary_label.pack(pady=10)
        
        # عرض الكاميرات الأولى فقط لتوفير المساحة
        for i, result in enumerate(results[:3]):
            camera_frame = ctk.CTkFrame(self.camera_info_frame)
            camera_frame.pack(fill="x", padx=15, pady=5)
            
            camera_title = ArabicLabel(
                camera_frame,
                text=f"الكاميرا {i+1}: {result.get('model', 'غير محدد')}",
                font=ctk.CTkFont(size=12, weight="bold")
            )
            camera_title.pack(anchor="w", padx=10, pady=5)
            
        if len(results) > 3:
            more_label = ArabicLabel(
                self.camera_info_frame,
                text=f"... و {len(results) - 3} كاميرا أخرى",
                font=ctk.CTkFont(size=11),
                text_color="gray70"
            )
            more_label.pack(pady=5)
            
    def save_results(self):
        """حفظ النتائج في ملف"""
        if not self.scan_result:
            messagebox.showwarning("تحذير", "لا توجد نتائج للحفظ")
            return
            
        file_path = filedialog.asksaveasfilename(
            title="حفظ النتائج",
            defaultextension=".json",
            filetypes=[
                ("ملفات JSON", "*.json"),
                ("جميع الملفات", "*.*")
            ]
        )
        
        if file_path:
            try:
                import json
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.scan_result, f, ensure_ascii=False, indent=2)
                messagebox.showinfo("نجح", "تم حفظ النتائج بنجاح")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ النتائج:\n{str(e)}")
                
    def generate_report(self):
        """إنشاء تقرير PDF"""
        if not self.scan_result:
            messagebox.showwarning("تحذير", "لا توجد نتائج لإنشاء تقرير")
            return
            
        self.on_report(self.scan_result)
        
    def share_results(self):
        """مشاركة النتائج"""
        if not self.scan_result:
            messagebox.showwarning("تحذير", "لا توجد نتائج للمشاركة")
            return
            
        # إنشاء نص موجز للمشاركة
        results = self.scan_result["data"]
        scan_type = self._get_scan_type_name(self.scan_result["type"])
        
        if isinstance(results, dict):
            share_text = f"""
نتيجة فحص الكاميرا - كاميرا آيـدي

نوع الفحص: {scan_type}
النموذج المكتشف: {results.get('model', 'غير محدد')}
درجة الثقة: {results.get('confidence', 0):.2%}
التاريخ: {datetime.now().strftime('%Y/%m/%d %H:%M')}

تم إنشاء هذا التقرير باستخدام تطبيق كاميرا آيـدي
            """
        else:
            share_text = f"""
نتيجة فحص الكاميرات - كاميرا آيـدي

نوع الفحص: {scan_type}
عدد الكاميرات المكتشفة: {len(results)}
التاريخ: {datetime.now().strftime('%Y/%m/%d %H:%M')}

تم إنشاء هذا التقرير باستخدام تطبيق كاميرا آيـدي
            """
            
        # نسخ النص للحافظة
        self.clipboard_clear()
        self.clipboard_append(share_text)
        
        messagebox.showinfo("نجح", "تم نسخ ملخص النتائج إلى الحافظة")

class CameraIDApp(ctk.CTk):
    """التطبيق الرئيسي لكاميرا آيـدي"""
    
    def __init__(self):
        super().__init__()
        
        # تحميل التكوين
        self.config = Config()
        self.logger = setup_logger()
        
        # إعداد النافذة الرئيسية
        self.setup_window()
        self.setup_ui()
        
        # الإطارات
        self.frames = {}
        self.current_frame = None
        
        # إنشاء الإطارات
        self.create_frames()
        
        # عرض الإطار الافتراضي
        self.show_frame("scan_mode")
        
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.title(self.config.app.app_name)
        self.geometry(f"{self.config.gui.window_width}x{self.config.gui.window_height}")
        self.minsize(self.config.gui.min_width, self.config.gui.min_height)
        
        # تعيين أيقونة التطبيق (إذا توفرت)
        # self.iconbitmap("icon.ico")
        
        # إعداد الشبكة
        self.grid_rowconfigure(0, weight=1)
        self.grid_columnconfigure(0, weight=1)
        
    def setup_ui(self):
        """إعداد واجهة المستخدم الأساسية"""
        
        # شريط القائمة العلوي
        self.menubar_frame = ctk.CTkFrame(self, height=50)
        self.menubar_frame.pack(fill="x", padx=5, pady=(5, 0))
        
        # شعار التطبيق
        logo_label = ArabicLabel(
            self.menubar_frame,
            text="🎥 كاميرا آيـدي",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        logo_label.pack(side="left", padx=20, pady=10)
        
        # معلومات الإصدار
        version_label = ArabicLabel(
            self.menubar_frame,
            text=f"الإصدار {self.config.app.version}",
            font=ctk.CTkFont(size=10),
            text_color="gray70"
        )
        version_label.pack(side="left", padx=10, pady=10)
        
        # أزرار التنقل
        nav_frame = ctk.CTkFrame(self.menubar_frame)
        nav_frame.pack(side="right", padx=20, pady=5)
        
        # زر الإعدادات
        settings_button = ArabicButton(
            nav_frame,
            text="⚙️ إعدادات",
            width=80,
            height=30,
            command=self.show_settings
        )
        settings_button.pack(side="right", padx=5)
        
        # زر السجل
        history_button = ArabicButton(
            nav_frame,
            text="📊 السجل",
            width=80,
            height=30,
            command=self.show_history
        )
        history_button.pack(side="right", padx=5)
        
        # الإطار الرئيسي للمحتوى
        self.main_frame = ctk.CTkFrame(self)
        self.main_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
    def create_frames(self):
        """إنشاء إطارات التطبيق المختلفة"""
        
        # إطار اختيار نوع الفحص
        self.frames["scan_mode"] = ScanModeFrame(
            self.main_frame,
            on_mode_select=self.on_scan_mode_select
        )
        
        # إطار الفحص البصري
        self.frames["visual_scan"] = VisualScanFrame(
            self.main_frame,
            on_back=lambda: self.show_frame("scan_mode"),
            on_scan=self.on_scan_complete
        )
        
        # إطار فحص الشبكة
        self.frames["network_scan"] = NetworkScanFrame(
            self.main_frame,
            on_back=lambda: self.show_frame("scan_mode"),
            on_scan=self.on_scan_complete
        )
        
        # إطار النتائج
        self.frames["results"] = ResultsFrame(
            self.main_frame,
            on_back=self.on_results_back,
            on_report=self.generate_pdf_report
        )
        
    def show_frame(self, frame_name: str):
        """عرض إطار معين"""
        # إخفاء الإطار الحالي
        if self.current_frame:
            self.current_frame.pack_forget()
            
        # عرض الإطار الجديد
        if frame_name in self.frames:
            self.frames[frame_name].pack(fill="both", expand=True)
            self.current_frame = self.frames[frame_name]
            
    def on_scan_mode_select(self, mode: str):
        """عند اختيار نوع الفحص"""
        self.logger.info(f"تم اختيار نوع الفحص: {mode}")
        
        if mode == "visual":
            self.show_frame("visual_scan")
        elif mode == "network":
            self.show_frame("network_scan")
        elif mode == "direct":
            messagebox.showinfo("قريباً", "فحص الاتصال المباشر سيكون متاحاً قريباً")
            
    def on_scan_complete(self, scan_type: str, results: Dict[str, Any]):
        """عند انتهاء الفحص"""
        self.logger.info(f"انتهى الفحص من النوع: {scan_type}")
        
        # عرض النتائج
        self.frames["results"].set_results(scan_type, results)
        self.show_frame("results")
        
    def on_results_back(self):
        """الرجوع من صفحة النتائج"""
        self.show_frame("scan_mode")
        
    def generate_pdf_report(self, scan_result: Dict[str, Any]):
        """إنشاء تقرير PDF"""
        try:
            # هنا يمكن إضافة منطق إنشاء تقرير PDF فعلي
            messagebox.showinfo("نجح", "سيتم إضافة ميزة إنشاء تقرير PDF قريباً")
            
        except Exception as e:
            self.logger.error(f"فشل في إنشاء التقرير: {e}")
            messagebox.showerror("خطأ", f"فشل في إنشاء التقرير:\n{str(e)}")
            
    def show_settings(self):
        """عرض نافذة الإعدادات"""
        settings_window = ctk.CTkToplevel(self)
        settings_window.title("الإعدادات")
        settings_window.geometry("500x400")
        
        # محتوى الإعدادات
        settings_label = ArabicLabel(
            settings_window,
            text="إعدادات التطبيق",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        settings_label.pack(pady=20)
        
        # إعدادات المظهر
        appearance_frame = ctk.CTkFrame(settings_window)
        appearance_frame.pack(fill="x", padx=20, pady=10)
        
        appearance_label = ArabicLabel(
            appearance_frame,
            text="المظهر:",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        appearance_label.pack(anchor="w", padx=15, pady=(15, 5))
        
        appearance_menu = ctk.CTkOptionMenu(
            appearance_frame,
            values=["فاتح", "داكن", "النظام"],
            command=self.change_appearance
        )
        appearance_menu.pack(anchor="w", padx=15, pady=(0, 15))
        appearance_menu.set("داكن")
        
    def change_appearance(self, new_appearance: str):
        """تغيير مظهر التطبيق"""
        appearance_map = {
            "فاتح": "light",
            "داكن": "dark",
            "النظام": "system"
        }
        
        if new_appearance in appearance_map:
            ctk.set_appearance_mode(appearance_map[new_appearance])
            
    def show_history(self):
        """عرض سجل الفحوصات"""
        history_window = ctk.CTkToplevel(self)
        history_window.title("سجل الفحوصات")
        history_window.geometry("600x500")
        
        # محتوى السجل
        history_label = ArabicLabel(
            history_window,
            text="سجل الفحوصات السابقة",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        history_label.pack(pady=20)
