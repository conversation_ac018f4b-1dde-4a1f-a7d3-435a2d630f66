
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النافذة الرئيسية - Main Window
واجهة المستخدم الرئيسية لتطبيق كاميرا آيـدي
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import customtkinter as ctk
from PIL import Image, ImageTk
import cv2
import threading
import os
from datetime import datetime
from typing import Optional, Dict, Any, Callable

from ..core.config import Config
from ..utils.logger import setup_logger

class ArabicLabel(ctk.CTkLabel):
    """تسمية نصية مع دعم اللغة العربية"""
    
    def __init__(self, master, text="", **kwargs):
        # تعيين الخط والاتجاه للنص العربي
        if 'font' not in kwargs:
            kwargs['font'] = ctk.CTkFont(family="Arial", size=12)
        super().__init__(master, text=text, **kwargs)

class ArabicButton(ctk.CTkButton):
    """زر مع دعم اللغة العربية"""
    
    def __init__(self, master, text="", **kwargs):
        if 'font' not in kwargs:
            kwargs['font'] = ctk.CTkFont(family="Arial", size=12, weight="bold")
        super().__init__(master, text=text, **kwargs)

class ScanModeFrame(ctk.CTkFrame):
    """إطار اختيار نوع الفحص"""
    
    def __init__(self, master, on_mode_select: Callable):
        super().__init__(master)
        self.on_mode_select = on_mode_select
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # عنوان الإطار
        title_label = ArabicLabel(
            self, 
            text="اختر طريقة الفحص",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=20)
        
        # أزرار أنواع الفحص
        scan_modes = [
            {
                "text": "🔍 مسح بصري (Computer Vision)",
                "description": "التعرف على الكاميرا من الصورة باستخدام الذكاء الاصطناعي",
                "mode": "visual",
                "color": "#1f538d"
            },
            {
                "text": "🌐 فحص شبكي (ONVIF/HTTP)",
                "description": "فحص الكاميرات عبر الشبكة واستخراج المعلومات",
                "mode": "network",
                "color": "#14a085"
            },
            {
                "text": "🔌 توصيل مباشر (USB/Ethernet)",
                "description": "فحص الكاميرات المتصلة مباشرة بالجهاز",
                "mode": "direct",
                "color": "#d63031"
            }
        ]
        
        for mode_info in scan_modes:
            # إطار الزر مع الوصف
            mode_frame = ctk.CTkFrame(self)
            mode_frame.pack(pady=10, padx=20, fill="x")
            
            # زر نوع الفحص
            mode_button = ArabicButton(
                mode_frame,
                text=mode_info["text"],
                font=ctk.CTkFont(size=14, weight="bold"),
                fg_color=mode_info["color"],
                height=50,
                command=lambda m=mode_info["mode"]: self.on_mode_select(m)
            )
            mode_button.pack(pady=10, padx=15, fill="x")
            
            # وصف نوع الفحص
            desc_label = ArabicLabel(
                mode_frame,
                text=mode_info["description"],
                font=ctk.CTkFont(size=11),
                text_color="gray70"
            )
            desc_label.pack(pady=(0, 10), padx=15)

class VisualScanFrame(ctk.CTkFrame):
    """إطار الفحص البصري"""
    
    def __init__(self, master, on_back: Callable, on_scan: Callable):
        super().__init__(master)
        self.on_back = on_back
        self.on_scan = on_scan
        self.camera_active = False
        self.video_capture = None
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # شريط العنوان
        header_frame = ctk.CTkFrame(self)
        header_frame.pack(fill="x", padx=10, pady=(10, 0))
        
        back_button = ArabicButton(
            header_frame,
            text="← رجوع",
            width=80,
            height=30,
            command=self.on_back
        )
        back_button.pack(side="left", padx=10, pady=10)
        
        title_label = ArabicLabel(
            header_frame,
            text="الفحص البصري للكاميرا",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(side="left", padx=20, pady=10)
        
        # منطقة عرض الكاميرا أو الصورة
        self.display_frame = ctk.CTkFrame(self)
        self.display_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # تسمية عرض الصورة
        self.image_label = ctk.CTkLabel(
            self.display_frame,
            text="انقر لتحميل صورة أو تشغيل الكاميرا",
            font=ctk.CTkFont(size=14),
            width=400,
            height=300
        )
        self.image_label.pack(expand=True, pady=20)
        
        # أزرار التحكم
        control_frame = ctk.CTkFrame(self)
        control_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        # زر تحميل صورة
        upload_button = ArabicButton(
            control_frame,
            text="📁 تحميل صورة",
            command=self.upload_image
        )
        upload_button.pack(side="left", padx=10, pady=10)
        
        # زر تشغيل الكاميرا
        self.camera_button = ArabicButton(
            control_frame,
            text="📷 تشغيل الكاميرا",
            command=self.toggle_camera
        )
        self.camera_button.pack(side="left", padx=10, pady=10)
        
        # زر التحليل
        self.analyze_button = ArabicButton(
            control_frame,
            text="🔍 تحليل الصورة",
            state="disabled",
            command=self.analyze_image,
            fg_color="#1f538d"
        )
        self.analyze_button.pack(side="right", padx=10, pady=10)
        
        # شريط التقدم
        self.progress_bar = ctk.CTkProgressBar(control_frame)
        self.progress_bar.pack(side="right", padx=20, pady=10)
        self.progress_bar.pack_forget()  # إخفاء في البداية
        
    def upload_image(self):
        """تحميل صورة من الملفات"""
        file_types = [
            ("صور", "*.jpg *.jpeg *.png *.bmp *.tiff"),
            ("جميع الملفات", "*.*")
        ]
        
        file_path = filedialog.askopenfilename(
            title="اختر صورة الكاميرا",
            filetypes=file_types
        )
        
        if file_path:
            self.load_image(file_path)
            
    def load_image(self, file_path: str):
        """تحميل وعرض الصورة"""
        try:
            # قراءة الصورة
            image = Image.open(file_path)
            
            # تغيير حجم الصورة للعرض
            display_size = (400, 300)
            image.thumbnail(display_size, Image.Resampling.LANCZOS)
            
            # تحويل للعرض في tkinter
            photo = ImageTk.PhotoImage(image)
            
            # عرض الصورة
            self.image_label.configure(image=photo, text="")
            self.image_label.image = photo  # حفظ مرجع للصورة
            
            # تفعيل زر التحليل
            self.analyze_button.configure(state="normal")
            
            # حفظ مسار الصورة
            self.current_image_path = file_path
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الصورة:\n{str(e)}")
            
    def toggle_camera(self):
        """تشغيل/إيقاف الكاميرا"""
        if not self.camera_active:
            self.start_camera()
        else:
            self.stop_camera()
            
    def start_camera(self):
        """تشغيل الكاميرا"""
        try:
            self.video_capture = cv2.VideoCapture(0)
            if not self.video_capture.isOpened():
                messagebox.showerror("خطأ", "فشل في الوصول للكاميرا")
                return
                
            self.camera_active = True
            self.camera_button.configure(text="⏹ إيقاف الكاميرا")
            self.update_camera_feed()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تشغيل الكاميرا:\n{str(e)}")
            
    def stop_camera(self):
        """إيقاف الكاميرا"""
        self.camera_active = False
        if self.video_capture:
            self.video_capture.release()
            self.video_capture = None
        self.camera_button.configure(text="📷 تشغيل الكاميرا")
        
    def update_camera_feed(self):
        """تحديث عرض الكاميرا"""
        if self.camera_active and self.video_capture:
            ret, frame = self.video_capture.read()
            if ret:
                # تحويل من BGR إلى RGB
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                
                # تغيير حجم الإطار
                height, width = frame_rgb.shape[:2]
                max_width, max_height = 400, 300
                
                if width > max_width or height > max_height:
                    scale = min(max_width/width, max_height/height)
                    new_width = int(width * scale)
                    new_height = int(height * scale)
                    frame_rgb = cv2.resize(frame_rgb, (new_width, new_height))
                
                # تحويل للعرض في tkinter
                image = Image.fromarray(frame_rgb)
                photo = ImageTk.PhotoImage(image)
                
                # عرض الإطار
                self.image_label.configure(image=photo, text="")
                self.image_label.image = photo
                
                # تفعيل زر التحليل
                self.analyze_button.configure(state="normal")
                
            # جدولة التحديث التالي
            self.after(30, self.update_camera_feed)
            
    def analyze_image(self):
        """تحليل الصورة المعروضة"""
        self.progress_bar.pack(side="right", padx=20, pady=10)
        self.progress_bar.set(0)
        self.analyze_button.configure(state="disabled", text="جاري التحليل...")
        
        # تشغيل التحليل في خيط منفصل
        threading.Thread(target=self._run_analysis, daemon=True).start()
        
    def _run_analysis(self):
        """تشغيل عملية التحليل"""
        try:
            # تحديث شريط التقدم
            self.progress_bar.set(0.1)
            self.update_idletasks()

            # تحليل الصورة
            result = self._analyze_image()

            # إخفاء شريط التقدم
            self.progress_bar.pack_forget()
            self.analyze_button.configure(state="normal", text="🔍 تحليل الصورة")

            self.on_scan("visual", result)

        except Exception as e:
            self.progress_bar.pack_forget()
            self.analyze_button.configure(state="normal", text="🔍 تحليل الصورة")
            messagebox.showerror("خطأ", f"فشل في التحليل:\n{str(e)}")

    def _analyze_image(self):
        """تحليل الصورة وتحديد نوع الكاميرا"""
        import json
        import os

        # تحميل قاعدة بيانات الكاميرات
        cameras_db_path = os.path.join("data", "cameras.json")
        vulnerabilities_db_path = os.path.join("data", "vulnerabilities.json")

        cameras_data = {}
        vulnerabilities_data = {}

        try:
            with open(cameras_db_path, 'r', encoding='utf-8') as f:
                cameras_data = json.load(f)
        except:
            pass

        try:
            with open(vulnerabilities_db_path, 'r', encoding='utf-8') as f:
                vulnerabilities_data = json.load(f)
        except:
            pass

        # تحليل بسيط للصورة (يمكن تحسينه باستخدام ML)
        result = self._simple_image_analysis()

        # البحث عن تطابق في قاعدة البيانات
        best_match = None
        best_confidence = 0.0

        if "cameras" in cameras_data:
            for brand_data in cameras_data["cameras"]:
                brand = brand_data["brand"]
                for model_data in brand_data["models"]:
                    model = model_data["model"]

                    # حساب درجة التطابق البسيطة
                    confidence = self._calculate_match_confidence(result, model_data)

                    if confidence > best_confidence:
                        best_confidence = confidence
                        best_match = {
                            "brand": brand,
                            "model": model,
                            "confidence": confidence,
                            "features": model_data.get("features", []),
                            "vulnerabilities": model_data.get("vulnerabilities", [])
                        }

        # إذا لم يتم العثور على تطابق جيد، إرجاع نتيجة عامة
        if best_match is None or best_confidence < 0.3:
            best_match = {
                "brand": "غير محدد",
                "model": "كاميرا IP عامة",
                "confidence": 0.5,
                "features": ["كاميرا مراقبة"],
                "vulnerabilities": ["Default credentials", "Weak Encryption"]
            }

        return best_match

    def _simple_image_analysis(self):
        """تحليل بسيط للصورة"""
        # هذا تحليل بسيط - يمكن استبداله بنموذج ML حقيقي
        result = {
            "has_camera_features": False,
            "resolution": "unknown",
            "color_space": "unknown",
            "aspect_ratio": 1.0
        }

        if hasattr(self, 'current_image_path') and self.current_image_path:
            try:
                image = Image.open(self.current_image_path)
                width, height = image.size
                result["resolution"] = f"{width}x{height}"
                result["aspect_ratio"] = width / height if height > 0 else 1.0
                result["color_space"] = image.mode

                # كشف ميزات بسيطة للكاميرا
                # هذا مجرد مثال - يمكن تحسينه
                result["has_camera_features"] = self._detect_camera_features(image)

            except Exception as e:
                print(f"خطأ في تحليل الصورة: {e}")

        return result

    def _detect_camera_features(self, image):
        """كشف ميزات الكاميرا في الصورة"""
        # تحليل بسيط للألوان والتباين
        # هذا placeholder - يمكن استبداله بتحليل أكثر تعقيداً
        try:
            # تحويل إلى grayscale وتحليل التباين
            grayscale = image.convert('L')
            pixels = list(grayscale.getdata())
            avg_brightness = sum(pixels) / len(pixels)
            contrast = max(pixels) - min(pixels)

            # كشف أساسي للكاميرات (يمكن تحسينه)
            return contrast > 50 and avg_brightness > 30
        except:
            return False

    def _calculate_match_confidence(self, image_analysis, model_data):
        """حساب درجة التطابق بين تحليل الصورة وبيانات النموذج"""
        confidence = 0.0

        # تطابق الميزات
        if image_analysis.get("has_camera_features"):
            confidence += 0.3

        # تطابق الدقة (إذا كانت متوفرة)
        model_features = model_data.get("features", [])
        if any("MP" in str(feature) for feature in model_features):
            confidence += 0.2

        # تطابق عام
        confidence += 0.5  # قاعدة أساسية

        return min(confidence, 1.0)

class NetworkScanFrame(ctk.CTkFrame):
    """إطار فحص الشبكة"""
    
    def __init__(self, master, on_back: Callable, on_scan: Callable):
        super().__init__(master)
        self.on_back = on_back
        self.on_scan = on_scan
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # شريط العنوان
        header_frame = ctk.CTkFrame(self)
        header_frame.pack(fill="x", padx=10, pady=(10, 0))
        
        back_button = ArabicButton(
            header_frame,
            text="← رجوع",
            width=80,
            height=30,
            command=self.on_back
        )
        back_button.pack(side="left", padx=10, pady=10)
        
        title_label = ArabicLabel(
            header_frame,
            text="فحص كاميرات الشبكة",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(side="left", padx=20, pady=10)
        
        # إعدادات الفحص
        settings_frame = ctk.CTkFrame(self)
        settings_frame.pack(fill="x", padx=10, pady=10)
        
        # عنوان الإعدادات
        settings_title = ArabicLabel(
            settings_frame,
            text="إعدادات الفحص:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        settings_title.pack(anchor="w", padx=15, pady=(15, 5))
        
        # نطاق IP
        ip_frame = ctk.CTkFrame(settings_frame)
        ip_frame.pack(fill="x", padx=15, pady=5)
        
        ip_label = ArabicLabel(ip_frame, text="نطاق الشبكة:")
        ip_label.pack(side="left", padx=10, pady=10)
        
        self.ip_entry = ctk.CTkEntry(
            ip_frame,
            placeholder_text="***********/24",
            width=200
        )
        self.ip_entry.pack(side="left", padx=10, pady=10)
        self.ip_entry.insert(0, "***********/24")
        
        # منافذ الفحص
        ports_frame = ctk.CTkFrame(settings_frame)
        ports_frame.pack(fill="x", padx=15, pady=5)
        
        ports_label = ArabicLabel(ports_frame, text="المنافذ:")
        ports_label.pack(side="left", padx=10, pady=10)
        
        self.ports_entry = ctk.CTkEntry(
            ports_frame,
            placeholder_text="80,554,8080",
            width=200
        )
        self.ports_entry.pack(side="left", padx=10, pady=10)
        self.ports_entry.insert(0, "80,554,8080,37777")
        
        # خيارات إضافية
        options_frame = ctk.CTkFrame(settings_frame)
        options_frame.pack(fill="x", padx=15, pady=(5, 15))
        
        self.onvif_check = ctk.CTkCheckBox(
            options_frame,
            text="فحص ONVIF",
            font=ctk.CTkFont(size=12)
        )
        self.onvif_check.pack(side="left", padx=10, pady=10)
        self.onvif_check.select()
        
        self.http_check = ctk.CTkCheckBox(
            options_frame,
            text="فحص HTTP Banners",
            font=ctk.CTkFont(size=12)
        )
        self.http_check.pack(side="left", padx=20, pady=10)
        self.http_check.select()
        
        self.mac_check = ctk.CTkCheckBox(
            options_frame,
            text="فحص MAC Address",
            font=ctk.CTkFont(size=12)
        )
        self.mac_check.pack(side="left", padx=20, pady=10)
        self.mac_check.select()
        
        # منطقة النتائج
        results_frame = ctk.CTkFrame(self)
        results_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        results_title = ArabicLabel(
            results_frame,
            text="نتائج الفحص:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        results_title.pack(anchor="w", padx=15, pady=(15, 5))
        
        # جدول النتائج
        self.results_tree = ttk.Treeview(
            results_frame,
            columns=("IP", "Port", "Model", "Confidence"),
            show="headings",
            height=8
        )
        
        # تكوين الأعمدة
        self.results_tree.heading("IP", text="عنوان IP")
        self.results_tree.heading("Port", text="المنفذ")
        self.results_tree.heading("Model", text="النموذج")
        self.results_tree.heading("Confidence", text="الثقة")
        
        self.results_tree.column("IP", width=120)
        self.results_tree.column("Port", width=80)
        self.results_tree.column("Model", width=200)
        self.results_tree.column("Confidence", width=80)
        
        # شريط التمرير للجدول
        scrollbar = ttk.Scrollbar(results_frame, orient="vertical", command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=scrollbar.set)
        
        self.results_tree.pack(side="left", fill="both", expand=True, padx=(15, 0), pady=(0, 15))
        scrollbar.pack(side="right", fill="y", padx=(0, 15), pady=(0, 15))
        
        # أزرار التحكم
        control_frame = ctk.CTkFrame(self)
        control_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        # زر بدء الفحص
        self.scan_button = ArabicButton(
            control_frame,
            text="🔍 بدء فحص الشبكة",
            command=self.start_network_scan,
            fg_color="#14a085"
        )
        self.scan_button.pack(side="left", padx=10, pady=10)
        
        # شريط التقدم
        self.progress_bar = ctk.CTkProgressBar(control_frame)
        self.progress_bar.pack(side="left", padx=20, pady=10)
        self.progress_bar.pack_forget()
        
        # تسمية الحالة
        self.status_label = ArabicLabel(
            control_frame,
            text="جاهز للفحص",
            font=ctk.CTkFont(size=12)
        )
        self.status_label.pack(side="left", padx=20, pady=10)
        
        # زر إظهار التفاصيل
        self.details_button = ArabicButton(
            control_frame,
            text="📋 تفاصيل النتائج",
            command=self.show_details,
            state="disabled"
        )
        self.details_button.pack(side="right", padx=10, pady=10)
        
    def start_network_scan(self):
        """بدء فحص الشبكة"""
        # تنظيف النتائج السابقة
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
            
        self.progress_bar.pack(side="left", padx=20, pady=10)
        self.progress_bar.set(0)
        self.scan_button.configure(state="disabled", text="جاري الفحص...")
        self.status_label.configure(text="جاري فحص الشبكة...")
        
        # تشغيل الفحص في خيط منفصل
        threading.Thread(target=self._run_network_scan, daemon=True).start()
        
    def _run_network_scan(self):
        """تشغيل عملية فحص الشبكة"""
        try:
            import socket
            import ipaddress
            import requests
            import json
            import os

            # الحصول على الإعدادات
            ip_range = self.ip_entry.get()
            ports_str = self.ports_entry.get()

            # التحقق من صحة المدخلات
            try:
                network = ipaddress.ip_network(ip_range, strict=False)
                ports = [int(p.strip()) for p in ports_str.split(',') if p.strip()]
            except Exception as e:
                raise ValueError(f"خطأ في المدخلات: {e}")

            # تحميل قاعدة بيانات الكاميرات
            cameras_db_path = os.path.join("data", "cameras.json")
            cameras_data = {}
            try:
                with open(cameras_db_path, 'r', encoding='utf-8') as f:
                    cameras_data = json.load(f)
            except:
                pass

            # فحص الشبكة
            results = []
            hosts = list(network.hosts())
            total_hosts = len(hosts)

            for i, host in enumerate(hosts):
                if not hasattr(self, 'scanning') or not self.scanning:
                    break

                host_ip = str(host)

                # تحديث شريط التقدم
                progress = (i + 1) / total_hosts
                self.progress_bar.set(progress)
                self.status_label.configure(text=f"فحص {host_ip}...")
                self.update_idletasks()

                # فحص ping أولاً
                if self.ping_check.get() and not self._ping_host(host_ip):
                    continue

                # فحص المنافذ
                open_ports = []
                for port in ports:
                    if self._check_port(host_ip, port):
                        open_ports.append(port)

                if not open_ports:
                    continue

                # فحص نوع الجهاز
                device_info = self._identify_device(host_ip, open_ports)

                if device_info:
                    result = {
                        "ip": host_ip,
                        "port": open_ports[0],  # المنفذ الأساسي
                        "model": device_info.get("model", "كاميرا IP"),
                        "confidence": device_info.get("confidence", 0.8)
                    }
                    results.append(result)

                    # إضافة النتيجة للجدول
                    self.results_tree.insert(
                        "",
                        "end",
                        values=(
                            result["ip"],
                            result["port"],
                            result["model"],
                            f"{result['confidence']:.2f}"
                        )
                    )

                    self.status_label.configure(text=f"تم العثور على {len(results)} كاميرا")

                # انتظار قصير لتجنب التحميل الزائد
                threading.Event().wait(0.1)

            # انتهاء الفحص
            self.progress_bar.pack_forget()
            self.scan_button.configure(state="normal", text="🔍 بدء فحص الشبكة")
            self.status_label.configure(text=f"انتهى الفحص - تم العثور على {len(results)} كاميرا")
            self.details_button.configure(state="normal")

            # حفظ النتائج
            self.scan_results = results

        except Exception as e:
            self.progress_bar.pack_forget()
            self.scan_button.configure(state="normal", text="🔍 بدء فحص الشبكة")
            self.status_label.configure(text="فشل في الفحص")
            messagebox.showerror("خطأ", f"فشل في فحص الشبكة:\n{str(e)}")

    def _ping_host(self, ip):
        """فحص ping للمضيف"""
        import platform
        import subprocess

        try:
            if platform.system().lower() == "windows":
                cmd = ["ping", "-n", "1", "-w", "1000", ip]
            else:
                cmd = ["ping", "-c", "1", "-W", "1", ip]

            result = subprocess.run(cmd, capture_output=True, timeout=2)
            return result.returncode == 0
        except:
            return False

    def _check_port(self, ip, port):
        """فحص منفذ محدد"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex((ip, port))
            sock.close()
            return result == 0
        except:
            return False

    def _identify_device(self, ip, ports):
        """تحديد نوع الجهاز"""
        # فحص HTTP
        if self.http_check.get():
            device_info = self._check_http_device(ip, ports)
            if device_info:
                return device_info

        # فحص ONVIF
        if self.onvif_check.get():
            device_info = self._check_onvif_device(ip)
            if device_info:
                return device_info

        # إذا لم يتم العثور على معلومات محددة
        return {
            "model": "كاميرا IP عامة",
            "confidence": 0.5
        }

    def _check_http_device(self, ip, ports):
        """فحص جهاز HTTP"""
        http_ports = [p for p in ports if p in [80, 8080, 8000, 8899]]

        for port in http_ports:
            try:
                url = f"http://{ip}:{port}"
                response = requests.get(url, timeout=3, allow_redirects=True)

                content = response.text.lower()
                headers = response.headers

                # البحث عن علامات الكاميرات
                camera_keywords = ['camera', 'webcam', 'ipcam', 'surveillance', 'hikvision', 'dahua', 'axis']

                if any(keyword in content for keyword in camera_keywords):
                    # تحديد الماركة
                    brand = "غير محدد"
                    if 'hikvision' in content:
                        brand = "Hikvision"
                    elif 'dahua' in content:
                        brand = "Dahua"
                    elif 'axis' in content:
                        brand = "Axis"

                    return {
                        "model": f"{brand} كاميرا IP",
                        "confidence": 0.8
                    }

            except:
                continue

        return None

    def _check_onvif_device(self, ip):
        """فحص جهاز ONVIF"""
        # محاكاة فحص ONVIF (يتطلب مكتبة onvif-zeep)
        try:
            # هذا placeholder - يمكن تطبيق ONVIF الحقيقي لاحقاً
            return None
        except:
            return None
            
    def show_details(self):
        """عرض تفاصيل العنصر المحدد"""
        selection = self.results_tree.selection()
        if selection:
            item = self.results_tree.item(selection[0])
            values = item['values']
            
            # إنشاء نافذة التفاصيل
            details_window = ctk.CTkToplevel(self)
            details_window.title("تفاصيل الكاميرا")
            details_window.geometry("400x500")
            
            # محتوى التفاصيل
            details_text = f"""
تفاصيل الكاميرا المكتشفة:

عنوان IP: {values[0]}
المنفذ: {values[1]}
النموذج: {values[2]}
درجة الثقة: {values[3]}

معلومات إضافية:
- نوع الاتصال: شبكي
- البروتوكول: ONVIF/HTTP
- حالة الأمان: يتطلب فحص
- التحديث الأخير: {datetime.now().strftime('%Y/%m/%d %H:%M')}

الثغرات المحتملة:
- كلمة مرور افتراضية
- إصدار قديم من البرنامج الثابت
- تشفير ضعيف
            """
            
            text_widget = ctk.CTkTextbox(details_window)
            text_widget.pack(fill="both", expand=True, padx=20, pady=20)
            text_widget.insert("0.0", details_text)
            text_widget.configure(state="disabled")

class ResultsFrame(ctk.CTkFrame):
    """إطار عرض النتائج"""
    
    def __init__(self, master, on_back: Callable, on_report: Callable):
        super().__init__(master)
        self.on_back = on_back
        self.on_report = on_report
        self.scan_result = None
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # شريط العنوان
        header_frame = ctk.CTkFrame(self)
        header_frame.pack(fill="x", padx=10, pady=(10, 0))
        
        back_button = ArabicButton(
            header_frame,
            text="← رجوع",
            width=80,
            height=30,
            command=self.on_back
        )
        back_button.pack(side="left", padx=10, pady=10)
        
        self.title_label = ArabicLabel(
            header_frame,
            text="نتائج الفحص",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        self.title_label.pack(side="left", padx=20, pady=10)
        
        # منطقة النتائج الرئيسية
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # معلومات الكاميرا المكتشفة
        self.camera_info_frame = ctk.CTkFrame(main_frame)
        self.camera_info_frame.pack(fill="x", padx=15, pady=15)
        
        # أزرار التحكم
        control_frame = ctk.CTkFrame(self)
        control_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        # زر حفظ النتائج
        save_button = ArabicButton(
            control_frame,
            text="💾 حفظ النتائج",
            command=self.save_results
        )
        save_button.pack(side="left", padx=10, pady=10)
        
        # زر إنشاء تقرير
        report_button = ArabicButton(
            control_frame,
            text="📄 إنشاء تقرير PDF",
            command=self.generate_report,
            fg_color="#d63031"
        )
        report_button.pack(side="left", padx=10, pady=10)
        
        # زر مشاركة
        share_button = ArabicButton(
            control_frame,
            text="📤 مشاركة",
            command=self.share_results
        )
        share_button.pack(side="right", padx=10, pady=10)
        
    def set_results(self, scan_type: str, results: Dict[str, Any]):
        """تعيين نتائج الفحص للعرض"""
        self.scan_result = {"type": scan_type, "data": results}
        self.display_results()
        
    def display_results(self):
        """عرض النتائج"""
        if not self.scan_result:
            return
            
        # تنظيف الإطار
        for widget in self.camera_info_frame.winfo_children():
            widget.destroy()
            
        results = self.scan_result["data"]
        scan_type = self.scan_result["type"]
        
        # عنوان النتيجة
        result_title = ArabicLabel(
            self.camera_info_frame,
            text=f"نتيجة الفحص {self._get_scan_type_name(scan_type)}",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        result_title.pack(pady=(15, 10))
        
        # معلومات الكاميرا
        if isinstance(results, dict):
            self._display_single_camera(results)
        elif isinstance(results, list):
            self._display_multiple_cameras(results)
            
    def _get_scan_type_name(self, scan_type: str) -> str:
        """الحصول على اسم نوع الفحص بالعربية"""
        names = {
            "visual": "البصري",
            "network": "الشبكي",
            "direct": "المباشر"
        }
        return names.get(scan_type, scan_type)
        
    def _display_single_camera(self, result: Dict[str, Any]):
        """عرض نتيجة كاميرا واحدة"""
        
        # إطار معلومات الكاميرا
        info_frame = ctk.CTkFrame(self.camera_info_frame)
        info_frame.pack(fill="x", padx=15, pady=10)
        
        # النموذج المكتشف
        model_label = ArabicLabel(
            info_frame,
            text=f"النموذج المكتشف: {result.get('model', 'غير محدد')}",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        model_label.pack(anchor="w", padx=15, pady=(15, 5))
        
        # درجة الثقة
        confidence = result.get('confidence', 0)
        confidence_label = ArabicLabel(
            info_frame,
            text=f"درجة الثقة: {confidence:.2%}",
            font=ctk.CTkFont(size=12)
        )
        confidence_label.pack(anchor="w", padx=15, pady=2)
        
        # شريط الثقة البصري
        confidence_bar = ctk.CTkProgressBar(info_frame)
        confidence_bar.pack(fill="x", padx=15, pady=5)
        confidence_bar.set(confidence)
        
        # معلومات إضافية
        if 'brand' in result:
            brand_label = ArabicLabel(
                info_frame,
                text=f"الشركة المصنعة: {result['brand']}",
                font=ctk.CTkFont(size=12)
            )
            brand_label.pack(anchor="w", padx=15, pady=2)
            
        # الثغرات الأمنية
        if 'vulnerabilities' in result and result['vulnerabilities']:
            vuln_frame = ctk.CTkFrame(info_frame, fg_color=("gray80", "gray20"))
            vuln_frame.pack(fill="x", padx=15, pady=(10, 15))
            
            vuln_title = ArabicLabel(
                vuln_frame,
                text="⚠️ تحذيرات أمنية:",
                font=ctk.CTkFont(size=12, weight="bold"),
                text_color=("red", "orange")
            )
            vuln_title.pack(anchor="w", padx=10, pady=(10, 5))
            
            for vuln in result['vulnerabilities']:
                vuln_label = ArabicLabel(
                    vuln_frame,
                    text=f"• {vuln}",
                    font=ctk.CTkFont(size=11),
                    text_color=("red", "orange")
                )
                vuln_label.pack(anchor="w", padx=20, pady=1)
                
            # توصيات أمنية
            recommendations = [
                "قم بتغيير كلمة المرور الافتراضية",
                "تحقق من توفر تحديثات للبرنامج الثابت",
                "فعّل التشفير إذا كان متوفراً",
                "اعزل الكاميرا عن الإنترنت إذا أمكن"
            ]
            
            rec_label = ArabicLabel(
                vuln_frame,
                text="التوصيات:",
                font=ctk.CTkFont(size=12, weight="bold")
            )
            rec_label.pack(anchor="w", padx=10, pady=(10, 5))
            
            for rec in recommendations:
                rec_item = ArabicLabel(
                    vuln_frame,
                    text=f"✓ {rec}",
                    font=ctk.CTkFont(size=11)
                )
                rec_item.pack(anchor="w", padx=20, pady=1)
                
            vuln_frame.pack_configure(pady=(10, 15))
            
    def _display_multiple_cameras(self, results: list):
        """عرض نتائج متعددة للكاميرات"""
        
        summary_label = ArabicLabel(
            self.camera_info_frame,
            text=f"تم العثور على {len(results)} كاميرا",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        summary_label.pack(pady=10)
        
        # عرض الكاميرات الأولى فقط لتوفير المساحة
        for i, result in enumerate(results[:3]):
            camera_frame = ctk.CTkFrame(self.camera_info_frame)
            camera_frame.pack(fill="x", padx=15, pady=5)
            
            camera_title = ArabicLabel(
                camera_frame,
                text=f"الكاميرا {i+1}: {result.get('model', 'غير محدد')}",
                font=ctk.CTkFont(size=12, weight="bold")
            )
            camera_title.pack(anchor="w", padx=10, pady=5)
            
        if len(results) > 3:
            more_label = ArabicLabel(
                self.camera_info_frame,
                text=f"... و {len(results) - 3} كاميرا أخرى",
                font=ctk.CTkFont(size=11),
                text_color="gray70"
            )
            more_label.pack(pady=5)
            
    def save_results(self):
        """حفظ النتائج في ملف"""
        if not self.scan_result:
            messagebox.showwarning("تحذير", "لا توجد نتائج للحفظ")
            return
            
        file_path = filedialog.asksaveasfilename(
            title="حفظ النتائج",
            defaultextension=".json",
            filetypes=[
                ("ملفات JSON", "*.json"),
                ("جميع الملفات", "*.*")
            ]
        )
        
        if file_path:
            try:
                import json
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.scan_result, f, ensure_ascii=False, indent=2)
                messagebox.showinfo("نجح", "تم حفظ النتائج بنجاح")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ النتائج:\n{str(e)}")
                
    def generate_report(self):
        """إنشاء تقرير PDF"""
        if not self.scan_result:
            messagebox.showwarning("تحذير", "لا توجد نتائج لإنشاء تقرير")
            return
            
        self.on_report(self.scan_result)
        
    def share_results(self):
        """مشاركة النتائج"""
        if not self.scan_result:
            messagebox.showwarning("تحذير", "لا توجد نتائج للمشاركة")
            return
            
        # إنشاء نص موجز للمشاركة
        results = self.scan_result["data"]
        scan_type = self._get_scan_type_name(self.scan_result["type"])
        
        if isinstance(results, dict):
            share_text = f"""
نتيجة فحص الكاميرا - كاميرا آيـدي

نوع الفحص: {scan_type}
النموذج المكتشف: {results.get('model', 'غير محدد')}
درجة الثقة: {results.get('confidence', 0):.2%}
التاريخ: {datetime.now().strftime('%Y/%m/%d %H:%M')}

تم إنشاء هذا التقرير باستخدام تطبيق كاميرا آيـدي
            """
        else:
            share_text = f"""
نتيجة فحص الكاميرات - كاميرا آيـدي

نوع الفحص: {scan_type}
عدد الكاميرات المكتشفة: {len(results)}
التاريخ: {datetime.now().strftime('%Y/%m/%d %H:%M')}

تم إنشاء هذا التقرير باستخدام تطبيق كاميرا آيـدي
            """
            
        # نسخ النص للحافظة
        self.clipboard_clear()
        self.clipboard_append(share_text)
        
        messagebox.showinfo("نجح", "تم نسخ ملخص النتائج إلى الحافظة")

class CameraIDApp(ctk.CTk):
    """التطبيق الرئيسي لكاميرا آيـدي"""
    
    def __init__(self):
        super().__init__()
        
        # تحميل التكوين
        self.config = Config()
        self.logger = setup_logger()
        
        # إعداد النافذة الرئيسية
        self.setup_window()
        self.setup_ui()
        
        # الإطارات
        self.frames = {}
        self.current_frame = None
        
        # إنشاء الإطارات
        self.create_frames()
        
        # عرض الإطار الافتراضي
        self.show_frame("scan_mode")
        
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.title(self.config.app.app_name)
        self.geometry(f"{self.config.gui.window_width}x{self.config.gui.window_height}")
        self.minsize(self.config.gui.min_width, self.config.gui.min_height)
        
        # تعيين أيقونة التطبيق (إذا توفرت)
        # self.iconbitmap("icon.ico")
        
        # إعداد الشبكة
        self.grid_rowconfigure(0, weight=1)
        self.grid_columnconfigure(0, weight=1)
        
    def setup_ui(self):
        """إعداد واجهة المستخدم الأساسية"""
        
        # شريط القائمة العلوي
        self.menubar_frame = ctk.CTkFrame(self, height=50)
        self.menubar_frame.pack(fill="x", padx=5, pady=(5, 0))
        
        # شعار التطبيق
        logo_label = ArabicLabel(
            self.menubar_frame,
            text="🎥 كاميرا آيـدي",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        logo_label.pack(side="left", padx=20, pady=10)
        
        # معلومات الإصدار
        version_label = ArabicLabel(
            self.menubar_frame,
            text=f"الإصدار {self.config.app.version}",
            font=ctk.CTkFont(size=10),
            text_color="gray70"
        )
        version_label.pack(side="left", padx=10, pady=10)
        
        # أزرار التنقل
        nav_frame = ctk.CTkFrame(self.menubar_frame)
        nav_frame.pack(side="right", padx=20, pady=5)
        
        # زر الإعدادات
        settings_button = ArabicButton(
            nav_frame,
            text="⚙️ إعدادات",
            width=80,
            height=30,
            command=self.show_settings
        )
        settings_button.pack(side="right", padx=5)
        
        # زر السجل
        history_button = ArabicButton(
            nav_frame,
            text="📊 السجل",
            width=80,
            height=30,
            command=self.show_history
        )
        history_button.pack(side="right", padx=5)
        
        # الإطار الرئيسي للمحتوى
        self.main_frame = ctk.CTkFrame(self)
        self.main_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
    def create_frames(self):
        """إنشاء إطارات التطبيق المختلفة"""
        
        # إطار اختيار نوع الفحص
        self.frames["scan_mode"] = ScanModeFrame(
            self.main_frame,
            on_mode_select=self.on_scan_mode_select
        )
        
        # إطار الفحص البصري
        self.frames["visual_scan"] = VisualScanFrame(
            self.main_frame,
            on_back=lambda: self.show_frame("scan_mode"),
            on_scan=self.on_scan_complete
        )
        
        # إطار فحص الشبكة
        self.frames["network_scan"] = NetworkScanFrame(
            self.main_frame,
            on_back=lambda: self.show_frame("scan_mode"),
            on_scan=self.on_scan_complete
        )
        
        # إطار النتائج
        self.frames["results"] = ResultsFrame(
            self.main_frame,
            on_back=self.on_results_back,
            on_report=self.generate_pdf_report
        )
        
    def show_frame(self, frame_name: str):
        """عرض إطار معين"""
        # إخفاء الإطار الحالي
        if self.current_frame:
            self.current_frame.pack_forget()
            
        # عرض الإطار الجديد
        if frame_name in self.frames:
            self.frames[frame_name].pack(fill="both", expand=True)
            self.current_frame = self.frames[frame_name]
            
    def on_scan_mode_select(self, mode: str):
        """عند اختيار نوع الفحص"""
        self.logger.info(f"تم اختيار نوع الفحص: {mode}")
        
        if mode == "visual":
            self.show_frame("visual_scan")
        elif mode == "network":
            self.show_frame("network_scan")
        elif mode == "direct":
            messagebox.showinfo("قريباً", "فحص الاتصال المباشر سيكون متاحاً قريباً")
            
    def on_scan_complete(self, scan_type: str, results: Dict[str, Any]):
        """عند انتهاء الفحص"""
        self.logger.info(f"انتهى الفحص من النوع: {scan_type}")
        
        # عرض النتائج
        self.frames["results"].set_results(scan_type, results)
        self.show_frame("results")
        
    def on_results_back(self):
        """الرجوع من صفحة النتائج"""
        self.show_frame("scan_mode")
        
    def generate_pdf_report(self, scan_result: Dict[str, Any]):
        """إنشاء تقرير PDF"""
        try:
            from reportlab.lib import colors
            from reportlab.lib.pagesizes import letter, A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch
            import os
            from datetime import datetime

            # إنشاء اسم الملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"camera_id_report_{timestamp}.pdf"
            filepath = os.path.join("reports", filename)

            # التأكد من وجود مجلد التقارير
            os.makedirs("reports", exist_ok=True)

            # إنشاء مستند PDF
            doc = SimpleDocTemplate(filepath, pagesize=A4)
            styles = getSampleStyleSheet()

            # أنماط مخصصة
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=16,
                spaceAfter=30,
                alignment=1  # وسط
            )

            header_style = ParagraphStyle(
                'CustomHeader',
                parent=styles['Heading2'],
                fontSize=14,
                spaceAfter=20
            )

            normal_style = styles['Normal']

            # محتوى التقرير
            content = []

            # العنوان
            content.append(Paragraph("تقرير فحص كاميرا آيـدي", title_style))
            content.append(Spacer(1, 12))

            # معلومات عامة
            content.append(Paragraph("معلومات الفحص", header_style))
            scan_info = [
                ["نوع الفحص:", self._get_scan_type_name_arabic(scan_result.get("type", ""))],
                ["تاريخ الفحص:", datetime.now().strftime("%Y/%m/%d %H:%M:%S")],
                ["عدد النتائج:", str(len(scan_result.get("data", [])) if isinstance(scan_result.get("data"), list) else "1")]
            ]

            info_table = Table(scan_info, colWidths=[2*inch, 4*inch])
            info_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            content.append(info_table)
            content.append(Spacer(1, 20))

            # النتائج
            content.append(Paragraph("نتائج الفحص", header_style))

            results_data = scan_result.get("data", [])
            if isinstance(results_data, dict):
                results_data = [results_data]

            if results_data:
                # جدول النتائج
                table_data = [["العنوان", "النموذج", "درجة الثقة", "الثغرات"]]

                for result in results_data:
                    ip = result.get("ip", "N/A")
                    model = result.get("model", "غير محدد")
                    confidence = result.get("confidence", 0)
                    vulnerabilities = result.get("vulnerabilities", [])

                    vuln_text = ", ".join(vulnerabilities) if vulnerabilities else "لا توجد"

                    table_data.append([
                        str(ip),
                        str(model),
                        f"{confidence:.2%}",
                        vuln_text
                    ])

                results_table = Table(table_data, colWidths=[1.5*inch, 2*inch, 1*inch, 2*inch])
                results_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                content.append(results_table)
            else:
                content.append(Paragraph("لم يتم العثور على نتائج", normal_style))

            content.append(Spacer(1, 20))

            # توصيات أمنية
            content.append(Paragraph("التوصيات الأمنية", header_style))
            recommendations = [
                "تغيير كلمات المرور الافتراضية لجميع الكاميرات",
                "التأكد من تحديث البرامج الثابتة للكاميرات",
                "تفعيل التشفير في الاتصالات",
                "عزل شبكة الكاميرات عن الشبكة الرئيسية",
                "مراقبة السجلات بانتظام",
                "إجراء فحوصات أمنية دورية"
            ]

            for rec in recommendations:
                content.append(Paragraph(f"• {rec}", normal_style))
                content.append(Spacer(1, 6))

            # إنشاء التقرير
            doc.build(content)

            messagebox.showinfo("نجح", f"تم إنشاء التقرير بنجاح:\n{filepath}")

        except ImportError:
            messagebox.showwarning("تحذير", "مكتبة ReportLab غير مثبتة. يرجى تثبيتها لإنشاء تقارير PDF")
        except Exception as e:
            self.logger.error(f"فشل في إنشاء التقرير: {e}")
            messagebox.showerror("خطأ", f"فشل في إنشاء التقرير:\n{str(e)}")

    def _get_scan_type_name_arabic(self, scan_type: str) -> str:
        """الحصول على اسم نوع الفحص بالعربية"""
        names = {
            "visual": "بصري",
            "network": "شبكي",
            "direct": "مباشر"
        }
        return names.get(scan_type, scan_type)
            
    def show_settings(self):
        """عرض نافذة الإعدادات"""
        settings_window = ctk.CTkToplevel(self)
        settings_window.title("الإعدادات")
        settings_window.geometry("500x400")
        
        # محتوى الإعدادات
        settings_label = ArabicLabel(
            settings_window,
            text="إعدادات التطبيق",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        settings_label.pack(pady=20)
        
        # إعدادات المظهر
        appearance_frame = ctk.CTkFrame(settings_window)
        appearance_frame.pack(fill="x", padx=20, pady=10)
        
        appearance_label = ArabicLabel(
            appearance_frame,
            text="المظهر:",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        appearance_label.pack(anchor="w", padx=15, pady=(15, 5))
        
        appearance_menu = ctk.CTkOptionMenu(
            appearance_frame,
            values=["فاتح", "داكن", "النظام"],
            command=self.change_appearance
        )
        appearance_menu.pack(anchor="w", padx=15, pady=(0, 15))
        appearance_menu.set("داكن")
        
    def change_appearance(self, new_appearance: str):
        """تغيير مظهر التطبيق"""
        appearance_map = {
            "فاتح": "light",
            "داكن": "dark",
            "النظام": "system"
        }
        
        if new_appearance in appearance_map:
            ctk.set_appearance_mode(appearance_map[new_appearance])
            
    def show_history(self):
        """عرض سجل الفحوصات"""
        history_window = ctk.CTkToplevel(self)
        history_window.title("سجل الفحوصات")
        history_window.geometry("600x500")
        
        # محتوى السجل
        history_label = ArabicLabel(
            history_window,
            text="سجل الفحوصات السابقة",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        history_label.pack(pady=20)
