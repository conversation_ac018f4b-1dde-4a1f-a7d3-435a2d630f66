#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التشفير والأمان - Encryption and Security System
"""

import os
import hashlib
import hmac
import secrets
import base64
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
import json
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple
import threading

class SecurityManager:
    """مدير الأمان الرئيسي"""
    
    def __init__(self, config, logger):
        self.config = config
        self.logger = logger
        self.encryption_key = None
        self.session_tokens = {}
        self.failed_attempts = {}
        self.lock = threading.Lock()
        
        # تهيئة النظام
        self.initialize_security()
    
    def initialize_security(self):
        """تهيئة نظام الأمان"""
        try:
            # إنشاء مجلد الأمان
            security_dir = self.config.base_dir / "security"
            security_dir.mkdir(exist_ok=True)
            
            # تحميل أو إنشاء مفتاح التشفير
            self.load_or_create_encryption_key()
            
            # تحميل إعدادات الأمان
            self.load_security_settings()
            
            self.logger.info("تم تهيئة نظام الأمان")
            
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة نظام الأمان: {e}")
            raise
    
    def load_or_create_encryption_key(self):
        """تحميل أو إنشاء مفتاح التشفير"""
        try:
            key_file = self.config.base_dir / "security" / "encryption.key"
            
            if key_file.exists():
                # تحميل المفتاح الموجود
                with open(key_file, 'rb') as f:
                    self.encryption_key = f.read()
                self.logger.info("تم تحميل مفتاح التشفير")
            else:
                # إنشاء مفتاح جديد
                self.encryption_key = Fernet.generate_key()
                
                # حفظ المفتاح
                with open(key_file, 'wb') as f:
                    f.write(self.encryption_key)
                
                # تعيين صلاحيات الملف (قراءة فقط للمالك)
                os.chmod(key_file, 0o600)
                
                self.logger.info("تم إنشاء مفتاح تشفير جديد")
                
        except Exception as e:
            self.logger.error(f"خطأ في تحميل مفتاح التشفير: {e}")
            raise
    
    def load_security_settings(self):
        """تحميل إعدادات الأمان"""
        try:
            settings_file = self.config.base_dir / "security" / "settings.json"
            
            default_settings = {
                "max_login_attempts": 5,
                "lockout_duration": 300,  # 5 دقائق
                "session_timeout": 3600,  # ساعة واحدة
                "password_min_length": 8,
                "require_special_chars": True,
                "enable_2fa": False,
                "log_security_events": True
            }
            
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)
                    default_settings.update(loaded_settings)
            
            self.security_settings = default_settings
            
            # حفظ الإعدادات المحدثة
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(default_settings, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            self.logger.error(f"خطأ في تحميل إعدادات الأمان: {e}")
            self.security_settings = {}

class DataEncryption:
    """فئة تشفير البيانات"""
    
    def __init__(self, encryption_key: bytes):
        self.fernet = Fernet(encryption_key)
        self.key = encryption_key
    
    def encrypt_data(self, data: str) -> str:
        """تشفير البيانات النصية"""
        try:
            encrypted_data = self.fernet.encrypt(data.encode('utf-8'))
            return base64.b64encode(encrypted_data).decode('utf-8')
        except Exception as e:
            raise Exception(f"خطأ في تشفير البيانات: {e}")
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """فك تشفير البيانات"""
        try:
            encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))
            decrypted_data = self.fernet.decrypt(encrypted_bytes)
            return decrypted_data.decode('utf-8')
        except Exception as e:
            raise Exception(f"خطأ في فك التشفير: {e}")
    
    def encrypt_file(self, file_path: str, output_path: str) -> bool:
        """تشفير ملف"""
        try:
            with open(file_path, 'rb') as f:
                file_data = f.read()
            
            encrypted_data = self.fernet.encrypt(file_data)
            
            with open(output_path, 'wb') as f:
                f.write(encrypted_data)
            
            return True
        except Exception as e:
            raise Exception(f"خطأ في تشفير الملف: {e}")
    
    def decrypt_file(self, encrypted_file_path: str, output_path: str) -> bool:
        """فك تشفير ملف"""
        try:
            with open(encrypted_file_path, 'rb') as f:
                encrypted_data = f.read()
            
            decrypted_data = self.fernet.decrypt(encrypted_data)
            
            with open(output_path, 'wb') as f:
                f.write(decrypted_data)
            
            return True
        except Exception as e:
            raise Exception(f"خطأ في فك تشفير الملف: {e}")

class PasswordManager:
    """مدير كلمات المرور"""
    
    @staticmethod
    def hash_password(password: str, salt: Optional[bytes] = None) -> Tuple[str, str]:
        """تشفير كلمة المرور"""
        if salt is None:
            salt = secrets.token_bytes(32)
        
        # استخدام PBKDF2 مع SHA-256
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        
        key = kdf.derive(password.encode('utf-8'))
        
        # إرجاع الهاش والملح مُرمزين بـ base64
        return (
            base64.b64encode(key).decode('utf-8'),
            base64.b64encode(salt).decode('utf-8')
        )
    
    @staticmethod
    def verify_password(password: str, hashed_password: str, salt: str) -> bool:
        """التحقق من كلمة المرور"""
        try:
            salt_bytes = base64.b64decode(salt.encode('utf-8'))
            stored_hash = base64.b64decode(hashed_password.encode('utf-8'))
            
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt_bytes,
                iterations=100000,
            )
            
            # التحقق من كلمة المرور
            kdf.verify(password.encode('utf-8'), stored_hash)
            return True
            
        except Exception:
            return False
    
    @staticmethod
    def validate_password_strength(password: str) -> Dict[str, Any]:
        """التحقق من قوة كلمة المرور"""
        result = {
            "is_strong": True,
            "score": 0,
            "issues": []
        }
        
        # طول كلمة المرور
        if len(password) < 8:
            result["issues"].append("كلمة المرور قصيرة جداً (أقل من 8 أحرف)")
            result["is_strong"] = False
        else:
            result["score"] += 1
        
        # وجود أحرف كبيرة
        if not any(c.isupper() for c in password):
            result["issues"].append("يجب أن تحتوي على أحرف كبيرة")
            result["is_strong"] = False
        else:
            result["score"] += 1
        
        # وجود أحرف صغيرة
        if not any(c.islower() for c in password):
            result["issues"].append("يجب أن تحتوي على أحرف صغيرة")
            result["is_strong"] = False
        else:
            result["score"] += 1
        
        # وجود أرقام
        if not any(c.isdigit() for c in password):
            result["issues"].append("يجب أن تحتوي على أرقام")
            result["is_strong"] = False
        else:
            result["score"] += 1
        
        # وجود رموز خاصة
        special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
        if not any(c in special_chars for c in password):
            result["issues"].append("يجب أن تحتوي على رموز خاصة")
            result["is_strong"] = False
        else:
            result["score"] += 1
        
        # تقييم القوة
        if result["score"] >= 4:
            result["strength"] = "قوية"
        elif result["score"] >= 3:
            result["strength"] = "متوسطة"
        else:
            result["strength"] = "ضعيفة"
        
        return result

class SessionManager:
    """مدير الجلسات"""
    
    def __init__(self, security_manager):
        self.security_manager = security_manager
        self.sessions = {}
        self.lock = threading.Lock()
    
    def create_session(self, user_id: str, user_data: Dict[str, Any]) -> str:
        """إنشاء جلسة جديدة"""
        try:
            with self.lock:
                # إنشاء رمز الجلسة
                session_token = secrets.token_urlsafe(32)
                
                # بيانات الجلسة
                session_data = {
                    "user_id": user_id,
                    "user_data": user_data,
                    "created_at": datetime.now(),
                    "last_activity": datetime.now(),
                    "ip_address": "127.0.0.1",  # يمكن تحديثه حسب الحاجة
                    "user_agent": "CameraID Desktop App"
                }
                
                self.sessions[session_token] = session_data
                
                # تنظيف الجلسات المنتهية الصلاحية
                self.cleanup_expired_sessions()
                
                return session_token
                
        except Exception as e:
            raise Exception(f"خطأ في إنشاء الجلسة: {e}")
    
    def validate_session(self, session_token: str) -> Optional[Dict[str, Any]]:
        """التحقق من صحة الجلسة"""
        try:
            with self.lock:
                if session_token not in self.sessions:
                    return None
                
                session_data = self.sessions[session_token]
                
                # التحقق من انتهاء الصلاحية
                timeout = self.security_manager.security_settings.get("session_timeout", 3600)
                if (datetime.now() - session_data["last_activity"]).seconds > timeout:
                    del self.sessions[session_token]
                    return None
                
                # تحديث آخر نشاط
                session_data["last_activity"] = datetime.now()
                
                return session_data
                
        except Exception as e:
            self.security_manager.logger.error(f"خطأ في التحقق من الجلسة: {e}")
            return None
    
    def destroy_session(self, session_token: str) -> bool:
        """إنهاء الجلسة"""
        try:
            with self.lock:
                if session_token in self.sessions:
                    del self.sessions[session_token]
                    return True
                return False
                
        except Exception as e:
            self.security_manager.logger.error(f"خطأ في إنهاء الجلسة: {e}")
            return False
    
    def cleanup_expired_sessions(self):
        """تنظيف الجلسات المنتهية الصلاحية"""
        try:
            timeout = self.security_manager.security_settings.get("session_timeout", 3600)
            current_time = datetime.now()
            
            expired_sessions = []
            for token, session_data in self.sessions.items():
                if (current_time - session_data["last_activity"]).seconds > timeout:
                    expired_sessions.append(token)
            
            for token in expired_sessions:
                del self.sessions[token]
                
        except Exception as e:
            self.security_manager.logger.error(f"خطأ في تنظيف الجلسات: {e}")

class SecurityAuditor:
    """مدقق الأمان"""
    
    def __init__(self, config, logger):
        self.config = config
        self.logger = logger
        self.audit_log = []
    
    def log_security_event(self, event_type: str, details: Dict[str, Any]):
        """تسجيل حدث أمني"""
        try:
            event = {
                "timestamp": datetime.now().isoformat(),
                "type": event_type,
                "details": details,
                "severity": self.get_event_severity(event_type)
            }
            
            self.audit_log.append(event)
            
            # حفظ في ملف السجل
            self.save_audit_log(event)
            
            # تنبيه في حالة الأحداث الحرجة
            if event["severity"] == "critical":
                self.alert_critical_event(event)
                
        except Exception as e:
            self.logger.error(f"خطأ في تسجيل الحدث الأمني: {e}")
    
    def get_event_severity(self, event_type: str) -> str:
        """تحديد خطورة الحدث"""
        critical_events = ["unauthorized_access", "data_breach", "system_compromise"]
        high_events = ["failed_login_attempts", "privilege_escalation"]
        medium_events = ["password_change", "session_timeout"]
        
        if event_type in critical_events:
            return "critical"
        elif event_type in high_events:
            return "high"
        elif event_type in medium_events:
            return "medium"
        else:
            return "low"
    
    def save_audit_log(self, event: Dict[str, Any]):
        """حفظ سجل التدقيق"""
        try:
            audit_file = self.config.logs_dir / "security_audit.log"
            
            with open(audit_file, 'a', encoding='utf-8') as f:
                f.write(f"{json.dumps(event, ensure_ascii=False)}\n")
                
        except Exception as e:
            self.logger.error(f"خطأ في حفظ سجل التدقيق: {e}")
    
    def alert_critical_event(self, event: Dict[str, Any]):
        """تنبيه للأحداث الحرجة"""
        try:
            self.logger.critical(f"حدث أمني حرج: {event['type']} - {event['details']}")
            
            # يمكن إضافة تنبيهات إضافية هنا مثل:
            # - إرسال إيميل
            # - إشعار سطح المكتب
            # - إيقاف النظام مؤقتاً
            
        except Exception as e:
            self.logger.error(f"خطأ في تنبيه الحدث الحرج: {e}")
    
    def generate_security_report(self) -> Dict[str, Any]:
        """إنشاء تقرير أمني"""
        try:
            report = {
                "generated_at": datetime.now().isoformat(),
                "total_events": len(self.audit_log),
                "events_by_severity": {},
                "events_by_type": {},
                "recent_events": self.audit_log[-10:] if self.audit_log else []
            }
            
            # تجميع الأحداث حسب الخطورة
            for event in self.audit_log:
                severity = event["severity"]
                report["events_by_severity"][severity] = report["events_by_severity"].get(severity, 0) + 1
            
            # تجميع الأحداث حسب النوع
            for event in self.audit_log:
                event_type = event["type"]
                report["events_by_type"][event_type] = report["events_by_type"].get(event_type, 0) + 1
            
            return report
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء التقرير الأمني: {e}")
            return {}

class PrivacyManager:
    """مدير الخصوصية"""
    
    def __init__(self, config, logger):
        self.config = config
        self.logger = logger
    
    def anonymize_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """إخفاء هوية البيانات"""
        try:
            anonymized = data.copy()
            
            # إزالة المعلومات الشخصية
            sensitive_fields = ["ip_address", "mac_address", "user_id", "session_id"]
            
            for field in sensitive_fields:
                if field in anonymized:
                    anonymized[field] = self.hash_sensitive_data(str(anonymized[field]))
            
            # إزالة الطوابع الزمنية الدقيقة
            if "timestamp" in anonymized:
                timestamp = datetime.fromisoformat(anonymized["timestamp"])
                # تقريب إلى أقرب ساعة
                rounded_timestamp = timestamp.replace(minute=0, second=0, microsecond=0)
                anonymized["timestamp"] = rounded_timestamp.isoformat()
            
            return anonymized
            
        except Exception as e:
            self.logger.error(f"خطأ في إخفاء هوية البيانات: {e}")
            return data
    
    def hash_sensitive_data(self, data: str) -> str:
        """تشفير البيانات الحساسة"""
        return hashlib.sha256(data.encode('utf-8')).hexdigest()[:8]
    
    def check_data_retention_policy(self):
        """فحص سياسة الاحتفاظ بالبيانات"""
        try:
            retention_days = self.config.get("privacy.data_retention_days", 90)
            
            # تنظيف البيانات القديمة
            cutoff_date = datetime.now() - timedelta(days=retention_days)
            
            # يمكن إضافة منطق تنظيف البيانات هنا
            self.logger.info(f"فحص سياسة الاحتفاظ بالبيانات: {retention_days} يوم")
            
        except Exception as e:
            self.logger.error(f"خطأ في فحص سياسة الاحتفاظ: {e}")
